<!-- 图片对比组件 -->
{# 使用方法：
1. 包含此文件：{% include 'components/compare_widget.html' %}
2. 传入参数：
   - before_image: 对比前图片URL（必需）
   - after_image: 对比后图片URL（必需）
   - before_label: 对比前标签文本（可选，默认为'原图'）
   - after_label: 对比后标签文本（可选，默认为'处理后'）
#}
<div class="bfl-compare">
    <div class="bfl-compare__container">
        <div class="bfl-compare__before" style="background-image: url('{{ before_image }}')"></div>
        <div class="bfl-compare__after" style="background-image: url('{{ after_image }}')"></div>
        <div class="bfl-compare__divider">
            <div class="bfl-compare__handle"></div>
        </div>
        <div class="bfl-compare__labels">
            <span class="bfl-compare__label bfl-compare__label--before">{{ before_label|default('原图') }}</span>
            <span class="bfl-compare__label bfl-compare__label--after">{{ after_label|default('处理后') }}</span>
        </div>
    </div>
    <div class="bfl-compare__actions">
        <a href="{{ before_image }}" class="bfl-compare__btn" download>
            <i class="fas fa-download"></i> 下载原图
        </a>
        <a href="{{ after_image }}" class="bfl-compare__btn" download>
            <i class="fas fa-download"></i> 下载处理后图像
        </a>
    </div>
</div>

<style>
.bfl-compare {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.bfl-compare__container {
    position: relative;
    width: 100%;
    min-height: 200px; /* 最小高度，防止图片加载前塌陷 */
    background: #f0f0f0;
    border-radius: 12px;
    overflow: hidden;
    user-select: none;
    touch-action: none;
}

.bfl-compare__before,
.bfl-compare__after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #f0f0f0;
}

.bfl-compare__after {
    clip-path: inset(0 50% 0 0);
}

.bfl-compare__divider {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: white;
    transform: translateX(-50%);
    cursor: col-resize;
}

.bfl-compare__handle {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}

.bfl-compare__handle::after {
    content: '⟷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666;
    font-size: 20px;
}

.bfl-compare__labels {
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.bfl-compare__label {
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.bfl-compare__actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
}

.bfl-compare__btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: #4a90e2;
    color: white;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.bfl-compare__btn:hover {
    background: #357abd;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .bfl-compare__labels {
        top: 10px;
        padding: 0 10px;
    }
    
    .bfl-compare__label {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .bfl-compare__handle {
        width: 32px;
        height: 32px;
    }
}
</style>

<script>
(function() {
    const containers = document.querySelectorAll('.bfl-compare__container');
    
    containers.forEach(container => {
        const divider = container.querySelector('.bfl-compare__divider');
        const afterImage = container.querySelector('.bfl-compare__after');
        const beforeImage = container.querySelector('.bfl-compare__before');
        
        let isDragging = false;
        
        // 设置容器高度以匹配图片宽高比
        function setContainerHeight() {
            // 创建一个临时图片来获取实际尺寸
            const img = new Image();
            img.src = beforeImage.style.backgroundImage.slice(5, -2); // 移除 url('...')
            
            img.onload = function() {
                const ratio = img.height / img.width;
                const containerWidth = container.offsetWidth;
                container.style.height = containerWidth * ratio + 'px';
            };
        }

        // 监听背景图片变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    // 当背景图片URL改变时，调整容器高度
                    setContainerHeight();
                }
            });
        });

        // 观察两个图片容器的style属性变化
        observer.observe(beforeImage, { attributes: true });
        observer.observe(afterImage, { attributes: true });
        
        // 监听窗口大小变化
        window.addEventListener('resize', setContainerHeight);
        
        // 初始设置
        if (beforeImage.style.backgroundImage) {
            setContainerHeight();
        }
        
        // 更新分割线位置
        function updateDividerPosition(clientX) {
            const rect = container.getBoundingClientRect();
            const x = Math.min(Math.max(0, clientX - rect.left), rect.width);
            const percentage = (x / rect.width) * 100;
            
            divider.style.left = `${percentage}%`;
            afterImage.style.clipPath = `inset(0 ${100 - percentage}% 0 0)`;
        }
        
        // 鼠标事件处理
        function handleMouseDown(e) {
            isDragging = true;
            updateDividerPosition(e.clientX);
        }
        
        function handleMouseMove(e) {
            if (!isDragging) return;
            updateDividerPosition(e.clientX);
        }
        
        function handleMouseUp() {
            isDragging = false;
        }
        
        // 触摸事件处理
        function handleTouchStart(e) {
            isDragging = true;
            updateDividerPosition(e.touches[0].clientX);
        }
        
        function handleTouchMove(e) {
            if (!isDragging) return;
            updateDividerPosition(e.touches[0].clientX);
        }
        
        // 绑定事件
        divider.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        
        divider.addEventListener('touchstart', handleTouchStart);
        document.addEventListener('touchmove', handleTouchMove);
        document.addEventListener('touchend', handleMouseUp);
        
        // 清理函数
        function cleanup() {
            observer.disconnect();
            window.removeEventListener('resize', setContainerHeight);
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', handleMouseUp);
        }
        
        // 存储清理函数
        container.cleanup = cleanup;
    });
})();
</script>