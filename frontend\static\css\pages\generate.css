/* 生成页面专属样式 - Version 2.1 */
/* 强制刷新缓存标识符：cache-bust-v2 */

/* 生成页面特定功能样式 */

/* 风格按钮样式 - 继承基础按钮样式 */
.style-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

/* 智能生成按钮样式 - 只保留特定样式 */
#smartGenerateBtn {
    background: var(--primary-bg);
    color: white;
}

/* 润色按钮样式 - 只保留特定样式 */
#polishBtn {
    background: var(--success-bg);
    color: white;
}

/* 超级润色开关样式 */
.super-polish-switch {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

/* 示例提示词网格布局 */
.prompt-examples {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.example-prompt {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .prompt-examples {
        grid-template-columns: 1fr;
    }
} 