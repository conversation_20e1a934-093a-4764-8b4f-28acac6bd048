{% extends "base.html" %}

{% block title %}系统设置 - 管理员面板{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-cog"></i> 系统设置</h2>
                <div class="btn-group">
                    <a href="{{ url_for('pages.admin_dashboard_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回仪表板
                    </a>
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save"></i> 保存所有设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置分类 -->
    <div class="row">
        <div class="col-md-3">
            <div class="list-group" id="settingsNav">
                <a class="list-group-item list-group-item-action active" data-category="general">
                    <i class="fas fa-cogs"></i> 基本设置
                </a>
                <a class="list-group-item list-group-item-action" data-category="credits">
                    <i class="fas fa-coins"></i> 积分设置
                </a>
                <a class="list-group-item list-group-item-action" data-category="limits">
                    <i class="fas fa-shield-alt"></i> 限制设置
                </a>
                <a class="list-group-item list-group-item-action" data-category="email">
                    <i class="fas fa-envelope"></i> 邮件设置
                </a>
                <a class="list-group-item list-group-item-action" data-category="payment">
                    <i class="fas fa-credit-card"></i> 支付设置
                </a>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 id="settingsCategoryTitle">基本设置</h5>
                </div>
                <div class="card-body">
                    <div id="settingsContent">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> 加载设置中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 设置模板 -->
<div id="settingsTemplates" style="display: none;">
    <!-- 基本设置模板 -->
    <div id="generalSettings">
        <div class="mb-3">
            <label class="form-label">网站名称</label>
            <input type="text" class="form-control" data-setting="site_name" placeholder="BFL AI 图像生成器">
        </div>
        <div class="mb-3">
            <label class="form-label">网站描述</label>
            <textarea class="form-control" data-setting="site_description" rows="3" placeholder="AI驱动的图像生成平台"></textarea>
        </div>
        <div class="mb-3">
            <label class="form-label">维护模式</label>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" data-setting="maintenance_mode">
                <label class="form-check-label">启用维护模式</label>
            </div>
        </div>
        <div class="mb-3">
            <label class="form-label">用户注册</label>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" data-setting="allow_registration" checked>
                <label class="form-check-label">允许新用户注册</label>
            </div>
        </div>
    </div>

    <!-- 积分设置模板 -->
    <div id="creditsSettings">
        <div class="mb-3">
            <label class="form-label">新用户默认积分</label>
            <input type="number" class="form-control" data-setting="default_credits" value="10" min="0">
        </div>
        <div class="mb-3">
            <label class="form-label">每张图片消耗积分</label>
            <input type="number" class="form-control" data-setting="credits_per_image" value="1" min="1">
        </div>
        <div class="mb-3">
            <label class="form-label">积分购买价格 (元/积分)</label>
            <input type="number" class="form-control" data-setting="credit_price" value="0.1" step="0.01" min="0">
        </div>
        <div class="mb-3">
            <label class="form-label">最小购买积分数</label>
            <input type="number" class="form-control" data-setting="min_credit_purchase" value="10" min="1">
        </div>
    </div>

    <!-- 限制设置模板 -->
    <div id="limitsSettings">
        <div class="mb-3">
            <label class="form-label">免费用户每日限制</label>
            <input type="number" class="form-control" data-setting="free_daily_limit" value="5" min="0">
        </div>
        <div class="mb-3">
            <label class="form-label">付费用户每日限制</label>
            <input type="number" class="form-control" data-setting="premium_daily_limit" value="50" min="0">
        </div>
        <div class="mb-3">
            <label class="form-label">Pro用户每日限制</label>
            <input type="number" class="form-control" data-setting="pro_daily_limit" value="200" min="0">
        </div>
        <div class="mb-3">
            <label class="form-label">最大图片尺寸</label>
            <select class="form-select" data-setting="max_image_size">
                <option value="512">512x512</option>
                <option value="768">768x768</option>
                <option value="1024" selected>1024x1024</option>
                <option value="1536">1536x1536</option>
            </select>
        </div>
    </div>

    <!-- 邮件设置模板 -->
    <div id="emailSettings">
        <div class="mb-3">
            <label class="form-label">SMTP服务器</label>
            <input type="text" class="form-control" data-setting="smtp_server" placeholder="smtp.gmail.com">
        </div>
        <div class="mb-3">
            <label class="form-label">SMTP端口</label>
            <input type="number" class="form-control" data-setting="smtp_port" value="587">
        </div>
        <div class="mb-3">
            <label class="form-label">发件人邮箱</label>
            <input type="email" class="form-control" data-setting="smtp_username" placeholder="<EMAIL>">
        </div>
        <div class="mb-3">
            <label class="form-label">邮箱密码</label>
            <input type="password" class="form-control" data-setting="smtp_password">
        </div>
        <div class="mb-3">
            <label class="form-label">启用TLS</label>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" data-setting="smtp_tls" checked>
                <label class="form-check-label">使用TLS加密</label>
            </div>
        </div>
    </div>

    <!-- 支付设置模板 -->
    <div id="paymentSettings">
        <div class="mb-3">
            <label class="form-label">支付宝商户ID</label>
            <input type="text" class="form-control" data-setting="alipay_merchant_id">
        </div>
        <div class="mb-3">
            <label class="form-label">微信支付商户号</label>
            <input type="text" class="form-control" data-setting="wechat_merchant_id">
        </div>
        <div class="mb-3">
            <label class="form-label">启用支付功能</label>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" data-setting="payment_enabled">
                <label class="form-check-label">允许用户购买积分</label>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 版本: 2025-07-13-17:15 - 强制刷新缓存
console.log('系统设置页面加载 - 版本: 2025-07-13-17:15');
let currentCategory = 'general';
let settings = {};

$(document).ready(function() {
    // 检查管理员权限
    checkAdminAuth();
    
    // 加载设置
    loadSettings();
    
    // 绑定分类切换事件
    $('#settingsNav .list-group-item').on('click', function(e) {
        e.preventDefault();
        const category = $(this).data('category');
        switchCategory(category);
    });
});

function checkAdminAuth() {
    const token = getToken();
    
    if (!token) {
        showError('请先登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
        return;
    }
    
    // 验证管理员权限
    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success && response.user) {
                if (response.user.user_type !== 'enterprise') {
                    showError('需要管理员权限访问此页面');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } else {
                showError('获取用户信息失败');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            }
        },
        error: function() {
            showError('认证失败，请重新登录');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    });
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;
    
    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function loadSettings() {
    const token = getToken();

    console.log('正在加载系统设置，API路径: /api/admin/system-settings');

    $.ajax({
        url: '/api/admin/system-settings',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            console.log('系统设置加载成功:', response);
            if (response.success) {
                settings = response.settings;
                switchCategory(currentCategory);
            } else {
                showError('加载设置失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('加载设置失败:', xhr.responseText);
            console.error('状态码:', xhr.status);
            console.error('响应文本:', xhr.responseText);
            showError('无法连接到服务器');
        }
    });
}

function switchCategory(category) {
    currentCategory = category;
    
    // 更新导航状态
    $('#settingsNav .list-group-item').removeClass('active');
    $(`#settingsNav .list-group-item[data-category="${category}"]`).addClass('active');
    
    // 更新标题
    const titles = {
        'general': '基本设置',
        'credits': '积分设置',
        'limits': '限制设置',
        'email': '邮件设置',
        'payment': '支付设置'
    };
    $('#settingsCategoryTitle').text(titles[category]);
    
    // 加载设置内容
    const template = $(`#${category}Settings`).html();
    $('#settingsContent').html(template);
    
    // 填充设置值
    populateSettings();
}

function populateSettings() {
    $('#settingsContent [data-setting]').each(function() {
        const settingKey = $(this).data('setting');
        const settingValue = settings[settingKey];
        
        if ($(this).is(':checkbox')) {
            $(this).prop('checked', settingValue === 'true' || settingValue === true);
        } else {
            $(this).val(settingValue || '');
        }
    });
}

function saveAllSettings() {
    const token = getToken();
    const updatedSettings = {};
    
    // 收集所有设置
    $('#settingsContent [data-setting]').each(function() {
        const settingKey = $(this).data('setting');
        let settingValue;
        
        if ($(this).is(':checkbox')) {
            settingValue = $(this).is(':checked');
        } else {
            settingValue = $(this).val();
        }
        
        updatedSettings[settingKey] = settingValue;
    });
    
    $.ajax({
        url: '/api/admin/system-settings',
        method: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(updatedSettings),
        success: function(response) {
            if (response.success) {
                showSuccess('设置保存成功');
                settings = {...settings, ...updatedSettings};
            } else {
                showError('保存设置失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('保存设置失败:', xhr.responseText);
            showError('保存设置失败，请重试');
        }
    });
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
