body {
    background: var(--light-bg, linear-gradient(135deg, #ffffff 0%, #f1f5f9 50%, #e2e8f0 100%)) !important;
    background-attachment: fixed !important;
    min-height: 100vh;
    font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif !important;
    color: var(--dark-color) !important;
}

/* 添加细微的纹理效果 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 50%, rgba(14, 165, 233, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(16, 185, 129, 0.04) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}



.navbar {
    background: var(--card-bg) !important;
    backdrop-filter: blur(20px);
    box-shadow: 0 1px 3px var(--shadow-light), 0 1px 2px var(--shadow-medium);
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.navbar-brand {
    font-weight: 700;
    background: var(--primary-gradient) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-size: 1.25rem;
    /* 备用方案：如果渐变文字不支持 */
    color: var(--primary-color) !important;
}

/* 对于不支持渐变文字的浏览器 */
@supports not (-webkit-background-clip: text) {
    .navbar-brand {
        color: var(--primary-color) !important;
        background: none !important;
    }
}

.nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.main-content {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px var(--shadow-light), 0 1px 3px var(--shadow-medium);
    backdrop-filter: blur(20px);
    background: var(--card-bg);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 10px 15px var(--shadow-light), 0 4px 6px var(--shadow-medium);
}

.card-header {
    background: var(--primary-gradient) !important;
    color: white !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border: none !important;
    font-weight: 600;
    padding: 1.25rem 1.5rem;
}

.card-body {
    background: var(--card-bg) !important;
    padding: 1.2rem;
}

.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 12px 28px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 24px var(--shadow-primary), 0 4px 8px var(--shadow-medium) !important;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow-primary);
}

.btn-success {
    background: var(--success-color);
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
}

.btn-outline-success {
    color: var(--success-color);
    border: 2px solid var(--success-color);
    border-radius: var(--border-radius-sm);
}

.form-control, .form-select {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e2e8f0;
    padding: 14px 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background: white;
}

.alert {
    border-radius: var(--border-radius-sm);
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
    color: #0c4a6e;
    border-left: 4px solid var(--accent-color);
}

.progress {
    height: 10px;
    border-radius: var(--border-radius-sm);
    background-color: rgba(99, 102, 241, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: var(--primary-gradient);
    border-radius: var(--border-radius-sm);
}

.image-preview {
    max-width: 100%;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.task-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.task-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
}

.status-badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.status-queued { 
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); 
    color: #000; 
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}
.status-processing { 
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); 
    color: #fff; 
    box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}
.status-completed { 
    background: linear-gradient(135deg, #10b981 0%, #059669 100%); 
    color: #fff; 
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}
.status-failed { 
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); 
    color: #fff; 
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.footer {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}

/* 添加现代化的图标和装饰 */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

/* 改善焦点状态 */
.btn:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* 添加微妙的动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* 改善表单元素 */
.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.75rem;
}

.form-text {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 现代化的模态框 */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(99, 102, 241, 0.1);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(99, 102, 241, 0.1);
    padding: 1.5rem;
}

.toast-notification.toast-info {
    border-left-color: #3b82f6; /* Blue */
}
.toast-notification.toast-info::before {
    content: "\f05a"; /* FontAwesome info-circle */
}

/* ==========================================================================
   Modern File Uploader Component
   ========================================================================== */

.modern-upload-area {
    position: relative;
    border: 2px dashed #e2e8f0;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    overflow: hidden;
    min-height: 180px; /* Adjusted for consistency */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
}

.modern-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(239, 246, 255, 0.05); /* A light transparent hover */
}

.modern-upload-area.drag-over {
    border-color: var(--primary-color) !important;
    background: rgba(219, 234, 254, 0.2) !important; /* A light transparent dragover */
    transform: scale(1.02);
}

.modern-upload-area .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    width: 100%;
    height: 100%;
}

.modern-upload-area .upload-content i {
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.modern-upload-area:hover .upload-content i {
    transform: scale(1.1);
}

.modern-upload-area .upload-preview {
    /* display: none; is handled by JS */
    width: 100%;
    height: 100%;
    position: relative;
}

.modern-upload-area .preview-image {
    width: 100%;
    height: 100%;
    max-height: 150px;
    object-fit: contain;
    border-radius: var(--border-radius-sm);
}

.modern-upload-area .file-name {
    word-break: break-all;
    font-size: 0.9em;
    text-align: center;
}

/* Specific button styles for the preview overlay */
.upload-preview .btn-group {
    margin-top: 0.5rem;
}