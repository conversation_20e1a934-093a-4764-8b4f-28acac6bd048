{% extends "base.html" %}

{% block title %}用户仪表板 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 欢迎横幅 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4><i class="fas fa-tachometer-alt"></i> <span id="welcomeMessage">欢迎回来！</span></h4>
                            <p class="mb-0">您的账户类型：<span class="badge bg-light text-dark" id="userType">加载中...</span></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="btn-group">
                                <a href="{{ url_for('pages.profile_page') }}" class="btn btn-light">
                    <i class="fas fa-user-edit"></i> 个人中心
                </a>
                <a href="{{ url_for('pages.credits_store_page') }}" class="btn btn-light">
                    <i class="fas fa-coins"></i> 积分管理
                </a>
                <a href="{{ url_for('pages.admin_dashboard_page') }}" class="btn btn-light" id="adminDashboardBtn" style="display: none;">
                    <i class="fas fa-cog"></i> 管理面板
                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分和使用统计 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                    <h5 class="card-title">可用积分</h5>
                    <h3 class="text-warning" id="availableCredits">-</h3>
                    <small class="text-muted">总积分: <span id="totalCredits">-</span></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                    <h5 class="card-title">已使用积分</h5>
                    <h3 class="text-info" id="usedCredits">-</h3>
                    <small class="text-muted">使用率: <span id="usageRate">-</span>%</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-day fa-2x text-success mb-2"></i>
                    <h5 class="card-title">今日剩余</h5>
                    <h3 class="text-success" id="dailyRemaining">-</h3>
                    <small class="text-muted">每日限制: <span id="dailyLimit">-</span></small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                    <h5 class="card-title">总任务数</h5>
                    <h3 class="text-primary" id="totalTasks">0</h3>
                    <small class="text-muted">历史任务</small>
                    <div id="taskStatsDetail">
                        <!-- 任务统计详情将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> 快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('pages.generate_page') }}" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-paint-brush fa-2x mb-2"></i>
                                <span>图像生成</span>
                                <small class="text-light">消耗 1-3 积分</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('pages.edit_page') }}" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-edit fa-2x mb-2"></i>
                                <span>图像编辑</span>
                                <small class="text-light">消耗 2-4 积分</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('pages.style_page') }}" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-palette fa-2x mb-2"></i>
                                <span>风格迁移</span>
                                <small class="text-light">消耗 3-6 积分</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('pages.gallery') }}" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center">
                                <i class="fas fa-images fa-2x mb-2"></i>
                                <span>我的作品</span>
                                <small class="text-light">查看历史</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近任务 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> 最近任务</h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshTasks()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="recentTasks">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> 正在加载...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分使用统计图表 -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> 积分使用分布</h5>
                </div>
                <div class="card-body">
                    <canvas id="creditUsageChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> 每日使用趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyUsageChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    console.log('🚀 仪表板页面加载完成');

    // 添加一个小延迟，确保DOM完全加载
    setTimeout(function() {
        console.log('🔄 延迟执行，开始检查认证状态...');
        // 检查认证状态
        checkAuthAndLoadData();
    }, 100); // 100ms延迟
});

function checkAuthAndLoadData() {
    console.log('🔍 开始检查认证状态...');
    
    // 使用session-based认证，直接检查认证状态
    $.ajax({
        url: '/api/auth/status',
        method: 'GET',
        credentials: 'include',  // 包含session cookie
        success: function(response) {
            console.log('🔍 用户资料API响应:', response);
            if (response.success && response.user) {
                console.log('✅ 用户验证成功，开始更新页面数据...');

                try {
                    // 更新页面数据
                    console.log('🔄 调用 updateUserInfo...');
                    updateUserInfo(response.user);
                    console.log('✅ updateUserInfo 调用完成');

                    console.log('🔄 开始加载其他数据...');
                    // 加载其他数据
                    console.log('🔄 调用 loadUserStats...');
                    loadUserStats();
                    console.log('✅ loadUserStats 调用完成');

                    console.log('🔄 调用 loadRecentTasks...');
                    loadRecentTasks();
                    console.log('✅ loadRecentTasks 调用完成');

                    console.log('🔄 检查 Chart.js 是否加载...');
                    if (typeof Chart !== 'undefined') {
                        console.log('✅ Chart.js 已加载，调用 initCharts...');
                        initCharts();
                        console.log('✅ initCharts 调用完成');
                    } else {
                        console.error('❌ Chart.js 未加载，跳过图表初始化');
                    }

                    console.log('⏰ 设置定时刷新...');
                    // 每30秒刷新一次数据
                    setInterval(function() {
                        loadUserStats();
                        loadRecentTasks();
                    }, 30000);
                    console.log('✅ 定时刷新设置完成');
                } catch (error) {
                    console.error('❌ 处理用户资料API响应时出错:', error);
                }
            } else {
                showError('获取用户信息失败');
                setTimeout(() => {
                    window.location.href = '{{ url_for("pages.login_page") }}';
                }, 2000);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ 认证状态检查失败:', error);
            console.error('❌ 状态码:', xhr.status);
            console.error('❌ 响应内容:', xhr.responseText);
            console.error('❌ 状态:', status);

            if (xhr.status === 401) {
                console.log('🔄 未登录或登录已过期，重定向到登录页面');
                showError('请先登录');
            } else {
                showError('认证失败，请重新登录');
            }

            setTimeout(() => {
                window.location.href = '{{ url_for("pages.login_page") }}';
            }, 2000);
        }
    });
}

// getToken函数已移除 - 使用session-based认证

function updateUserInfo(user) {
    console.log('🚀 updateUserInfo 函数被调用，用户数据:', user);

    try {
        console.log('🔄 更新欢迎消息...');
        $('#welcomeMessage').text(`欢迎回来，${user.username}！`);

        console.log('🔄 更新用户类型...');
        $('#userType').text(getUserTypeText(user.user_type));

        console.log('✅ updateUserInfo 执行完成');
    } catch (error) {
        console.error('❌ updateUserInfo 执行出错:', error);
    }
}

function updateCreditsInfo(credits) {
    console.log('🔍 updateCreditsInfo 被调用，数据:', credits);

    // 检查元素是否存在
    console.log('检查元素存在性:');
    console.log('  availableCredits:', $('#availableCredits').length);
    console.log('  totalCredits:', $('#totalCredits').length);
    console.log('  usedCredits:', $('#usedCredits').length);
    console.log('  dailyRemaining:', $('#dailyRemaining').length);

    $('#availableCredits').text(credits.available_credits);
    $('#totalCredits').text(credits.total_credits);
    $('#usedCredits').text(credits.used_credits);
    $('#dailyRemaining').text(credits.daily_remaining);
    $('#dailyLimit').text(credits.daily_limit === -1 ? '无限制' : credits.daily_limit);

    // 计算使用率
    const usageRate = credits.total_credits > 0 ?
        ((credits.used_credits / credits.total_credits) * 100).toFixed(1) : 0;
    $('#usageRate').text(usageRate);

    console.log('✅ updateCreditsInfo 完成');
}

function updateTaskStats(tasks) {
    console.log('🔍 updateTaskStats 被调用，数据:', tasks);

    // 检查元素是否存在
    console.log('检查元素存在性:');
    console.log('  totalTasks:', $('#totalTasks').length);
    console.log('  taskStatsDetail:', $('#taskStatsDetail').length);

    $('#totalTasks').text(tasks.total);

    // 更新任务统计显示
    const successRate = tasks.success_rate;
    const taskStatsHtml = `
        <div class="row mt-3">
            <div class="col-4 text-center">
                <div class="text-success">
                    <i class="fas fa-check-circle"></i>
                    <div>${tasks.completed}</div>
                    <small>已完成</small>
                </div>
            </div>
            <div class="col-4 text-center">
                <div class="text-danger">
                    <i class="fas fa-times-circle"></i>
                    <div>${tasks.failed}</div>
                    <small>失败</small>
                </div>
            </div>
            <div class="col-4 text-center">
                <div class="text-info">
                    <i class="fas fa-percentage"></i>
                    <div>${successRate}%</div>
                    <small>成功率</small>
                </div>
            </div>
        </div>
    `;

    // 如果有任务统计容器，更新它
    if ($('#taskStatsDetail').length) {
        $('#taskStatsDetail').html(taskStatsHtml);
    }

    console.log('✅ updateTaskStats 完成');
}

function updateRecentTasks(tasks) {
    const container = $('#recentTasks');

    if (!tasks || tasks.length === 0) {
        container.html('<div class="text-center text-muted">暂无任务记录</div>');
        return;
    }

    let tasksHtml = '';
    tasks.forEach(task => {
        const statusIcon = getTaskStatusIcon(task.status);
        const statusClass = getTaskStatusClass(task.status);
        const timeAgo = getTimeAgo(task.created_at);

        tasksHtml += `
            <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                <div class="d-flex align-items-center">
                    <i class="${statusIcon} ${statusClass} me-2"></i>
                    <div>
                        <div class="fw-bold">${getTaskTypeText(task.type)}</div>
                        <small class="text-muted">${task.message || '无描述'}</small>
                    </div>
                </div>
                <div class="text-end">
                    <div class="text-muted small">${timeAgo}</div>
                    <div class="text-primary small">${task.credits_cost || 0} 积分</div>
                </div>
            </div>
        `;
    });

    container.html(tasksHtml);
}

function getUserTypeText(userType) {
    const types = {
        'free': '免费用户',
        'premium': '付费用户',
        'pro': 'Pro用户',
        'enterprise': '企业用户'
    };
    return types[userType] || userType;
}

function getTaskStatusIcon(status) {
    const icons = {
        'completed': 'fas fa-check-circle',
        'failed': 'fas fa-times-circle',
        'processing': 'fas fa-spinner fa-spin',
        'pending': 'fas fa-clock'
    };
    return icons[status] || 'fas fa-question-circle';
}

function getTaskStatusClass(status) {
    const classes = {
        'completed': 'text-success',
        'failed': 'text-danger',
        'processing': 'text-warning',
        'pending': 'text-info'
    };
    return classes[status] || 'text-muted';
}

function getTaskTypeText(type) {
    const types = {
        'generate': '图像生成',
        'edit': '图像编辑',
        'style': '风格迁移',
        'restore': '照片修复'
    };
    return types[type] || type;
}

function getTimeAgo(dateString) {
    if (!dateString) return '未知时间';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return date.toLocaleDateString();
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;

    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function refreshTasks() {
    // 重新加载仪表板数据
    loadUserStats();
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 3000);
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
}

function loadUserStats() {
    console.log('🚀 loadUserStats 函数被调用');

    console.log('🔄 开始发送dashboard-data API请求...');
    $.ajax({
        url: '/api/user/dashboard-data',
        method: 'GET',
        credentials: 'include',  // 包含session cookie
        success: function(response) {
            console.log('🔍 仪表板数据响应:', response);
            console.log('🔍 响应类型:', typeof response);
            console.log('🔍 响应成功状态:', response.success);

            if (response.success) {
                console.log('✅ 开始更新仪表板数据...');
                console.log('  - 用户数据:', response.data.user);
                console.log('  - 积分数据:', response.data.credits);
                console.log('  - 任务数据:', response.data.tasks);
                console.log('  - 最近任务:', response.data.recent_tasks);

                // 更新用户信息
                console.log('🔄 更新用户信息...');
                updateUserInfo(response.data.user);

                // 更新积分信息
                console.log('🔄 更新积分信息...');
                updateCreditsInfo(response.data.credits);

                // 更新任务统计
                console.log('🔄 更新任务统计...');
                updateTaskStats(response.data.tasks);

                // 更新最近任务
                console.log('🔄 更新最近任务...');
                updateRecentTasks(response.data.recent_tasks);

                console.log('✅ 仪表板数据更新完成');
            } else {
                console.error('❌ 获取仪表板数据失败:', response.message);
                showError('加载仪表板数据失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('加载仪表板数据失败:', xhr.responseText);
            showError('无法连接到服务器，请检查网络连接');
        }
    });
}

function loadRecentTasks() {
    $.ajax({
        url: '/api/user/tasks',
        method: 'GET',
        credentials: 'include',  // 包含session cookie
        success: function(response) {
            if (response.success) {
                displayRecentTasks(response.tasks);
                $('#totalTasks').text(response.tasks.filter(t => t.status === 'completed').length);
            }
        },
        error: function() {
            $('#recentTasks').html('<div class="text-center text-danger">加载失败</div>');
        }
    });
}

function displayRecentTasks(tasks) {
    const recentTasks = tasks.slice(0, 5);
    let html = '';
    
    if (recentTasks.length === 0) {
        html = '<div class="text-center text-muted">暂无任务记录</div>';
    } else {
        recentTasks.forEach(function(task) {
            const statusClass = getStatusClass(task.status);
            const typeIcon = getTypeIcon(task.type);
            const createdAt = new Date(task.created_at).toLocaleString('zh-CN');
            
            html += `
                <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                    <div class="d-flex align-items-center">
                        <i class="${typeIcon} me-2"></i>
                        <div>
                            <div class="fw-bold">${task.prompt.substring(0, 50)}${task.prompt.length > 50 ? '...' : ''}</div>
                            <small class="text-muted">${createdAt}</small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge ${statusClass}">${getStatusText(task.status)}</span>
                        ${task.credits_cost ? `<small class="text-muted d-block">${task.credits_cost} 积分</small>` : ''}
                    </div>
                </div>
            `;
        });
    }
    
    $('#recentTasks').html(html);
}

function refreshTasks() {
    loadRecentTasks();
    showToast('success', '任务列表已刷新');
}

function getStatusClass(status) {
    switch(status) {
        case 'completed': return 'bg-success';
        case 'processing': return 'bg-warning';
        case 'queued': return 'bg-info';
        case 'failed': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getTypeIcon(type) {
    switch(type) {
        case 'generate': return 'fas fa-paint-brush text-primary';
        case 'edit': return 'fas fa-edit text-success';
        case 'style': return 'fas fa-palette text-warning';
        default: return 'fas fa-image text-secondary';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'queued': return '排队中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return status;
    }
}

function initCharts() {
    console.log('🚀 initCharts 函数开始执行');

    try {
        // 检查Chart.js是否可用
        if (typeof Chart === 'undefined') {
            console.error('❌ Chart.js 未定义，无法初始化图表');
            return;
        }

        console.log('🔄 初始化积分使用分布饼图...');
        // 积分使用分布饼图
        const ctx1 = document.getElementById('creditUsageChart').getContext('2d');
        window.creditChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['图像生成', '图像编辑', '风格迁移'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#007bff', '#28a745', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // 每日使用趋势柱状图
    const ctx2 = document.getElementById('dailyUsageChart').getContext('2d');
    window.dailyChart = new Chart(ctx2, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '积分使用',
                data: [],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    console.log('✅ initCharts 函数执行完成');

    } catch (error) {
        console.error('❌ initCharts 函数执行出错:', error);
    }
}

function updateCharts(data) {
    // 更新图表数据（这里需要根据实际API返回的数据结构调整）
    if (data.usage_by_type) {
        window.creditChart.data.datasets[0].data = [
            data.usage_by_type.generate || 0,
            data.usage_by_type.edit || 0,
            data.usage_by_type.style || 0
        ];
        window.creditChart.update();
    }
}

function showToast(type, message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 创建toast容器（如果不存在）
    if (!$('#toastContainer').length) {
        $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }
    
    const $toast = $(toastHtml);
    $('#toastContainer').append($toast);
    
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();
    
    // 3秒后自动移除
    setTimeout(() => $toast.remove(), 3000);
}
</script>
{% endblock %}
