from datetime import datetime
from .database import db, BaseModel
import uuid

class UserAuth(BaseModel):
    __tablename__ = 'user_auths'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False, index=True)
    auth_type = db.Column(db.String(20), nullable=False)  # phone/email/oauth
    identifier = db.Column(db.String(100), nullable=False, index=True)
    credential = db.Column(db.String(255), nullable=False)
    is_primary = db.Column(db.<PERSON>an, default=False, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    user = db.relationship("User", back_populates="auth_methods") 