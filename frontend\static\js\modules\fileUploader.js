/**
 * A reusable file uploader component that handles drag-and-drop, file selection, and previews.
 */
class FileUploader {
    /**
     * @param {string} containerSelector - The CSS selector for the main upload container element.
     * @param {string} inputName - The name attribute for the file input, used in FormData.
     */
    constructor(containerSelector, inputName) {
        this.container = document.querySelector(containerSelector);
        if (!this.container) {
            throw new Error(`FileUploader container not found: ${containerSelector}`);
        }
        this.inputName = inputName;
        this.file = null;

        this._initDOM();
        this._bindEvents();
    }

    _initDOM() {
        // Find or create necessary DOM elements
        this.fileInput = this.container.querySelector('input[type="file"]');
        this.uploadContent = this.container.querySelector('.upload-content'); // Area with "drag & drop" text
        this.preview = this.container.querySelector('.upload-preview');
        this.previewImage = this.container.querySelector('.preview-image');
        
        // Ensure all elements exist
        if (!this.fileInput || !this.uploadContent || !this.preview || !this.previewImage) {
            throw new Error('FileUploader is missing required internal elements (e.g., .upload-content, .upload-preview)');
        }
    }

    _bindEvents() {
        // Clicking the area triggers the file input
        this.uploadContent.addEventListener('click', () => this.fileInput.click());
        this.preview.querySelector('.change-btn')?.addEventListener('click', () => this.fileInput.click());

        // File selection
        this.fileInput.addEventListener('change', (e) => this._handleFiles(e.target.files));

        // Drag and drop
        this.container.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.container.classList.add('drag-over');
        });
        this.container.addEventListener('dragleave', () => this.container.classList.remove('drag-over'));
        this.container.addEventListener('drop', (e) => {
            e.preventDefault();
            this.container.classList.remove('drag-over');
            this._handleFiles(e.dataTransfer.files);
        });
        
        // Reset
        this.preview.querySelector('.remove-btn')?.addEventListener('click', () => this.reset());
    }

    _handleFiles(files) {
        if (!files.length) return;
        const file = files[0];

        if (!file.type.startsWith('image/')) {
            alert('请上传有效的图像文件 (e.g., JPG, PNG, WEBP)');
            return;
        }

        this.file = file;
        this.previewImage.src = URL.createObjectURL(file);
        this.uploadContent.style.display = 'none';
        this.preview.style.display = 'flex';
    }

    /**
     * Resets the uploader to its initial state.
     */
    reset() {
        this.file = null;
        this.fileInput.value = ''; // Clear the file input
        this.preview.style.display = 'none';
        this.uploadContent.style.display = 'flex';
        this.previewImage.src = '#';
    }

    /**
     * Gets the currently selected file.
     * @returns {File | null} The selected file, or null if no file is selected.
     */
    getFile() {
        return this.file;
    }

    /**
     * Appends the current file to an existing FormData object.
     * @param {FormData} formData - The FormData object to append to.
     */
    appendToFormData(formData) {
        if (this.file) {
            formData.append(this.inputName, this.file, this.file.name);
        }
    }
}

export default FileUploader; 