# 超级管理员账号信息与数据库结构文档

## 1. 超级管理员账号信息

| 字段     | 内容                |
|----------|---------------------|
| 用户名   | admin               |
| 手机号   | 99999999999         |
| 邮箱     | <EMAIL>   |
| 密码     | admin123456         |
| 账户类型 | enterprise（超级管理员） |
| 状态     | 已激活、已验证      |

> **安全建议**：首次登录后请尽快修改管理员密码，并绑定真实手机号和邮箱。

---

## 2. 数据库结构文档（主表）

### users

| 字段名             | 类型         | 说明             | 约束/备注         |
|--------------------|--------------|------------------|-------------------|
| id                 | String(36)   | UUID主键         | 主键              |
| phone              | String(20)   | 手机号           | 唯一，必填        |
| username           | String(50)   | 用户名           | 唯一，可空        |
| email              | String(100)  | 邮箱             | 唯一，可空        |
| password_hash      | String(255)  | 密码哈希         | 必填              |
| is_active          | Boolean      | 是否激活         | 默认True          |
| is_verified        | Boolean      | 是否已验证       | 默认False         |
| verification_token | String(255)  | 验证token        | 可空              |
| reset_token        | String(255)  | 重置token        | 可空              |
| reset_token_expires| DateTime     | 重置token过期    | 可空              |
| last_login         | DateTime     | 最后登录时间     | 可空              |
| user_type          | Enum         | 用户类型         | free/premium/pro/enterprise |
| subscription_expires| DateTime    | 订阅到期         | 可空              |
| total_credits      | Integer      | 总积分           | 默认10            |
| used_credits       | Integer      | 已用积分         | 默认0             |
| daily_limit        | Integer      | 每日限制         |                   |
| daily_used         | Integer      | 今日已用         |                   |
| daily_reset_date   | Date         | 每日重置日期     |                   |
| created_at         | DateTime     | 创建时间         |                   |
| updated_at         | DateTime     | 更新时间         |                   |

### user_auths

| 字段名     | 类型         | 说明           | 约束/备注         |
|------------|--------------|----------------|-------------------|
| id         | String(36)   | UUID主键       | 主键              |
| user_id    | String(36)   | 用户ID         | 外键，必填        |
| auth_type  | String(20)   | 认证类型       | phone/email/oauth |
| identifier | String(100)  | 标识（手机号/邮箱/OAuthID） | 唯一组合 |
| credential | String(255)  | 密码哈希/token | 必填              |
| is_primary | Boolean      | 是否主认证     | 默认False         |
| created_at | DateTime     | 创建时间       |                   |

### 其他表（简要）

- user_profiles：id(UUID主键)、user_id(唯一外键)、display_name、avatar_url、bio、website、location、preferences
- credits_transactions：id(UUID主键)、user_id、transaction_type、credits_amount、balance_after、description、created_at
- subscriptions：id(UUID主键)、user_id、plan、status、start_date、end_date、auto_renew
- payments：id(UUID主键)、user_id、amount、status、payment_method、created_at
- tasks：id(UUID主键)、user_id、type、status、message、prompt、model、parameters、input_image_path、output_image_path、credits_cost、is_public、created_at、completed_at
- system_settings、user_limits、credit_packages等均有UUID主键

---

如需完整SQL建表语句或ER图，请联系开发负责人导出。 