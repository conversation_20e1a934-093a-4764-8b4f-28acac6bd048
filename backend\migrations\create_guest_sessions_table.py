"""
创建访客会话表的数据库迁移
"""
import sqlite3
from datetime import datetime
import os

def create_guest_sessions_table():
    """创建访客会话表"""
    db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'instance', 'bfl_app.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='guest_sessions'")
        if cursor.fetchone():
            print("- guest_sessions 表已存在")
            return
        
        # 创建访客会话表
        create_table_sql = """
        CREATE TABLE guest_sessions (
            id VARCHAR(36) PRIMARY KEY NOT NULL,
            session_id VARCHAR(255) UNIQUE NOT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            page_views INTEGER NOT NULL DEFAULT 0,
            last_activity DATETIME NOT NULL,
            is_blocked BOOLEAN NOT NULL DEFAULT 0,
            block_reason VARCHAR(255),
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL
        )
        """
        cursor.execute(create_table_sql)
        
        # 创建索引
        cursor.execute("CREATE INDEX idx_guest_sessions_session_id ON guest_sessions(session_id)")
        cursor.execute("CREATE INDEX idx_guest_sessions_ip_address ON guest_sessions(ip_address)")
        cursor.execute("CREATE INDEX idx_guest_sessions_last_activity ON guest_sessions(last_activity)")
        
        conn.commit()
        print("✅ guest_sessions 表创建成功")
        print("✅ 相关索引创建成功")
        
    except Exception as e:
        print(f"✗ 创建访客会话表失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    create_guest_sessions_table()
