"""
订阅和支付相关数据模型
"""
from .database import db, BaseModel
import enum
import uuid


class SubscriptionStatus(enum.Enum):
    """订阅状态枚举"""
    pending = "pending"
    active = "active"
    cancelled = "cancelled"
    expired = "expired"
    past_due = "past_due"


class PaymentStatus(enum.Enum):
    """支付状态枚举"""
    pending = "pending"
    succeeded = "succeeded"
    failed = "failed"
    cancelled = "cancelled"
    refunded = "refunded"


class PaymentMethod(enum.Enum):
    """支付方式枚举"""
    stripe = "stripe"
    paypal = "paypal"
    alipay = "alipay"
    wechat = "wechat"


class Subscription(BaseModel):
    """订阅模型"""
    __tablename__ = 'subscriptions'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    
    # 订阅计划信息
    plan_type = db.Column(db.String(20), nullable=False)  # monthly, yearly, lifetime
    plan_name = db.Column(db.String(50), nullable=False)  # Premium, Pro, Enterprise
    status = db.Column(db.Enum(SubscriptionStatus), default=SubscriptionStatus.pending, nullable=False)
    
    # 价格信息
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='USD', nullable=False)
    
    # Stripe相关
    stripe_subscription_id = db.Column(db.String(255), unique=True)
    stripe_customer_id = db.Column(db.String(255))
    
    # 订阅周期
    current_period_start = db.Column(db.DateTime)
    current_period_end = db.Column(db.DateTime)
    cancel_at_period_end = db.Column(db.Boolean, default=False, nullable=False)
    cancelled_at = db.Column(db.DateTime)
    
    # 试用期
    trial_start = db.Column(db.DateTime)
    trial_end = db.Column(db.DateTime)
    
    # 关系
    user = db.relationship("User", back_populates="subscriptions")
    payments = db.relationship("Payment", back_populates="subscription", lazy='dynamic')
    
    @property
    def is_active(self):
        """订阅是否活跃"""
        return self.status == SubscriptionStatus.active
    
    @property
    def is_trial(self):
        """是否在试用期"""
        if not self.trial_start or not self.trial_end:
            return False
        
        from datetime import datetime
        now = datetime.utcnow()
        return self.trial_start <= now <= self.trial_end
    
    @property
    def days_until_renewal(self):
        """距离续费还有多少天"""
        if not self.current_period_end:
            return None
        
        from datetime import datetime
        now = datetime.utcnow()
        if self.current_period_end > now:
            return (self.current_period_end - now).days
        return 0
    
    def cancel(self, at_period_end=True):
        """取消订阅"""
        self.cancel_at_period_end = at_period_end
        if not at_period_end:
            from datetime import datetime
            self.cancelled_at = datetime.utcnow()
            self.status = SubscriptionStatus.cancelled
    
    def reactivate(self):
        """重新激活订阅"""
        self.cancel_at_period_end = False
        self.cancelled_at = None
        if self.status == SubscriptionStatus.cancelled:
            self.status = SubscriptionStatus.active
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        data['status'] = self.status.value if self.status else None
        data['amount'] = float(self.amount)
        data['is_active'] = self.is_active
        data['is_trial'] = self.is_trial
        data['days_until_renewal'] = self.days_until_renewal
        return data
    
    @classmethod
    def get_active_subscription(cls, user_id):
        """获取用户的活跃订阅"""
        return cls.query.filter_by(
            user_id=user_id,
            status=SubscriptionStatus.active
        ).first()
    
    def __repr__(self):
        return f'<Subscription {self.plan_name} {self.status.value if self.status else "unknown"}>'


class Payment(BaseModel):
    """支付记录模型"""
    __tablename__ = 'payments'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    subscription_id = db.Column(db.String(36), db.ForeignKey('subscriptions.id'), nullable=True)
    
    # 支付信息
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='USD', nullable=False)
    payment_method = db.Column(db.Enum(PaymentMethod), nullable=False)
    status = db.Column(db.Enum(PaymentStatus), default=PaymentStatus.pending, nullable=False)
    
    # 第三方支付ID
    payment_intent_id = db.Column(db.String(255))  # Stripe PaymentIntent ID
    charge_id = db.Column(db.String(255))          # Stripe Charge ID
    
    # 支付描述
    description = db.Column(db.Text)
    payment_metadata = db.Column(db.JSON)  # 额外的支付元数据
    
    # 退款信息
    refunded_amount = db.Column(db.Numeric(10, 2), default=0)
    refund_reason = db.Column(db.Text)
    refunded_at = db.Column(db.DateTime)
    
    # 关系
    user = db.relationship("User", back_populates="payments")
    subscription = db.relationship("Subscription", back_populates="payments")
    credit_transaction = db.relationship("CreditsTransaction", back_populates="payment", uselist=False)
    
    @property
    def is_successful(self):
        """支付是否成功"""
        return self.status == PaymentStatus.succeeded
    
    @property
    def is_refundable(self):
        """是否可以退款"""
        return (self.status == PaymentStatus.succeeded and 
                self.refunded_amount < self.amount)
    
    @property
    def net_amount(self):
        """净支付金额（扣除退款）"""
        return float(self.amount) - float(self.refunded_amount or 0)
    
    def refund(self, amount=None, reason=""):
        """退款"""
        if not self.is_refundable:
            raise ValueError("此支付不可退款")
        
        if amount is None:
            amount = self.amount - (self.refunded_amount or 0)
        
        if amount > (self.amount - (self.refunded_amount or 0)):
            raise ValueError("退款金额超过可退款金额")
        
        from datetime import datetime
        self.refunded_amount = (self.refunded_amount or 0) + amount
        self.refund_reason = reason
        self.refunded_at = datetime.utcnow()
        
        if self.refunded_amount >= self.amount:
            self.status = PaymentStatus.refunded
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        data['amount'] = float(self.amount)
        data['refunded_amount'] = float(self.refunded_amount or 0)
        data['net_amount'] = self.net_amount
        data['status'] = self.status.value if self.status else None
        data['payment_method'] = self.payment_method.value if self.payment_method else None
        data['is_successful'] = self.is_successful
        data['is_refundable'] = self.is_refundable
        return data
    
    @classmethod
    def get_user_payments(cls, user_id, limit=50):
        """获取用户的支付记录"""
        return cls.query.filter_by(user_id=user_id)\
                       .order_by(cls.created_at.desc())\
                       .limit(limit).all()
    
    @classmethod
    def get_revenue_stats(cls, days=30):
        """获取收入统计"""
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        stats = db.session.query(
            func.sum(cls.amount).label('total_revenue'),
            func.sum(cls.refunded_amount).label('total_refunded'),
            func.count(cls.id).label('total_payments'),
            func.count(cls.id).filter(cls.status == PaymentStatus.succeeded).label('successful_payments')
        ).filter(
            cls.created_at >= start_date
        ).first()
        
        return {
            'total_revenue': float(stats.total_revenue or 0),
            'total_refunded': float(stats.total_refunded or 0),
            'net_revenue': float((stats.total_revenue or 0) - (stats.total_refunded or 0)),
            'total_payments': stats.total_payments or 0,
            'successful_payments': stats.successful_payments or 0,
            'success_rate': (stats.successful_payments / stats.total_payments * 100) if stats.total_payments > 0 else 0
        }
    
    def __repr__(self):
        return f'<Payment ${self.amount} {self.status.value if self.status else "unknown"}>'
