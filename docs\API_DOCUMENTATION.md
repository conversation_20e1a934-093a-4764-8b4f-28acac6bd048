# BLK Web API 接口文档

## 概述

BLK Web 提供了一套完整的 RESTful API，支持图像生成、编辑、风格迁移、翻译等功能。所有 API 都基于 HTTP 协议，使用 JSON 格式进行数据交换。

## 基础信息

- **Base URL**: `http://localhost:5000`
- **API 版本**: v1
- **内容类型**: `application/json` 或 `multipart/form-data`
- **字符编码**: UTF-8

## 认证

当前版本暂不需要认证，所有 API 端点都是公开的。

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

### 状态码说明
- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误
- `503 Service Unavailable`: 服务不可用

## 图像处理 API

### 1. 图像生成

#### 端点
```
POST /api/generate
```

#### 描述
基于文本描述生成图像

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| prompt | string | 是 | 图像描述提示词 | "a beautiful landscape" |
| style | string | 否 | 图像风格 | "realistic", "anime", "oil_painting" |
| model | string | 否 | 生成模型 | "flux-pro", "flux-dev" |
| aspect_ratio | string | 否 | 宽高比 | "1:1", "16:9", "9:16" |
| test_mode | boolean | 否 | 测试模式 | true, false |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "a serene mountain lake at sunset",
    "style": "realistic",
    "model": "flux-pro",
    "aspect_ratio": "16:9"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "图像生成任务已提交",
  "estimated_time": 30
}
```

### 2. 图像编辑

#### 端点
```
POST /api/edit
```

#### 描述
对上传的图像进行 AI 编辑

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| image | file | 是 | 要编辑的图像文件 | - |
| instruction | string | 是 | 编辑指令 | "add a rainbow in the sky" |
| model | string | 否 | 编辑模型 | "flux-kontext-pro", "flux-kontext-max" |
| steps | integer | 否 | 扩散步数 | 20-50 |
| seed | integer | 否 | 随机种子 | 任意整数 |
| guidance | float | 否 | 引导强度 | 1.0-20.0 |
| test_mode | boolean | 否 | 测试模式 | true, false |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/edit \
  -F "image=@/path/to/image.jpg" \
  -F "instruction=add a beautiful sunset" \
  -F "model=flux-kontext-pro" \
  -F "steps=30"
```

#### 响应示例
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-************",
  "message": "图像编辑任务已提交",
  "estimated_time": 45
}
```

### 3. 风格迁移

#### 端点
```
POST /api/style
```

#### 描述
将参考图像的风格应用到内容描述上

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| reference_image | file | 是 | 参考风格图像 | - |
| content_description | string | 是 | 内容描述 | "a portrait of a woman" |
| model | string | 否 | 处理模型 | "flux-kontext-pro" |
| steps | integer | 否 | 扩散步数 | 20-50 |
| seed | integer | 否 | 随机种子 | 任意整数 |
| guidance | float | 否 | 引导强度 | 1.0-20.0 |
| test_mode | boolean | 否 | 测试模式 | true, false |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/style \
  -F "reference_image=@/path/to/style.jpg" \
  -F "content_description=a beautiful garden" \
  -F "model=flux-kontext-pro"
```

#### 响应示例
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-************",
  "message": "风格迁移任务已提交",
  "estimated_time": 60
}
```

### 4. 旧照片修复

#### 端点
```
POST /api/restore
```

#### 描述
修复老旧或损坏的照片

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| image | file | 是 | 要修复的图像文件 | - |
| enhancement_level | string | 否 | 增强级别 | "light", "medium", "strong" |
| preserve_original | boolean | 否 | 保留原始特征 | true, false |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/restore \
  -F "image=@/path/to/old_photo.jpg" \
  -F "enhancement_level=medium"
```

#### 响应示例
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-446655440003",
  "message": "照片修复任务已提交",
  "estimated_time": 40
}
```

### 5. 任务状态查询

#### 端点
```
GET /api/task_status/{task_id}
```

#### 描述
查询任务执行状态和结果

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| task_id | string | 是 | 任务ID |

#### 请求示例
```bash
curl -X GET http://localhost:5000/api/task_status/550e8400-e29b-41d4-a716-446655440000
```

#### 响应示例

##### 任务进行中
```json
{
  "success": true,
  "status": "processing",
  "progress": 65,
  "message": "正在生成图像...",
  "estimated_remaining_time": 15
}
```

##### 任务完成
```json
{
  "success": true,
  "status": "completed",
  "result": {
    "image_url": "/static/outputs/generated_1234567890_abcdef.jpg",
    "prompt": "a serene mountain lake at sunset",
    "metadata": {
      "model": "flux-pro",
      "style": "realistic",
      "aspect_ratio": "16:9",
      "generation_time": 28.5
    }
  },
  "message": "图像生成完成"
}
```

##### 任务失败
```json
{
  "success": false,
  "status": "failed",
  "error": {
    "code": "GENERATION_FAILED",
    "message": "图像生成失败",
    "details": "内容审核未通过"
  }
}
```

### 6. 图像对比

#### 端点
```
POST /api/compare
```

#### 描述
对比两张图像或进行智能预处理对比

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| mode | string | 是 | 对比模式 | "smart", "manual" |
| image1 | file | 是* | 第一张图像 | - |
| image2 | file | 否* | 第二张图像 | - |

*注：smart 模式只需要 image1，manual 模式需要 image1 和 image2

#### 请求示例
```bash
# 智能预处理对比
curl -X POST http://localhost:5000/api/compare \
  -F "mode=smart" \
  -F "image1=@/path/to/image.jpg"

# 手动对比
curl -X POST http://localhost:5000/api/compare \
  -F "mode=manual" \
  -F "image1=@/path/to/before.jpg" \
  -F "image2=@/path/to/after.jpg"
```

#### 响应示例
```json
{
  "success": true,
  "task_id": "550e8400-e29b-41d4-a716-446655440004",
  "message": "图像对比任务已提交",
  "estimated_time": 20
}
```

## 翻译服务 API

### 1. 文本翻译

#### 端点
```
POST /api/translate
```

#### 描述
将中文文本翻译为英文

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| text | string | 是 | 要翻译的中文文本 | "一只可爱的小猫" |
| service | string | 否 | 翻译服务 | "deepseek", "ollama" |
| target_language | string | 否 | 目标语言 | "en", "zh" |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/translate \
  -H "Content-Type: application/json" \
  -d '{
    "text": "一只在花园里玩耍的小猫",
    "service": "deepseek",
    "target_language": "en"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "translated_text": "a cute kitten playing in the garden",
  "original_text": "一只在花园里玩耍的小猫",
  "service_used": "deepseek",
  "translation_time": 1.2
}
```

### 2. 提示词润色

#### 端点
```
POST /api/polish
```

#### 描述
对中文提示词进行润色和优化

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| text | string | 是 | 要润色的中文提示词 | "一只猫" |
| service | string | 否 | 润色服务 | "deepseek", "ollama" |
| style | string | 否 | 润色风格 | "detailed", "artistic", "simple" |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/polish \
  -H "Content-Type: application/json" \
  -d '{
    "text": "一只猫",
    "service": "deepseek",
    "style": "detailed"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "polished_text": "一只毛茸茸的橘色小猫，有着明亮的绿色眼睛，正优雅地坐在阳光洒满的窗台上",
  "original_text": "一只猫",
  "service_used": "deepseek",
  "polish_time": 2.1
}
```

### 3. 翻译服务状态

#### 端点
```
GET /api/translation/status
```

#### 描述
检查翻译服务的可用性

#### 请求示例
```bash
curl -X GET http://localhost:5000/api/translation/status
```

#### 响应示例
```json
{
  "success": true,
  "services": {
    "deepseek": {
      "available": true,
      "response_time": 0.8,
      "last_check": "2024-01-15T10:30:00Z"
    },
    "ollama": {
      "available": false,
      "error": "Connection refused",
      "last_check": "2024-01-15T10:30:00Z"
    }
  }
}
```

## 文件管理 API

### 1. 文件上传

#### 端点
```
POST /api/upload
```

#### 描述
上传文件到服务器

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| file | file | 是 | 要上传的文件 | - |
| category | string | 否 | 文件分类 | "image", "document" |

#### 请求示例
```bash
curl -X POST http://localhost:5000/api/upload \
  -F "file=@/path/to/image.jpg" \
  -F "category=image"
```

#### 响应示例
```json
{
  "success": true,
  "file_id": "1234567890_abcdef",
  "file_url": "/static/uploads/1234567890_abcdef.jpg",
  "file_size": 2048576,
  "mime_type": "image/jpeg"
}
```

### 2. 文件下载

#### 端点
```
GET /api/download/{file_id}
```

#### 描述
下载指定文件

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file_id | string | 是 | 文件ID |

#### 请求示例
```bash
curl -X GET http://localhost:5000/api/download/1234567890_abcdef \
  -o downloaded_file.jpg
```

### 3. 文件列表

#### 端点
```
GET /api/files
```

#### 描述
获取文件列表

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| category | string | 否 | 文件分类 | "image", "document" |
| limit | integer | 否 | 返回数量限制 | 10, 20, 50 |
| offset | integer | 否 | 偏移量 | 0, 10, 20 |

#### 请求示例
```bash
curl -X GET "http://localhost:5000/api/files?category=image&limit=20&offset=0"
```

#### 响应示例
```json
{
  "success": true,
  "files": [
    {
      "file_id": "1234567890_abcdef",
      "filename": "generated_image.jpg",
      "file_url": "/static/outputs/generated_1234567890_abcdef.jpg",
      "file_size": 2048576,
      "mime_type": "image/jpeg",
      "created_at": "2024-01-15T10:30:00Z",
      "category": "image"
    }
  ],
  "total": 150,
  "limit": 20,
  "offset": 0
}
```

## 系统信息 API

### 1. 系统状态

#### 端点
```
GET /api/system/status
```

#### 描述
获取系统运行状态

#### 请求示例
```bash
curl -X GET http://localhost:5000/api/system/status
```

#### 响应示例
```json
{
  "success": true,
  "system": {
    "status": "healthy",
    "uptime": 86400,
    "version": "1.0.0",
    "environment": "production"
  },
  "services": {
    "image_generation": {
      "status": "available",
      "queue_size": 3,
      "processing_time_avg": 28.5
    },
    "translation": {
      "status": "available",
      "deepseek": true,
      "ollama": false
    }
  },
  "resources": {
    "cpu_usage": 45.2,
    "memory_usage": 68.7,
    "disk_usage": 23.1
  }
}
```

### 2. 系统配置

#### 端点
```
GET /api/system/config
```

#### 描述
获取系统配置信息（脱敏）

#### 请求示例
```bash
curl -X GET http://localhost:5000/api/system/config
```

#### 响应示例
```json
{
  "success": true,
  "config": {
    "max_file_size": 10485760,
    "supported_formats": ["jpg", "jpeg", "png", "webp"],
    "default_model": "flux-pro",
    "max_concurrent_tasks": 5,
    "task_timeout": 300,
    "translation_services": ["deepseek", "ollama"]
  }
}
```

## 错误代码说明

### 通用错误代码
| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| MISSING_PARAMETER | 400 | 缺少必需参数 |
| FILE_TOO_LARGE | 400 | 文件大小超过限制 |
| UNSUPPORTED_FORMAT | 400 | 不支持的文件格式 |
| TASK_NOT_FOUND | 404 | 任务不存在 |
| SERVICE_UNAVAILABLE | 503 | 服务不可用 |
| INTERNAL_ERROR | 500 | 内部服务器错误 |

### 图像处理错误代码
| 错误代码 | 描述 |
|----------|------|
| GENERATION_FAILED | 图像生成失败 |
| CONTENT_FILTERED | 内容审核未通过 |
| MODEL_UNAVAILABLE | 模型不可用 |
| PROCESSING_TIMEOUT | 处理超时 |

### 翻译服务错误代码
| 错误代码 | 描述 |
|----------|------|
| TRANSLATION_FAILED | 翻译失败 |
| SERVICE_TIMEOUT | 服务超时 |
| QUOTA_EXCEEDED | 配额超限 |
| INVALID_LANGUAGE | 不支持的语言 |

## 使用限制

### 文件上传限制
- 最大文件大小：10MB
- 支持格式：JPG, JPEG, PNG, WebP
- 最大分辨率：4096x4096

### 请求频率限制
- 图像生成：每分钟 10 次
- 图像编辑：每分钟 5 次
- 翻译服务：每分钟 60 次
- 文件上传：每分钟 20 次

### 任务队列限制
- 最大并发任务：5 个
- 任务超时时间：5 分钟
- 队列最大长度：50 个

## SDK 和示例

### Python SDK 示例
```python
import requests
import json

class BLKWebClient:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
    
    def generate_image(self, prompt, style="realistic", model="flux-pro"):
        """生成图像"""
        url = f"{self.base_url}/api/generate"
        data = {
            "prompt": prompt,
            "style": style,
            "model": model
        }
        response = requests.post(url, json=data)
        return response.json()
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        url = f"{self.base_url}/api/task_status/{task_id}"
        response = requests.get(url)
        return response.json()
    
    def translate_text(self, text, service="deepseek"):
        """翻译文本"""
        url = f"{self.base_url}/api/translate"
        data = {
            "text": text,
            "service": service
        }
        response = requests.post(url, json=data)
        return response.json()

# 使用示例
client = BLKWebClient()

# 生成图像
result = client.generate_image("a beautiful sunset")
print(f"Task ID: {result['task_id']}")

# 查询状态
status = client.get_task_status(result['task_id'])
print(f"Status: {status['status']}")

# 翻译文本
translation = client.translate_text("一只可爱的小猫")
print(f"Translation: {translation['translated_text']}")
```

### JavaScript SDK 示例
```javascript
class BLKWebClient {
  constructor(baseUrl = 'http://localhost:5000') {
    this.baseUrl = baseUrl;
  }

  async generateImage(prompt, options = {}) {
    const response = await fetch(`${this.baseUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt,
        ...options
      })
    });
    return response.json();
  }

  async getTaskStatus(taskId) {
    const response = await fetch(`${this.baseUrl}/api/task_status/${taskId}`);
    return response.json();
  }

  async translateText(text, service = 'deepseek') {
    const response = await fetch(`${this.baseUrl}/api/translate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text,
        service
      })
    });
    return response.json();
  }
}

// 使用示例
const client = new BLKWebClient();

// 生成图像
client.generateImage('a beautiful sunset', {
  style: 'realistic',
  model: 'flux-pro'
}).then(result => {
  console.log('Task ID:', result.task_id);
  
  // 轮询状态
  const checkStatus = () => {
    client.getTaskStatus(result.task_id).then(status => {
      console.log('Status:', status.status);
      if (status.status === 'completed') {
        console.log('Image URL:', status.result.image_url);
      } else if (status.status === 'processing') {
        setTimeout(checkStatus, 2000);
      }
    });
  };
  
  checkStatus();
});
```

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持图像生成、编辑、风格迁移
- 集成翻译服务
- 添加文件管理功能
- 实现任务管理系统

### 计划功能
- 用户认证和授权
- 批量处理 API
- Webhook 通知
- 更多图像处理功能
- 性能优化和缓存

---

**技术支持**:
- 文档问题：[GitHub Issues]()
- API 问题：[技术支持邮箱]()
- 功能建议：[功能请求]()

**相关资源**:
- [开发者指南](DEVELOPER_GUIDE.md)
- [项目文档](PROJECT_DOCUMENTATION.md)
- [架构设计](ARCHITECTURE.md)