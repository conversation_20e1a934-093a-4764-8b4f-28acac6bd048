# BLK Web AI 图像处理平台

> 🎨 基于 BFL API 的智能图像处理 Web 应用  
> 🌐 集成多引擎 AI 翻译服务  
> 🚀 现代化模块化架构设计

## 🌟 主要功能

- **AI 图像生成** - 文本到图像，支持多种风格和模型
- **智能图像编辑** - 基于指令的图像修改和优化
- **风格迁移** - 参考图像的艺术风格迁移  
- **智能翻译** - 中文提示词自动翻译 (DeepSeek + Ollama)
- **用户系统** - 完整的认证、积分管理和任务跟踪
- **响应式设计** - 支持桌面和移动设备

## ⚡ 快速开始

```bash
# 1. 安装依赖
pip install -r backend/requirements_web.txt

# 2. 配置环境变量
export BFL_API_KEY="your_bfl_api_key"
export DEEPSEEK_API_KEY="your_deepseek_api_key"

# 3. 初始化数据库
python init_db.py

# 4. 启动应用
python run_web.py
```

访问 http://localhost:5000 开始使用

## 📁 项目结构

```
web/
├── run_web.py              # 🚀 主启动入口
├── backend/               # 🔧 Flask 应用核心
│   ├── routes/           # API 路由
│   ├── services/         # 业务服务
│   ├── models/           # 数据模型
│   └── templates/        # 页面模板
├── Translation/          # 🌐 翻译服务
├── BFL/                 # 🎨 图像生成核心
├── tests/               # 🧪 测试套件
└── docs/                # 📚 详细文档
```

## 🔗 相关文档

- **[详细文档](PROJECT_DOCUMENTATION.md)** - 完整的项目文档和API说明
- **[架构设计](ARCHITECTURE.md)** - 系统架构和设计原则
- **[开发指南](DEVELOPER_GUIDE.md)** - 开发环境配置和代码规范
- **[清理报告](PROJECT_CLEANUP_REPORT.md)** - 项目重构和清理说明

## 🛠️ 技术栈

- **后端**: Flask, SQLAlchemy, JWT
- **前端**: Bootstrap 5, 原生 JavaScript ES6+
- **AI 服务**: BFL API, DeepSeek, Ollama
- **数据库**: SQLite (开发) / PostgreSQL (生产)

## 📊 项目状态

- ✅ **核心功能完整** - 图像生成、编辑、翻译全面可用
- ✅ **架构清晰** - 模块化设计，易于维护和扩展
- ✅ **代码整洁** - 完成大规模清理和重构
- ✅ **文档完善** - 详细的技术文档和使用说明

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！请阅读 [开发指南](DEVELOPER_GUIDE.md) 了解详细的贡献流程。

## 📜 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**版本**: v2.0 | **更新**: 2025-01-03 | **状态**: 生产就绪 