#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用工具函数测试模块
测试utils/common_utils.py中的所有工具函数
"""

import sys
import os
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入需要测试的函数
from backend.utils.common_utils import (
    calculate_gcd,
    simplify_aspect_ratio,
    generate_bfl_dimensions,
    detect_prompt_language,
    is_chinese_text,
    extract_english_from_mixed_text,
    resize_image_for_upload
)

# 其他需要的函数从backend.utils.common_utils导入
from backend.utils.common_utils import (
    create_error_response,
    create_success_response,
    log_error_with_context
)


def test_gcd_function() -> bool:
    """
    测试最大公约数计算函数
    
    Returns:
        测试是否通过
    """
    print("🧮 测试GCD函数...")
    
    test_cases = [
        (48, 18, 6),
        (100, 25, 25),
        (17, 13, 1),
        (1024, 768, 256),
        (1920, 1080, 120)
    ]
    
    passed = 0
    for a, b, expected in test_cases:
        result = calculate_gcd(a, b)
        status = "✅" if result == expected else "❌"
        print(f"   GCD({a}, {b}) = {result} (期望: {expected}) {status}")
        if result == expected:
            passed += 1
    
    success = passed == len(test_cases)
    print(f"   结果: {passed}/{len(test_cases)} 通过 {'✅' if success else '❌'}")
    return success


def test_aspect_ratio_function() -> bool:
    """
    测试宽高比简化函数
    
    Returns:
        测试是否通过
    """
    print("\n📐 测试宽高比简化函数...")
    
    test_cases = [
        (1920, 1080, "16:9"),
        (1024, 768, "4:3"),
        (1080, 1080, "1:1"),
        (1440, 900, "8:5"),
        (2560, 1440, "16:9"),
        (832, 1248, "2:3")
    ]
    
    passed = 0
    for width, height, expected in test_cases:
        result = simplify_aspect_ratio(width, height)
        status = "✅" if result == expected else "❌"
        print(f"   {width}x{height} = {result} (期望: {expected}) {status}")
        if result == expected:
            passed += 1
    
    success = passed == len(test_cases)
    print(f"   结果: {passed}/{len(test_cases)} 通过 {'✅' if success else '❌'}")
    return success


def test_bfl_dimensions_function() -> bool:
    """
    测试BFL尺寸生成函数
    
    Returns:
        测试是否通过
    """
    print("\n📏 测试BFL尺寸生成函数...")
    
    # 测试默认参数
    candidates = generate_bfl_dimensions()
    print(f"   默认参数生成候选数: {len(candidates)}")
    
    if len(candidates) == 0:
        print("   ❌ 没有生成任何候选尺寸")
        return False
    
    # 检查候选尺寸的约束
    valid_candidates = 0
    for width, height, pixels, ratio in candidates:
        # 检查是否是16的倍数
        if width % 16 == 0 and height % 16 == 0:
            # 检查像素数是否在合理范围内
            if 988000 <= pixels <= 1092000:  # 1040000 ± 5%
                # 检查宽高比是否在范围内
                if 9/21 <= ratio <= 21/9:
                    valid_candidates += 1
    
    success_rate = valid_candidates / len(candidates)
    success = success_rate >= 0.9  # 至少90%的候选符合约束
    
    print(f"   有效候选数: {valid_candidates}/{len(candidates)} ({success_rate:.1%})")
    print(f"   约束检查: {'✅' if success else '❌'}")
    
    # 测试自定义参数
    custom_candidates = generate_bfl_dimensions(
        target_pixels=2000000,
        step=32,
        tolerance=0.1,
        aspect_ratio_range=(1/2, 2/1)
    )
    print(f"   自定义参数生成候选数: {len(custom_candidates)}")
    
    return success and len(custom_candidates) > 0


def test_error_response_functions() -> bool:
    """
    测试错误响应创建函数
    
    Returns:
        测试是否通过
    """
    print("\n❌ 测试错误响应函数...")
    
    # 测试错误响应
    error_response, status_code = create_error_response("测试错误消息")
    expected_error = {
        'success': False,
        'error': '测试错误消息'
    }
    
    error_success = (error_response == expected_error and status_code == 400)
    print(f"   错误响应格式: {'✅' if error_success else '❌'}")
    
    # 测试成功响应
    success_response = create_success_response(
        data={'test': 'value'},
        message='测试成功'
    )
    expected_success = {
        'success': True,
        'data': {'test': 'value'},
        'message': '测试成功'
    }
    
    success_success = (success_response == expected_success)
    print(f"   成功响应格式: {'✅' if success_success else '❌'}")
    
    return error_success and success_success


def test_error_logging_function() -> bool:
    """
    测试错误日志记录函数
    
    Returns:
        测试是否通过
    """
    print("\n📝 测试错误日志函数...")
    
    try:
        # 创建一个测试异常
        raise ValueError("这是一个测试异常")
    except Exception as e:
        # 测试错误日志记录
        error_msg = log_error_with_context(e, "测试上下文", print_traceback=False)
        expected_prefix = "❌ 测试上下文错误: 这是一个测试异常"
        
        success = error_msg == expected_prefix
        print(f"   错误日志格式: {'✅' if success else '❌'}")
        
        return success


def test_common_utils() -> Dict[str, Any]:
    """
    运行完整的通用工具函数测试套件
    
    Returns:
        测试结果摘要
    """
    print("🔧 通用工具函数测试")
    print("=" * 60)
    
    # 运行各项测试
    test_results = {
        'gcd': test_gcd_function(),
        'aspect_ratio': test_aspect_ratio_function(),
        'bfl_dimensions': test_bfl_dimensions_function(),
        'response_functions': test_error_response_functions(),
        'error_logging': test_error_logging_function()
    }
    
    # 汇总结果
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    overall_success = passed_tests == total_tests
    
    print("\n" + "=" * 60)
    print("🎉 工具函数测试完成！")
    print(f"\n📊 测试结果摘要:")
    for test_name, result in test_results.items():
        print(f"   {test_name}: {'✅' if result else '❌'}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 通过 {'✅' if overall_success else '❌'}")
    
    return {
        'individual_tests': test_results,
        'passed_count': passed_tests,
        'total_count': total_tests,
        'overall_success': overall_success
    }


if __name__ == '__main__':
    test_common_utils()