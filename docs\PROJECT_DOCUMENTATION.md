# BLK Web AI 图像处理平台 - 项目文档

> 更新时间: 2025-01-03  
> 版本: v2.0  
> 状态: 生产就绪

## 项目概述

BLK Web 是一个功能完整的 AI 图像处理平台，基于 Flask 构建，集成了图像生成、编辑、风格迁移、智能翻译、用户管理等现代化功能。项目采用模块化架构设计，支持扩展和自定义。

## 🚀 核心功能

### 图像处理能力
- **AI 图像生成** - 基于 BFL API 的文本到图像生成
- **智能图像编辑** - 基于指令的图像修改和优化
- **风格迁移** - 参考图像的艺术风格迁移
- **旧照片修复** - AI 驱动的照片修复和增强
- **图像预处理** - 自动尺寸优化和100万像素限制

### 智能翻译系统
- **多引擎支持** - DeepSeek API + Ollama 本地模型
- **自动语言检测** - 中英文智能识别
- **提示词润色** - AI 驱动的文本优化
- **容错机制** - 主备服务自动切换

### 用户系统
- **JWT 认证** - 安全的用户认证机制
- **积分系统** - 完整的积分管理和消费跟踪
- **任务管理** - 异步任务处理和状态监控
- **使用限制** - 可配置的日限额和总限额

## 📁 项目结构

### 清理后的目录架构
```
web/
├── run_web.py                # 🚀 主启动入口
├── init_db.py               # 🗄️ 数据库初始化工具
├── PROJECT_CLEANUP_REPORT.md # 📋 项目清理报告
├── backend/                 # 🔧 Flask 应用核心
│   ├── app_new.py          # 应用工厂
│   ├── config/             # 配置管理
│   │   └── app_config.py   # 统一配置类
│   ├── routes/             # 🛣️ 路由模块
│   │   ├── page_routes.py      # 页面路由
│   │   ├── image_routes.py     # 图像API路由
│   │   ├── translation_routes.py # 翻译API路由
│   │   ├── user_routes.py      # 用户API路由
│   │   └── admin_routes.py     # 管理员路由
│   ├── services/           # 🔨 业务服务层
│   │   ├── image_service.py    # 图像处理服务
│   │   ├── task_service.py     # 任务管理服务
│   │   └── credit_service.py   # 积分管理服务
│   ├── models/             # 📊 数据模型
│   │   ├── user.py            # 用户模型
│   │   ├── task.py            # 任务模型
│   │   ├── credit.py          # 积分模型
│   │   └── system_settings.py # 系统设置
│   ├── utils/              # 🛠️ 工具函数
│   │   ├── common_utils.py     # 通用工具
│   │   ├── helpers.py          # 辅助函数
│   │   ├── image_preprocessor.py # 图像预处理
│   │   └── validators.py       # 数据验证
│   ├── auth/               # 🔐 认证模块
│   │   ├── auth_manager.py     # 认证管理器
│   │   └── decorators.py       # 认证装饰器
│   ├── static/             # 🎨 静态资源
│   │   ├── css/               # 样式文件
│   │   ├── js/                # JavaScript 模块
│   │   └── img/               # 图片资源
│   └── templates/          # 📄 HTML 模板
│       ├── base.html          # 基础模板
│       ├── index.html         # 主页
│       ├── generate.html      # 图像生成页面
│       ├── edit.html          # 图像编辑页面
│       ├── style.html         # 风格迁移页面
│       ├── translate.html     # 翻译页面
│       ├── restore.html       # 修复页面
│       ├── compare.html       # 对比页面
│       ├── gallery.html       # 画廊页面
│       └── auth/              # 用户系统页面
├── Translation/             # 🌐 翻译服务模块
│   ├── unified_translator.py   # 统一翻译接口
│   ├── deepseek_service.py     # DeepSeek API 服务
│   └── ollama_service.py       # Ollama 本地服务
├── BFL/                     # 🎨 图像生成核心
│   └── bfl_image_generator.py  # BFL API 客户端
├── tests/                   # 🧪 测试套件
│   ├── run_tests.py           # 测试运行器
│   ├── test_utils.py          # 工具函数测试
│   ├── test_translation.py    # 翻译服务测试
│   ├── test_image_generation.py # 图像生成测试
│   └── test_language_detection.py # 语言检测测试
├── docs/                    # 📚 专业文档
│   ├── WEB_README.md          # Web应用说明
│   ├── BFL_README.md          # BFL集成文档
│   └── AUTO_TRANSLATION_README.md # 翻译系统文档
├── uploads/                 # 📤 用户上传文件
├── outputs/                 # 📥 生成结果文件
└── del/                     # 🗑️ 已清理文件 (备份)
```

## 🔧 技术栈

### 后端技术
- **Flask 2.0+** - Python Web 框架
- **SQLAlchemy** - ORM 数据库抽象层
- **Flask-JWT-Extended** - JWT 认证
- **Pillow (PIL)** - 图像处理
- **requests** - HTTP 客户端
- **threading** - 异步任务处理

### 前端技术
- **Bootstrap 5** - 响应式 UI 框架
- **Font Awesome** - 图标库
- **原生 JavaScript ES6+** - 模块化前端架构
- **Jinja2** - 模板引擎

### 外部服务
- **BFL API** - 图像生成和编辑
- **DeepSeek API** - 云端 AI 翻译服务
- **Ollama** - 本地 AI 模型服务

## 🏗️ 架构设计

### 应用工厂模式
```python
# backend/app_new.py
def create_app():
    """应用工厂函数 - 创建和配置Flask应用"""
    app = Flask(__name__)
    
    # 配置应用
    _configure_app(app)
    
    # 初始化组件
    _initialize_components()
    
    # 注册蓝图
    _register_blueprints(app)
    
    return app
```

### 蓝图架构
- **page_bp** - 页面路由蓝图
- **image_bp** - 图像API蓝图 (`/api/*`)
- **translation_bp** - 翻译API蓝图 (`/api/translate/*`)
- **user_bp** - 用户API蓝图 (`/api/user/*`)
- **admin_bp** - 管理API蓝图 (`/api/admin/*`)

### 服务层设计
```python
# 图像服务
image_service = ImageService()
task_service = TaskService()
credit_service = CreditService()

# 翻译服务
translator = UnifiedTranslator(
    primary='deepseek',
    backup='ollama'
)
```

## 🌟 功能详解

### 1. 图像生成 (`/generate`)
**核心功能:**
- 文本到图像生成
- 多种艺术风格
- 可调节参数 (尺寸、步数、种子)
- 中文提示词自动翻译

**API端点:** `POST /api/generate`
**模型支持:** FLUX Kontext Pro, FLUX Dev

### 2. 图像编辑 (`/edit`)
**核心功能:**
- 基于指令的图像编辑
- 图像预处理优化
- 保持原图宽高比
- 支持高级参数调节

**API端点:** `POST /api/edit`
**特性:** 100万像素自动限制、智能裁剪

### 3. 风格迁移 (`/style`)
**核心功能:**
- 参考图像风格迁移
- 内容描述生成
- 风格强度控制

**API端点:** `POST /api/style`

### 4. 智能翻译 (`/translate`)
**核心功能:**
- 中英文自动检测
- 双引擎容错机制
- 提示词专业润色
- 实时服务状态监控

**API端点:**
- `POST /api/translate` - 文本翻译
- `POST /api/translate/polish` - 文本润色
- `GET /api/translate/status` - 服务状态

### 5. 用户系统
**核心功能:**
- JWT 认证和授权
- 积分系统和消费跟踪
- 使用限制和配额管理
- 任务历史和状态跟踪

**API端点:**
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/profile` - 用户信息
- `GET /api/user/credits` - 积分查询

## 🔌 API 接口

### 图像处理 API

#### 生成图像
```http
POST /api/generate
Content-Type: multipart/form-data

prompt: string (必需) - 图像描述
model: string - 模型选择 (默认: flux-kontext-pro)
style: string - 风格选择
aspect_ratio: string - 宽高比 (1:1, 16:9, 9:16)
steps: integer - 扩散步数
seed: integer - 随机种子
```

#### 编辑图像
```http
POST /api/edit
Content-Type: multipart/form-data

image: file (必需) - 上传的图像
prompt: string (必需) - 编辑指令
model: string - 模型选择
steps: integer - 扩散步数
guidance: float - 引导强度
```

### 翻译 API

#### 翻译文本
```http
POST /api/translate
Content-Type: application/json

{
  "text": "要翻译的文本"
}
```

#### 润色文本
```http
POST /api/translate/polish
Content-Type: application/json

{
  "text": "要润色的文本"
}
```

## 🚀 部署和运行

### 环境要求
- Python 3.8+
- 16GB+ 内存推荐
- 支持 GPU 加速 (可选)

### 快速启动
```bash
# 1. 安装依赖
pip install -r backend/requirements_web.txt

# 2. 配置环境变量
export BFL_API_KEY="your_bfl_api_key"
export DEEPSEEK_API_KEY="your_deepseek_api_key"

# 3. 初始化数据库
python init_db.py

# 4. 启动应用
python run_web.py
```

### 访问地址
- Web 界面: http://localhost:5000
- API 文档: http://localhost:5000/api (待实现)

## 🔧 配置管理

### 主配置类 (AppConfig)
```python
class AppConfig:
    # 基础配置
    DEBUG = True
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key')
    
    # 数据库配置
    DATABASE_ENABLED = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///bfl_app.db'
    
    # 外部服务配置
    BFL_API_KEY = os.getenv('BFL_API_KEY')
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
    
    # 文件配置
    UPLOAD_FOLDER = 'backend/uploads'
    OUTPUT_FOLDER = 'backend/outputs'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
```

## 🧪 测试套件

### 测试结构
```
tests/
├── run_tests.py              # 测试运行器
├── test_utils.py             # 工具函数测试
├── test_translation.py       # 翻译服务测试
├── test_image_generation.py  # 图像生成测试
└── test_language_detection.py # 语言检测测试
```

### 运行测试
```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定测试
python -m pytest tests/test_translation.py -v
```

## 📊 性能特性

### 图像处理优化
- **100万像素限制** - 自动调整大图片尺寸
- **智能裁剪** - BFL API 最佳尺寸适配
- **异步处理** - 非阻塞任务执行
- **进度跟踪** - 实时任务状态更新

### 翻译服务优化
- **双引擎容错** - 主备服务自动切换
- **缓存机制** - 避免重复翻译
- **负载均衡** - 智能服务选择

### 前端优化
- **模块化架构** - 按需加载
- **响应式设计** - 移动端适配
- **实时更新** - WebSocket 或轮询

## 📝 开发指南

### 添加新功能
1. 在 `backend/routes/` 中创建新路由
2. 在 `backend/services/` 中实现业务逻辑
3. 在 `backend/templates/` 中添加页面模板
4. 在 `backend/static/js/` 中添加前端脚本

### 代码规范
- Python: PEP 8 规范
- JavaScript: ES6+ 语法
- HTML/CSS: 语义化标签、响应式设计

### 提交指南
- 功能开发在独立分支
- 代码审查后合并主分支
- 完整的提交说明

## 📈 未来规划

### 短期目标
- [ ] API 文档自动生成
- [ ] 用户使用分析
- [ ] 性能监控系统
- [ ] 批量处理功能

### 长期目标
- [ ] 微服务架构迁移
- [ ] Docker 容器化部署
- [ ] 多语言支持扩展
- [ ] 插件系统开发

## 🤝 贡献指南

### 如何贡献
1. Fork 项目仓库
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

### 代码提交
- 遵循现有代码风格
- 添加必要的测试
- 更新相关文档

## 📞 技术支持

### 常见问题
1. **启动失败** - 检查依赖安装和环境变量
2. **API 调用失败** - 验证 API 密钥配置
3. **图像上传问题** - 确认文件格式和大小限制

### 获取帮助
- 查看项目文档
- 提交 Issue 描述问题
- 联系开发团队

---

**项目版本**: v2.0  
**最后更新**: 2025-01-03  
**维护状态**: 积极维护中