# BFL AI 图像生成器

> **⚠️ 重要提示：模型参数差异**  
> **FLUX.1 Kontext 系列**（图像编辑/风格迁移）仅支持 `aspect_ratio` 参数，**不支持** `width/height` 精确像素控制。  
> **其他 FLUX 模型**（纯图像生成）支持 `width/height` 精确像素控制和复杂参数。  
> 请根据选择的模型使用正确的参数！

基于 Black Forest Labs API 实现的简单 Python 图像生成工具，支持多种 FLUX 模型进行高质量图像生成。

## 功能特性

- 🎨 支持多种 FLUX 模型（Kontext Pro/Max、Pro 1.1 Ultra/Standard、Pro、Dev）
- 🚀 异步请求处理，自动轮询结果
- 📥 自动下载生成的图像
- 🛠️ 命令行和编程接口双重支持
- 📊 详细的状态反馈和错误处理
- 🔧 灵活的参数配置
- 📐 支持精确的宽度/高度设置
- 🎯 可重现的结果（通过种子值）
- 🔄 批量图像生成
- ⚡ 并发处理优化
- 🎛️ 高级参数控制（步数、引导强度、提示词增强等）
- ✏️ **图像编辑功能** - 基于文本指令修改现有图像
- 🎨 **风格迁移功能** - 基于参考图像生成新风格的内容
- 🖼️ **以图生图功能** - 使用图像作为输入进行生成

## 安装依赖

```bash
pip install -r requirements.txt
```

或者直接安装：

```bash
pip install requests
```

## 获取 API 密钥

1. 访问 [BFL Dashboard](https://dashboard.bfl.ai) 注册账户
2. 确认邮箱后登录
3. 点击 "Add Key" 创建 API 密钥
4. 复制生成的 API 密钥

## 使用方法

### 方法1: 命令行使用

#### 设置环境变量（推荐）

```bash
# Windows
set BFL_API_KEY=your_api_key_here

# Linux/Mac
export BFL_API_KEY=your_api_key_here
```

#### 基本使用

```bash
# 基本图像生成
python bfl_image_generator.py "一只可爱的小猫在花园里玩耍"

# 指定模型和宽高比
python bfl_image_generator.py "美丽的日落风景" --model flux-pro-1.1 --aspect-ratio 16:9

# 使用精确尺寸
python bfl_image_generator.py "未来城市夜景" --width 1024 --height 768 --output city.jpg

# 高级参数控制
python bfl_image_generator.py "魔法森林" --model flux-pro-1.1-ultra --steps 40 --seed 12345 --prompt-upsampling --raw

# 批量生成
python bfl_image_generator.py "日落风景" "未来城市" "魔法森林" --batch --output batch_images --max-concurrent 3

# 图像编辑
python bfl_image_generator.py "添加一只小船在湖上" --input-image sunset.jpg --edit --output edited.jpg

# 风格迁移
python bfl_image_generator.py "一座现代城市" --input-image style_ref.jpg --style-transfer --output styled.jpg

# 以图生图 (使用参考图像)
python bfl_image_generator.py "将这个场景改为冬天的雪景" --input-image summer.jpg --output winter.jpg

# 直接传入API密钥
python bfl_image_generator.py "抽象艺术作品" --api-key your_api_key_here
```

#### 支持的模型

- `flux-kontext-pro` - 最新的 Kontext Pro 模型
- `flux-kontext-max` - Kontext Max 模型（限制6个并发任务）
- `flux-pro-1.1-ultra` - Pro 1.1 Ultra 高质量模型
- `flux-pro-1.1` - Pro 1.1 标准模型（默认）
- `flux-pro` - Pro 模型
- `flux-dev` - 开发版模型

#### 支持的宽高比

- `1:1` - 正方形（默认）
- `16:9` - 宽屏
- `9:16` - 竖屏
- `21:9` - 超宽屏
- `3:4` - 竖向矩形
- `4:3` - 横向矩形

#### 高级参数说明

| 参数 | 类型 | 适用模型 | 说明 | 默认值 |
|------|------|----------|------|--------|
| `aspect_ratio` | string | **所有模型** | 宽高比（如"16:9"） | "1:1" |
| `width` | int | **仅非Kontext模型** | 图像宽度（32的倍数，256-1440） | 1024 |
| `height` | int | **仅非Kontext模型** | 图像高度（32的倍数，256-1440） | 768 |
| `input_image` | string | **仅Kontext模型** | Base64编码的输入图像（编辑/风格迁移） | null |
| `steps` | int | **仅非Kontext模型** | 扩散步数（1-50） | 40 |
| `guidance` | float | **仅非Kontext模型** | 引导强度 | varies |
| `seed` | int | **所有模型** | 随机种子 | null |
| `prompt_upsampling` | bool | **所有模型** | 提示词增强 | false |
| `safety_tolerance` | int | **所有模型** | 安全容忍度（0-6） | 2 |
| `raw` | bool | **仅Ultra模型** | 生成更自然图像 | false |

#### 尺寸控制 - 根据模型区分

**FLUX.1 Kontext 系列（图像编辑/风格迁移专用）**：
- ✅ **仅支持 aspect_ratio 参数**（字符串格式，如 "16:9"）
- ✅ **支持范围**：3:7（竖向）到 7:3（横向）
- ✅ **默认输出**：约1MP像素（~1024x1024）
- ❌ **不支持**：width/height 精确像素参数

**其他FLUX模型（纯图像生成）**：
- ✅ **支持 width/height 参数**（像素，32的倍数）
- ✅ **尺寸范围**：
  - flux-pro/flux-pro-1.1: 256x256 到 1440x1440
  - flux-pro-1.1-ultra: 默认高分辨率
  - flux-dev: 256x256 到 1440x1440
- ✅ **也支持 aspect_ratio 参数**作为替代

#### 功能模型分类

| 模型类别 | 主要用途 | 尺寸控制方式 | 特殊功能 |
|----------|----------|-------------|----------|
| **Kontext 系列** | 图像编辑、风格迁移 | 仅 aspect_ratio | input_image 参数 |
| **Pro/Dev 系列** | 纯图像生成 | width/height 或 aspect_ratio | 多种高级参数 |

#### 图像编辑功能说明

**FLUX.1 Kontext** 模型支持强大的图像编辑功能：

| 功能 | 说明 | 推荐模型 |
|------|------|----------|
| **局部编辑** | 修改图像中的特定元素，不影响其他部分 | flux-kontext-pro/max |
| **风格迁移** | 基于参考图像的风格生成新内容 | flux-kontext-pro/max |
| **角色一致性** | 在不同场景中保持角色特征一致 | flux-kontext-pro/max |
| **文本编辑** | 修改图像中的文字内容 | flux-kontext-max |
| **迭代编辑** | 基于前一次编辑结果继续修改 | flux-kontext-pro/max |

**编辑指令示例：**
- "将猫的颜色改为橙色"
- "添加一顶蓝色帽子"
- "将背景改为海滩"
- "去掉图中的汽车"
- "将文字改为'Hello World'"

### 方法2: 编程接口使用

#### 基本图像生成

```python
from bfl_image_generator import BFLImageGenerator

# 创建生成器实例
generator = BFLImageGenerator("your_api_key_here")

# 一键生成图像
success = generator.generate_image(
    prompt="一只在月光下的狼",
    model="flux-pro-1.1",
    aspect_ratio="16:9",
    output_filename="wolf_moonlight.jpg"
)

if success:
    print("图像生成成功!")
else:
    print("图像生成失败!")
```

#### 高级参数使用

```python
# 使用精确尺寸和高级参数
success = generator.generate_image(
    prompt="A majestic dragon flying over ancient mountains",
    model="flux-pro-1.1-ultra",
    width=1024,
    height=768,
    steps=40,
    seed=12345,
    prompt_upsampling=True,
    safety_tolerance=5,
    raw=True,
    output_filename="dragon.jpg"
)
```

#### 批量生成

```python
# 批量生成多张图像
prompts = [
    "A serene lake at sunset",
    "A futuristic cityscape",
    "A magical forest with glowing plants"
]

results = generator.generate_batch_images(
    prompts=prompts,
    model="flux-dev",
    aspect_ratio="16:9",
    output_dir="batch_output",
    max_concurrent=3,
    steps=25
)

print(f"成功生成: {results['successful']}/{results['total']}")
```

#### 图像编辑

```python
# 编辑现有图像
success = generator.edit_image(
    input_image="original.jpg",
    prompt="将猫的颜色改为橙色",
    model="flux-kontext-pro",
    output_filename="edited_cat.jpg"
)
```

#### 风格迁移

```python
# 基于参考图像的风格生成新内容
success = generator.style_transfer(
    reference_image="van_gogh_style.jpg",
    prompt="一座现代摩天大楼",
    model="flux-kontext-pro",
    output_filename="van_gogh_building.jpg"
)
```

#### 以图生图

```python
# 使用图像作为输入进行生成
success = generator.generate_image(
    prompt="将这个夏日场景改为冬天雪景",
    image="summer_scene.jpg",
    model="flux-kontext-pro",
    output_filename="winter_scene.jpg"
)
```

#### 分步骤操作

```python
# 1. 提交生成请求
request_id = generator.submit_generation_request(
    prompt="科幻太空站",
    model="flux-kontext-pro",
    aspect_ratio="21:9"
)

if request_id:
    # 2. 等待生成结果
    image_url = generator.poll_for_result(request_id, max_wait_time=300)
    
    if image_url:
        # 3. 下载图像
        success = generator.download_image(image_url, "space_station.jpg")
```

## 运行示例

```bash
# 运行示例程序（需要设置环境变量 BFL_API_KEY）
python example.py
```

示例程序包含多个演示：
1. 基本图像生成
2. 图像编辑（基于生成的图像）
3. 风格迁移（使用参考图像）

### 命令行使用示例

```bash
# 基本生成
python bfl_image_generator.py "一只可爱的猫咪"

# 高质量生成
python bfl_image_generator.py "壮丽的山脉日出" --model flux-pro-1.1-ultra --width 1024 --height 768 --steps 50

# 批量生成
python bfl_image_generator.py "森林" "海洋" "沙漠" "雪山" --batch --output nature_scenes

# 可重现生成
python bfl_image_generator.py "机器人朋友" --seed 12345 --steps 30

# 图像编辑
python bfl_image_generator.py "给这只猫戴上一顶红色帽子" --input-image cat.jpg --edit

# 风格迁移
python bfl_image_generator.py "一座城堡" --input-image painting_style.jpg --style-transfer
```

## API 限制

- **并发限制**: 最多24个活跃任务（flux-kontext-max 限制6个）
- **积分系统**: 需要购买积分才能使用
- **URL有效期**: 生成的图像URL仅10分钟内有效

## 错误处理

程序会自动处理以下错误情况：

- `429` - 请求过于频繁
- `402` - 积分不足
- 网络连接错误
- 生成超时
- 下载失败

## 提示词建议

为了获得最佳效果，建议：

1. **详细描述**: 包含具体的视觉元素、风格、颜色等
2. **英文提示**: 英文提示词通常效果更好
3. **避免负面词汇**: 专注于描述想要的内容
4. **风格指定**: 可以指定艺术风格，如 "photorealistic", "anime style", "oil painting" 等

### 示例提示词

```
# 风景类
"A serene mountain lake at sunrise, with mist rising from the water and snow-capped peaks in the background, photorealistic style"

# 人物类
"Portrait of a wise old wizard with a long white beard, wearing a blue robe, fantasy art style"

# 抽象类
"Abstract geometric composition with vibrant colors and flowing shapes, modern art style"

# 动物类
"A majestic eagle soaring through cloudy skies, wings spread wide, dramatic lighting"
```

## 文件结构

```
.
├── bfl_image_generator.py  # 主程序文件
├── example.py             # 使用示例
├── requirements.txt       # 依赖文件
└── README.md             # 说明文档
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查密钥是否正确设置
   - 确认密钥在 BFL Dashboard 中有效

2. **积分不足**
   - 访问 https://api.us1.bfl.ai 充值积分

3. **生成超时**
   - 检查网络连接
   - 尝试使用更快的模型（如 flux-dev）

4. **下载失败**
   - 确保有写入权限
   - 检查磁盘空间

### 调试模式

如需查看详细的API响应，可以修改代码添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 许可证

本项目仅供学习和研究使用。使用 BFL API 需要遵守其服务条款。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具！ 