{% extends "base.html" %}

{% block title %}风格迁移 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-style{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/components/upload-component.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/components/common-form-styles.css') }}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-palette"></i> 风格迁移
                </h5>
            </div>
            <div class="card-body">
                <form id="styleForm" action="/api/style" method="POST" enctype="multipart/form-data">
                    <!-- 参考图像上传 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-upload"></i> 上传参考图像 *
                        </label>
                        <div class="modern-upload-area" id="uploadArea">
                            <input type="file" id="image" name="image" accept="image/*" required style="display: none;">
                            <div class="upload-content" id="uploadContent">
                                <div class="upload-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="upload-text">
                                    <h6 class="upload-title">拖拽参考图像到这里或点击上传</h6>
                                    <p class="upload-subtitle">上传您想要模仿风格的参考图像</p>
                                    <p class="upload-size">支持 JPG、PNG、GIF、WebP 格式</p>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="browseBtn">
                                    <i class="fas fa-folder-open"></i> 选择文件
                                </button>
                            </div>
                            <div class="upload-preview" style="display: none;">
                                <img class="preview-image" src="#" alt="预览图像">
                                <div class="preview-overlay">
                                    <div class="preview-info">
                                        <p class="file-name">filename.jpg</p>
                                        <p class="file-size">1.2 MB</p>
                                    </div>
                                    <div class="preview-actions">
                                        <button type="button" class="btn btn-sm btn-outline-light change-btn">
                                            <i class="fas fa-sync"></i> 更换
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-btn">
                                            <i class="fas fa-trash"></i> 移除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 内容描述 -->
                    <div class="mb-3">
                        <label for="prompt" class="form-label">
                            <i class="fas fa-pen"></i> 内容描述 *
                        </label>
                        <textarea class="form-control" id="prompt" name="prompt" rows="3" 
                                placeholder="请描述您想要生成的内容，支持中文或英文..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-lightbulb"></i> 提示：描述您想要生成的内容，支持中文输入并自动翻译
                        </div>
                        
                        <!-- 翻译按钮 -->
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-info btn-sm" id="translateBtn">
                                <i class="fas fa-language"></i> 翻译为英文
                            </button>
                            <span id="translateStatus" class="ms-2 text-muted" style="display: none;"></span>
                        </div>
                    </div>
                    
                    <!-- 翻译结果 -->
                    <div class="mb-3" id="translatedPromptArea" style="display: none;">
                        <label for="translatedPrompt" class="form-label">
                            <i class="fas fa-globe"></i> 英文内容描述 (可编辑)
                        </label>
                        <textarea class="form-control" id="translatedPrompt" name="translatedPrompt" rows="3" 
                                placeholder="翻译结果将显示在这里，您可以进行修改..."></textarea>
                        <div class="form-text">
                            <i class="fas fa-edit"></i> 您可以修改翻译结果以获得更精确的内容描述
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-success btn-sm" id="useTranslatedBtn">
                                <i class="fas fa-check"></i> 使用此英文描述
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="hideTranslatedBtn">
                                <i class="fas fa-times"></i> 隐藏翻译
                            </button>
                        </div>
                    </div>
                    
                    <!-- 模型选择 -->
                    <div class="mb-3">
                        <label for="model" class="form-label">
                            <i class="fas fa-cog"></i> 模型选择
                        </label>
                        <select class="form-select" id="model" name="model">
                            <option value="flux-kontext-pro" selected>FLUX Kontext Pro (推荐)</option>
                            <option value="flux-kontext-max">FLUX Kontext Max (最高质量)</option>
                        </select>
                        <div class="form-text">
                            风格迁移功能需要使用 Kontext 系列模型
                        </div>
                    </div>
                    
                    <!-- 高级参数 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h"></i> 高级参数
                                <button type="button" class="btn btn-sm btn-outline-secondary float-end" 
                                        data-bs-toggle="collapse" data-bs-target="#advancedParams">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="collapse" id="advancedParams">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="steps" class="form-label">扩散步数 (1-50)</label>
                                            <input type="number" class="form-control" id="steps" name="steps" 
                                                   min="1" max="50" placeholder="默认: 40">
                                            <div class="form-text">更高的步数通常产生更好的质量</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="seed" class="form-label">随机种子</label>
                                            <input type="number" class="form-control" id="seed" name="seed" 
                                                   placeholder="留空为随机">
                                            <div class="form-text">相同种子产生相同结果</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="guidance" class="form-label">引导强度</label>
                                            <input type="number" class="form-control" id="guidance" name="guidance" 
                                                   step="0.1" placeholder="默认值">
                                            <div class="form-text">控制风格迁移的强度</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="mt-4">
                        <!-- 测试模式和重置按钮 -->
                        <div class="row g-2 mb-3">
                            <div class="col-md-4">
                                <div class="form-check form-switch h-100 d-flex align-items-center">
                                    <input class="form-check-input" type="checkbox" id="testMode" name="testMode">
                                    <label class="form-check-label" for="testMode">
                                        <i class="fas fa-bug"></i> 测试模式（显示API请求内容）
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> 重置表单
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100" id="styleBtn">
                                    <i class="fas fa-palette"></i> 开始风格迁移
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 状态和结果显示 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 迁移状态
                </h5>
            </div>
            <div class="card-body">
                <div id="statusArea">
                    <div class="text-center text-muted">
                        <i class="fas fa-palette fa-3x mb-3"></i>
                        <p>上传参考图像并描述内容来开始风格迁移</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 风格迁移示例 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i> 内容描述示例
                </h6>
            </div>
            <div class="card-body">
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>建筑:</strong> 一座现代摩天大楼矗立在城市中心
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>自然:</strong> 一片宁静的森林，阳光透过树叶洒下
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>人物:</strong> 一位优雅的女士坐在咖啡厅里
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>动物:</strong> 一只威严的狮子站在草原上
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>风景:</strong> 壮丽的山脉和湖泊全景
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>抽象:</strong> 流动的色彩和几何形状组合
                </div>
            </div>
        </div>
        
        <!-- 风格迁移说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info"></i> 风格迁移说明
                </h6>
            </div>
            <div class="card-body">
                <h6>什么是风格迁移？</h6>
                <p class="small text-muted mb-3">
                    风格迁移是将参考图像的艺术风格（如色彩、笔触、纹理等）应用到新内容上的技术。
                </p>
                
                <h6>如何使用？</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 上传风格参考图像（如油画、水彩画等）</li>
                    <li><i class="fas fa-check text-success"></i> 描述您想要生成的内容</li>
                    <li><i class="fas fa-check text-success"></i> 系统会生成具有参考风格的新图像</li>
                </ul>
                
                <h6>适用场景：</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-star text-warning"></i> 艺术创作</li>
                    <li><i class="fas fa-star text-warning"></i> 设计灵感</li>
                    <li><i class="fas fa-star text-warning"></i> 风格探索</li>
                    <li><i class="fas fa-star text-warning"></i> 创意设计</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="module" src="{{ url_for('static', filename='js/pages/style.js') }}"></script>
{% endblock %} 