/* 通用表单组件样式 */

/* 主题色彩变量，通过 CSS 变量实现主题切换 */
:root {
    --theme-color-rgb: 99, 102, 241; /* 默认紫色 */
    --border-radius-sm: 8px;
}

/* 示例提示词样式 */
.example-prompt {
    padding: 14px 18px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(16, 185, 129, 0.03) 100%);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid var(--success-color);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.example-prompt:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    transform: translateX(8px) translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

/* 状态卡片样式 */
.status-card {
    border-left: 4px solid var(--success-color);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 6px var(--shadow-light), 0 1px 3px var(--shadow-medium);
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--theme-color-rgb, 99, 102, 241), 0.1);
}

/* 状态徽章样式 */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-queued {
    background-color: #fef3c7;
    color: #92400e;
}

.status-processing {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-completed {
    background-color: #d1fae5;
    color: #065f46;
}

.status-failed {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 现代化按钮样式 */
.modern-btn {
    background: linear-gradient(135deg, rgb(var(--theme-color-rgb)) 0%, rgba(var(--theme-color-rgb), 0.8) 100%);
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(var(--theme-color-rgb), 0.2);
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--theme-color-rgb), 0.3);
}

/* 现代化文本框样式 */
.modern-textarea {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.modern-textarea:focus {
    border-color: rgb(var(--theme-color-rgb));
    box-shadow: 0 0 0 3px rgba(var(--theme-color-rgb), 0.1);
    background: white;
}

/* 翻译相关样式 */
.translate-status {
    margin-top: 8px;
    font-size: 0.875rem;
}

.translated-result {
    background: rgba(var(--theme-color-rgb), 0.02);
    border: 1px solid rgba(var(--theme-color-rgb), 0.1);
    border-radius: 8px;
    padding: 16px;
}

.translated-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

/* 结果图像样式 */
.result-image {
    max-width: 100%;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.result-image:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* 主题特定的渐变背景 */
.bg-emerald-gradient {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.bg-orange-gradient {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background: rgba(var(--theme-color-rgb), 0.1);
}

.progress-bar {
    background: linear-gradient(135deg, rgb(var(--theme-color-rgb)) 0%, rgba(var(--theme-color-rgb), 0.8) 100%);
    border-radius: 4px;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .example-prompt {
        padding: 12px 16px;
        margin-bottom: 8px;
    }
    
    .modern-btn {
        padding: 12px 24px;
    }
    
    .translated-actions {
        flex-direction: column;
    }
}

/* 翻译按钮样式 */
.translate-btn {
    border: 2px solid var(--accent-color, #06b6d4);
    color: var(--accent-color, #06b6d4);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.translate-btn:hover {
    background: var(--accent-color, #06b6d4);
    border-color: var(--accent-color, #06b6d4);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.use-translated-btn {
    background: var(--success-color, #10b981);
    border: none;
    font-weight: 500;
    color: white;
}

.use-translated-btn:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 翻译结果区域 */
.translated-prompt-area {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(6, 182, 212, 0.03) 100%);
    border-radius: var(--border-radius-sm, 8px);
    padding: 16px;
    border: 1px solid rgba(6, 182, 212, 0.1);
    backdrop-filter: blur(10px);
}

/* 主要操作按钮增强 */
.primary-action-btn {
    font-size: 1.1rem;
    padding: 14px 32px;
    font-weight: 600;
    box-shadow: 0 4px 14px rgba(var(--theme-color-rgb, 99, 102, 241), 0.3);
    background: linear-gradient(135deg, rgb(var(--theme-color-rgb, 99, 102, 241)) 0%, rgb(var(--theme-color-rgb-dark, 79, 70, 229)) 100%);
    border: none;
    transition: all 0.3s ease;
}

.primary-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(var(--theme-color-rgb, 99, 102, 241), 0.4);
    background: linear-gradient(135deg, rgb(var(--theme-color-rgb-dark, 79, 70, 229)) 0%, rgb(var(--theme-color-rgb-darker, 67, 56, 202)) 100%);
}

.primary-action-btn:disabled {
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0.7;
}

/* 高级参数区域 */
.advanced-params .card-body {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
    backdrop-filter: blur(10px);
}

/* 模型选择器 */
.model-select-enhanced {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.model-select-enhanced:focus {
    background: white;
    border-color: rgb(var(--theme-color-rgb, 99, 102, 241));
    box-shadow: 0 0 0 3px rgba(var(--theme-color-rgb, 99, 102, 241), 0.1);
}

/* 翻译状态文本 */
.translate-status.text-success {
    color: var(--success-color, #10b981) !important;
}

.translate-status.text-danger {
    color: var(--danger-color, #ef4444) !important;
}

/* 测试模式样式 */
.test-mode-switch {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: var(--border-radius-sm, 8px);
    padding: 12px 16px;
    margin-bottom: 12px;
}

.test-result-section {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.02) 100%);
}

.test-content-box {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 200px;
    overflow-y: auto;
}

/* 主题色彩变量定义 */
.theme-edit {
    --theme-color-rgb: 16, 185, 129;
    --theme-color-rgb-dark: 5, 150, 105;
    --theme-color-rgb-darker: 4, 120, 87;
}

.theme-style {
    --theme-color-rgb: 245, 158, 11;
    --theme-color-rgb-dark: 217, 119, 6;
    --theme-color-rgb-darker: 180, 83, 9;
}

.theme-generate {
    --theme-color-rgb: 99, 102, 241;
    --theme-color-rgb-dark: 79, 70, 229;
    --theme-color-rgb-darker: 67, 56, 202;
} 