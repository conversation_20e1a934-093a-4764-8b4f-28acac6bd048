# BLK Web 开发者指南

## 快速开始

### 环境准备

#### 1. 系统要求
- **操作系统**: Windows 10/11, macOS, Linux
- **Python**: 3.8+ (推荐 3.11+)
- **Node.js**: 16+ (用于前端工具，可选)
- **Git**: 最新版本

#### 2. 依赖安装
```bash
# 克隆项目
git clone <repository-url>
cd BLK/web

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r backend/requirements_web.txt
```

#### 3. 配置文件
创建 `backend/config/.env` 文件：
```env
# DeepSeek API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# Ollama 配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=qwen2.5:7b
OLLAMA_NO_THINK=true

# BFL API 配置
BFL_API_KEY=your_bfl_api_key_here

# 应用配置
FLASK_ENV=development
FLASK_DEBUG=true
```

#### 4. 启动应用
```bash
# 开发模式启动
python run_web.py

# 访问应用
# http://localhost:5000
```

## 项目结构详解

### 核心目录说明

```
backend/
├── config/              # 配置管理
│   ├── __init__.py
│   └── app_config.py    # 应用配置类
├── routes/              # 路由定义
│   ├── image_routes.py  # 图像处理API
│   ├── page_routes.py   # 页面路由
│   └── translation_routes.py # 翻译API
├── services/            # 业务服务
│   ├── image_service.py # 图像处理服务
│   └── task_service.py  # 任务管理服务
├── utils/               # 工具函数
│   ├── common_utils.py  # 通用工具
│   ├── helpers.py       # 辅助函数
│   ├── image_preprocessor.py # 图像预处理
│   └── validators.py    # 数据验证
├── static/              # 静态资源
│   ├── css/            # 样式文件
│   ├── js/             # JavaScript文件
│   └── img/            # 图片资源
├── templates/           # HTML模板
├── uploads/             # 上传文件
└── outputs/             # 输出文件
```

### 关键文件说明

#### 1. 应用入口 (`run_web.py`)
```python
# 应用启动文件
# 负责创建Flask应用实例和启动服务
```

#### 2. 配置管理 (`backend/config/app_config.py`)
```python
class AppConfig:
    """应用配置类
    
    管理所有应用配置，包括：
    - API密钥和端点
    - 文件路径配置
    - 服务参数设置
    """
```

#### 3. 路由定义 (`backend/routes/`)
- **image_routes.py**: 图像处理相关API路由
- **page_routes.py**: 页面渲染路由
- **translation_routes.py**: 翻译服务API路由

#### 4. 业务服务 (`backend/services/`)
- **image_service.py**: 图像处理核心业务逻辑
- **task_service.py**: 异步任务管理服务

## 开发规范

### 1. 代码风格

#### Python 代码规范
```python
# 遵循 PEP 8 规范
# 使用 4 个空格缩进
# 行长度不超过 88 字符

def process_image(image_path: str, options: dict) -> dict:
    """
    处理图像文件
    
    Args:
        image_path (str): 图像文件路径
        options (dict): 处理选项
        
    Returns:
        dict: 处理结果
        
    Raises:
        ValueError: 当图像路径无效时
        ProcessingError: 当处理失败时
    """
    # 实现逻辑
    pass
```

#### JavaScript 代码规范
```javascript
// 使用 ES6+ 语法
// 使用 2 个空格缩进
// 使用 camelCase 命名

/**
 * 处理文件上传
 * @param {File} file - 要上传的文件
 * @param {Object} options - 上传选项
 * @returns {Promise<Object>} 上传结果
 */
async function uploadFile(file, options = {}) {
  // 实现逻辑
}
```

### 2. 文件命名规范

#### Python 文件
- 使用小写字母和下划线：`image_service.py`
- 类名使用 PascalCase：`ImageService`
- 函数名使用 snake_case：`process_image`

#### JavaScript 文件
- 使用小写字母和连字符：`api-client.js`
- 类名使用 PascalCase：`TaskProcessor`
- 函数名使用 camelCase：`processTask`

#### HTML/CSS 文件
- 使用小写字母和连字符：`image-gallery.html`
- CSS 类名使用连字符：`.image-preview`

### 3. 注释规范

#### 函数注释
```python
def create_task(task_type: str, params: dict) -> str:
    """
    创建新任务
    
    Args:
        task_type (str): 任务类型 ('generate', 'edit', 'style')
        params (dict): 任务参数
        
    Returns:
        str: 任务ID
        
    Example:
        >>> task_id = create_task('generate', {'prompt': 'a cat'})
        >>> print(task_id)
        'uuid-string'
    """
```

#### 类注释
```python
class TaskService:
    """
    任务管理服务
    
    负责管理异步任务的生命周期，包括：
    - 任务创建和状态管理
    - 并发任务处理
    - 任务结果存储
    - 任务清理和维护
    
    Attributes:
        tasks (dict): 任务存储字典
        lock (threading.Lock): 线程锁
    """
```

## 开发工作流

### 1. 功能开发流程

#### 步骤 1: 需求分析
1. 理解功能需求
2. 设计API接口
3. 确定数据流
4. 评估技术难点

#### 步骤 2: 后端开发
```bash
# 1. 创建服务类
touch backend/services/new_service.py

# 2. 实现业务逻辑
# 3. 添加路由
touch backend/routes/new_routes.py

# 4. 编写单元测试
touch tests/test_new_service.py
```

#### 步骤 3: 前端开发
```bash
# 1. 创建页面模板
touch backend/templates/new_page.html

# 2. 添加页面脚本
touch backend/static/js/pages/new-page.js

# 3. 添加样式文件
touch backend/static/css/pages/new-page.css
```

#### 步骤 4: 集成测试
1. 测试API接口
2. 测试前端交互
3. 测试错误处理
4. 性能测试

### 2. Git 工作流

#### 分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/image-generation    # 图像生成功能
feature/translation-service # 翻译服务功能

# 修复分支
hotfix/critical-bug-fix     # 紧急修复
```

#### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <description>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(image): add image generation API
fix(translation): fix deepseek service timeout
docs(readme): update installation guide
```

## 调试指南

### 1. 后端调试

#### 使用 Flask 调试模式
```python
# 在 run_web.py 中启用调试
app.run(debug=True, host='0.0.0.0', port=5000)
```

#### 日志调试
```python
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 使用日志
logger.debug(f"Processing image: {image_path}")
logger.info(f"Task created: {task_id}")
logger.error(f"Error occurred: {str(e)}")
```

#### 断点调试
```python
# 使用 pdb 调试器
import pdb
pdb.set_trace()  # 设置断点

# 或使用 breakpoint() (Python 3.7+)
breakpoint()
```

### 2. 前端调试

#### 浏览器开发者工具
```javascript
// 控制台调试
console.log('Debug info:', data);
console.error('Error occurred:', error);
console.table(arrayData);  // 表格形式显示数组

// 断点调试
debugger;  // 设置断点
```

#### 网络请求调试
```javascript
// 监控 API 请求
fetch('/api/generate', {
  method: 'POST',
  body: formData
})
.then(response => {
  console.log('Response status:', response.status);
  return response.json();
})
.then(data => {
  console.log('Response data:', data);
})
.catch(error => {
  console.error('Request failed:', error);
});
```

### 3. 常见问题排查

#### API 调用失败
```python
# 检查点
1. API 密钥是否正确
2. 网络连接是否正常
3. 请求参数是否有效
4. 服务是否可用

# 调试代码
try:
    response = requests.post(url, json=data, timeout=30)
    response.raise_for_status()
except requests.exceptions.Timeout:
    logger.error("Request timeout")
except requests.exceptions.RequestException as e:
    logger.error(f"Request failed: {e}")
```

#### 文件上传问题
```python
# 检查点
1. 文件大小是否超限
2. 文件类型是否支持
3. 上传目录权限
4. 磁盘空间

# 调试代码
if file.content_length > MAX_FILE_SIZE:
    raise ValueError("File too large")
    
if not allowed_file(file.filename):
    raise ValueError("File type not supported")
```

## 测试指南

### 1. 单元测试

#### 测试框架设置
```python
# tests/conftest.py
import pytest
from backend.app_new import create_app

@pytest.fixture
def app():
    app = create_app(testing=True)
    return app

@pytest.fixture
def client(app):
    return app.test_client()
```

#### 服务测试示例
```python
# tests/test_image_service.py
import pytest
from backend.services.image_service import ImageService

class TestImageService:
    def test_create_generation_task(self):
        service = ImageService()
        task_id = service.create_generation_task(
            prompt="a beautiful landscape",
            style="realistic"
        )
        assert task_id is not None
        assert len(task_id) > 0
    
    def test_invalid_prompt(self):
        service = ImageService()
        with pytest.raises(ValueError):
            service.create_generation_task(prompt="")
```

#### API 测试示例
```python
# tests/test_api.py
def test_generate_image_api(client):
    response = client.post('/api/generate', data={
        'prompt': 'a cat',
        'style': 'realistic',
        'model': 'flux-pro'
    })
    assert response.status_code == 200
    data = response.get_json()
    assert 'task_id' in data
```

### 2. 集成测试

#### 端到端测试
```python
# tests/test_integration.py
def test_image_generation_workflow(client):
    # 1. 提交生成任务
    response = client.post('/api/generate', data={
        'prompt': 'test image',
        'style': 'realistic'
    })
    task_id = response.get_json()['task_id']
    
    # 2. 查询任务状态
    response = client.get(f'/api/task_status/{task_id}')
    assert response.status_code == 200
    
    # 3. 验证结果
    # (在实际测试中可能需要等待任务完成)
```

### 3. 前端测试

#### JavaScript 单元测试
```javascript
// tests/js/test-api-client.js
describe('ApiClient', () => {
  test('should submit generation task', async () => {
    const mockResponse = { task_id: 'test-id' };
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      })
    );
    
    const result = await ApiClient.submitGenerationTask({
      prompt: 'test',
      style: 'realistic'
    });
    
    expect(result.task_id).toBe('test-id');
  });
});
```

## 部署指南

### 1. 开发环境部署

```bash
# 1. 启动依赖服务
# 启动 Ollama (如果使用)
ollama serve

# 2. 启动应用
python run_web.py

# 3. 访问应用
# http://localhost:5000
```

### 2. 生产环境部署

#### 使用 Gunicorn
```bash
# 安装 Gunicorn
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:5000 run_web:app
```

#### 使用 Docker
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements_web.txt .
RUN pip install -r requirements_web.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "run_web:app"]
```

```bash
# 构建和运行
docker build -t blk-web .
docker run -p 5000:5000 blk-web
```

## 性能优化

### 1. 后端优化

#### 异步处理
```python
# 使用线程池处理长时间任务
from concurrent.futures import ThreadPoolExecutor

executor = ThreadPoolExecutor(max_workers=4)

def process_image_async(task_id, params):
    future = executor.submit(process_image, params)
    # 存储 future 对象，用于后续状态查询
    task_futures[task_id] = future
```

#### 缓存优化
```python
# 使用 functools.lru_cache
from functools import lru_cache

@lru_cache(maxsize=128)
def get_translation(text, service):
    # 缓存翻译结果
    return translate_text(text, service)
```

### 2. 前端优化

#### 图片懒加载
```javascript
// 使用 Intersection Observer
const imageObserver = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const img = entry.target;
      img.src = img.dataset.src;
      imageObserver.unobserve(img);
    }
  });
});

// 观察图片元素
document.querySelectorAll('img[data-src]').forEach(img => {
  imageObserver.observe(img);
});
```

#### 防抖处理
```javascript
// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 使用防抖
const debouncedSearch = debounce(searchFunction, 300);
```

## 故障排除

### 1. 常见错误及解决方案

#### 模块导入错误
```python
# 错误: ModuleNotFoundError: No module named 'backend'
# 解决: 确保 PYTHONPATH 包含项目根目录
export PYTHONPATH="${PYTHONPATH}:/path/to/BLK/web"
```

#### API 连接超时
```python
# 增加超时时间和重试机制
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

session = requests.Session()
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504]
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("http://", adapter)
session.mount("https://", adapter)
```

#### 文件权限问题
```bash
# 确保上传和输出目录有正确权限
chmod 755 backend/uploads
chmod 755 backend/outputs
```

### 2. 日志分析

#### 结构化日志
```python
import logging
import json

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': self.formatTime(record),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName
        }
        return json.dumps(log_entry)

# 配置日志
handler = logging.StreamHandler()
handler.setFormatter(JSONFormatter())
logger = logging.getLogger()
logger.addHandler(handler)
```

## 贡献指南

### 1. 代码贡献流程

1. **Fork 项目**
2. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```
3. **开发和测试**
4. **提交代码**
   ```bash
   git commit -m "feat: add new feature"
   ```
5. **推送分支**
   ```bash
   git push origin feature/new-feature
   ```
6. **创建 Pull Request**

### 2. 代码审查标准

- 代码符合项目规范
- 包含适当的测试
- 文档更新完整
- 无明显性能问题
- 安全性考虑充分

### 3. 问题报告

使用 GitHub Issues 报告问题时，请包含：
- 问题描述
- 复现步骤
- 期望行为
- 实际行为
- 环境信息
- 相关日志

---

**开发者资源**:
- [Flask 官方文档](https://flask.palletsprojects.com/)
- [Bootstrap 文档](https://getbootstrap.com/docs/)
- [Python PEP 8](https://pep8.org/)
- [JavaScript MDN](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

**联系方式**:
- 项目维护者: BLK Team
- 技术支持: [GitHub Issues]()
- 文档更新: 欢迎提交 PR