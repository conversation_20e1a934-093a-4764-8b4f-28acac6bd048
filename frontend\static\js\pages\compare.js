let currentImageData = null;
let currentMode = 'preprocess'; // 'preprocess' 或 'manual'
let compareWidget = null;

// 标签切换功能
function initTabSwitching() {
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            
            // 更新按钮状态
            document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 重置状态
            resetAllStates();
            currentMode = tabName;
        });
    });
}

// 文件预览功能
function setupFilePreview(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    
    input.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `<img src="${e.target.result}" alt="预览图">`;
                preview.classList.add('show');
            };
            reader.readAsDataURL(this.files[0]);
        }
    });
}

// 显示对比组件
function showCompareWidget(beforeUrl, afterUrl, beforeLabel = '原图', afterLabel = '处理后') {
    const container = document.getElementById('compareWidgetContainer');
    container.style.display = 'block';
    document.getElementById('actionArea').style.display = 'flex';
    
    // 销毁旧实例
    if (compareWidget) {
        compareWidget.destroy();
    }

    // 创建新实例
    compareWidget = new BFLCompareWidget('compareWidgetContainer', {
        beforeUrl: beforeUrl,
        afterUrl: afterUrl,
        beforeLabel: beforeLabel,
        afterLabel: afterLabel,
        onChange: (percent) => {
            // 可以在这里添加拖动时的额外逻辑
        }
    });
}

// 重置所有状态
function resetAllStates() {
    // 销毁对比组件
    if (compareWidget) {
        compareWidget.destroy();
        compareWidget = null;
    }

    document.getElementById('statusArea').style.display = 'none';
    document.getElementById('compareWidgetContainer').style.display = 'none';
    document.getElementById('infoArea').style.display = 'none';
    document.getElementById('actionArea').style.display = 'none';

    // 重置表单
    document.getElementById('preprocessForm').reset();
    document.getElementById('manualCompareForm').reset();

    // 清除预览
    document.getElementById('leftPreview').classList.remove('show');
    document.getElementById('rightPreview').classList.remove('show');
    document.getElementById('leftPreview').innerHTML = '';
    document.getElementById('rightPreview').innerHTML = '';

    currentImageData = null;
}

// 处理预处理表单提交
function handlePreprocessSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const statusArea = document.getElementById('statusArea');
    const statusText = document.getElementById('statusText');
    
    // 显示加载状态
    statusArea.style.display = 'block';
    statusText.textContent = '正在处理中...';
    
    fetch('/api/preprocess', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentImageData = data;
            
            // 更新信息显示
            document.getElementById('leftImageInfo').textContent = data.original_info;
            document.getElementById('rightImageInfo').textContent = data.processed_info;
            document.getElementById('aspectRatio').textContent = data.target_ratio;
            document.getElementById('cropInfo').textContent = data.crop_info;
            document.getElementById('ratioChange').textContent = data.ratio_change;
            
            document.getElementById('preprocessInfo').style.display = 'block';
            document.getElementById('ratioChangeInfo').style.display = 'block';
            document.getElementById('infoArea').style.display = 'block';
            
            // 显示对比组件
            showCompareWidget(data.original_url, data.processed_url);
            
            // 隐藏状态
            statusArea.style.display = 'none';
        } else {
            throw new Error(data.message || '处理失败');
        }
    })
    .catch(error => {
        statusText.textContent = '处理失败: ' + error.message;
        setTimeout(() => {
            statusArea.style.display = 'none';
        }, 3000);
    });
}

// 处理手动对比表单提交
function handleManualSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const statusArea = document.getElementById('statusArea');
    const statusText = document.getElementById('statusText');
    
    // 显示加载状态
    statusArea.style.display = 'block';
    statusText.textContent = '正在处理中...';
    
    fetch('/api/manual_compare', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentImageData = data;
            
            // 更新信息显示
            document.getElementById('leftImageInfo').textContent = data.left_info;
            document.getElementById('rightImageInfo').textContent = data.right_info;
            document.getElementById('preprocessInfo').style.display = 'none';
            document.getElementById('ratioChangeInfo').style.display = 'none';
            document.getElementById('infoArea').style.display = 'block';
            
            // 显示对比组件
            showCompareWidget(data.left_url, data.right_url, '左侧图像', '右侧图像');
            
            // 隐藏状态
            statusArea.style.display = 'none';
        } else {
            throw new Error(data.message || '处理失败');
        }
    })
    .catch(error => {
        statusText.textContent = '处理失败: ' + error.message;
        setTimeout(() => {
            statusArea.style.display = 'none';
        }, 3000);
    });
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 初始化标签切换
    initTabSwitching();
    
    // 设置文件预览
    setupFilePreview('leftImage', 'leftPreview');
    setupFilePreview('rightImage', 'rightPreview');
    
    // 绑定表单提交事件
    document.getElementById('preprocessForm').addEventListener('submit', handlePreprocessSubmit);
    document.getElementById('manualCompareForm').addEventListener('submit', handleManualSubmit);
    
    // 新对比按钮
    document.getElementById('newCompare').addEventListener('click', resetAllStates);
});

// 图片对比页面的主要功能
class ImageCompareManager {
    constructor() {
        this.initElements();
        this.initEvents();
        this.preprocessImage = null;
        this.manualImage1 = null;
        this.manualImage2 = null;
    }

    initElements() {
        // 获取DOM元素
        this.preprocessFile = document.getElementById('preprocess-file');
        this.manualFile1 = document.getElementById('manual-file-1');
        this.manualFile2 = document.getElementById('manual-file-2');
        this.preprocessCompare = document.getElementById('preprocess-compare');
        this.manualCompare = document.getElementById('manual-compare');
        this.statusArea = document.getElementById('statusArea');
        this.statusText = document.getElementById('statusText');
        this.infoArea = document.getElementById('infoArea');
        this.actionArea = document.getElementById('actionArea');
        
        // 标签页元素
        this.tabs = document.querySelectorAll('.tab-btn');
        this.contents = document.querySelectorAll('.tab-content');
    }

    initEvents() {
        // 文件上传事件
        this.preprocessFile.addEventListener('change', (e) => this.handlePreprocessUpload(e));
        this.manualFile1.addEventListener('change', (e) => this.handleManualUpload(e, 1));
        this.manualFile2.addEventListener('change', (e) => this.handleManualUpload(e, 2));
        
        // 标签切换事件
        this.tabs.forEach(tab => {
            tab.addEventListener('click', () => this.handleTabChange(tab));
        });
        
        // 新对比按钮事件
        const newCompareBtn = document.getElementById('newCompare');
        if (newCompareBtn) {
            newCompareBtn.addEventListener('click', () => this.resetCompare());
        }
    }

    async handlePreprocessUpload(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        try {
            this.showStatus('正在处理中...');
            
            // 显示原图
            const beforeUrl = URL.createObjectURL(file);
            this.updateCompareImage(this.preprocessCompare, beforeUrl, 'before');
            
            // 调用预处理API
            const formData = new FormData();
            formData.append('image', file);
            
            const response = await fetch('/api/preprocess', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) throw new Error('预处理失败');
            
            const result = await response.json();
            const afterUrl = result.processed_image_url;
            
            // 显示处理后的图片
            this.updateCompareImage(this.preprocessCompare, afterUrl, 'after');
            
            // 显示对比组件和操作区
            this.preprocessCompare.style.display = 'block';
            this.showInfo(file.name, result);
            this.actionArea.style.display = 'flex';
            this.hideStatus();
            
        } catch (error) {
            console.error('预处理失败:', error);
            this.showStatus('处理失败，请重试', true);
        }
    }

    handleManualUpload(e, imageNum) {
        const file = e.target.files[0];
        if (!file) return;
        
        const imageUrl = URL.createObjectURL(file);
        
        if (imageNum === 1) {
            this.manualImage1 = imageUrl;
            this.updateCompareImage(this.manualCompare, imageUrl, 'before');
        } else {
            this.manualImage2 = imageUrl;
            this.updateCompareImage(this.manualCompare, imageUrl, 'after');
        }
        
        if (this.manualImage1 && this.manualImage2) {
            this.manualCompare.style.display = 'block';
            this.actionArea.style.display = 'flex';
            this.showInfo(
                this.manualFile1.files[0].name,
                { processed_image: this.manualFile2.files[0].name }
            );
        }
    }

    handleTabChange(tab) {
        const target = tab.dataset.tab;
        
        this.tabs.forEach(t => t.classList.remove('active'));
        this.contents.forEach(c => c.classList.remove('active'));
        
        tab.classList.add('active');
        document.getElementById(target).classList.add('active');
    }

    updateCompareImage(container, imageUrl, type) {
        const img = container.querySelector(`.bfl-compare__${type}`);
        const downloadBtn = container.querySelector(`.bfl-compare__actions a:${type === 'before' ? 'first' : 'last'}-child`);
        
        img.style.backgroundImage = `url('${imageUrl}')`;
        downloadBtn.href = imageUrl;
    }

    showStatus(message, isError = false) {
        this.statusArea.style.display = 'block';
        this.statusText.textContent = message;
        if (isError) {
            this.statusText.style.color = '#dc3545';
        } else {
            this.statusText.style.color = 'white';
        }
    }

    hideStatus() {
        this.statusArea.style.display = 'none';
    }

    showInfo(originalName, result) {
        this.infoArea.style.display = 'block';
        document.getElementById('leftImageInfo').textContent = originalName;
        document.getElementById('rightImageInfo').textContent = result.processed_image;
        
        if (result.aspect_ratio) {
            document.getElementById('preprocessInfo').style.display = 'flex';
            document.getElementById('aspectRatio').textContent = result.aspect_ratio;
            document.getElementById('cropInfo').textContent = result.crop_info || '无需裁剪';
        }
    }

    resetCompare() {
        // 重置所有状态
        this.preprocessFile.value = '';
        this.manualFile1.value = '';
        this.manualFile2.value = '';
        this.preprocessCompare.style.display = 'none';
        this.manualCompare.style.display = 'none';
        this.statusArea.style.display = 'none';
        this.infoArea.style.display = 'none';
        this.actionArea.style.display = 'none';
        
        // 重置图片URL
        this.preprocessImage = null;
        this.manualImage1 = null;
        this.manualImage2 = null;
        
        // 切换到第一个标签
        this.tabs[0].click();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.compareManager = new ImageCompareManager();
}); 