"""简化的认证路由
设计原则：
1. 简化Token策略 - 只使用httpOnly cookie存储session
2. 统一错误处理 - 标准化的响应格式
3. 职责单一 - 每个路由只做一件事
4. 安全优先 - 防止常见的认证漏洞
"""

from flask import Blueprint, request, jsonify, session, current_app
from werkzeug.security import check_password_hash
from datetime import datetime, timedelta
import secrets

from backend.models.database import db
from backend.models.user import User
from backend.utils.helpers import create_response

# 创建认证蓝图
auth_bp = Blueprint('simple_auth', __name__, url_prefix='/api/auth')


def serialize_user(user):
    """序列化用户信息
    
    Args:
        user: User对象
        
    Returns:
        dict: 用户信息字典
    """
    if not user:
        return None
        
    return {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'phone': user.phone,
        'user_type': user.user_type.value if user.user_type else 'basic',
        'is_active': user.is_active,
        'is_verified': user.is_verified,
        'total_credits': user.total_credits,
        'used_credits': user.used_credits,
        'available_credits': user.total_credits - user.used_credits,
        'last_login': user.last_login.isoformat() if user.last_login else None
    }


def find_user_by_identifier(identifier):
    """根据标识符查找用户
    
    Args:
        identifier (str): 用户名、邮箱或手机号
        
    Returns:
        User: 用户对象或None
    """
    # 尝试邮箱
    if '@' in identifier:
        return User.query.filter_by(email=identifier).first()
    
    # 尝试手机号（纯数字或包含+号）
    if identifier.isdigit() or '+' in identifier:
        return User.query.filter_by(phone=identifier).first()
    
    # 尝试用户名
    return User.query.filter_by(username=identifier).first()


@auth_bp.route('/login', methods=['POST'])
def login():
    """用户登录
    
    Returns:
        JSON: 登录结果
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(False, '请求数据格式错误'), 400
            
        identifier = data.get('identifier', '').strip()
        password = data.get('password', '')
        remember_me = data.get('remember_me', False)
        
        # 基本验证
        if not identifier or not password:
            return jsonify(create_response(False, '请填写登录信息和密码')), 400
            
        # 查找用户
        user = find_user_by_identifier(identifier)
        if not user:
            return jsonify(create_response(False, '用户不存在')), 401
            
        # 验证密码
        if not user.check_password(password):
            return jsonify(create_response(False, '密码错误')), 401
            
        # 检查用户状态
        if not user.is_active:
            return jsonify(create_response(False, '账户已被禁用')), 401
            
        # 更新登录信息
        user.last_login = datetime.utcnow()
        user.reset_daily_usage()  # 重置每日使用次数
        db.session.commit()
        
        # 设置session
        session.permanent = remember_me
        session['user_id'] = user.id
        session['login_time'] = datetime.utcnow().isoformat()
        
        # 设置session过期时间
        if remember_me:
            current_app.permanent_session_lifetime = timedelta(days=30)
        else:
            current_app.permanent_session_lifetime = timedelta(hours=24)
            
        current_app.logger.info(f'用户登录成功: {user.username} (ID: {user.id})')
        
        return jsonify(create_response(
            True, 
            '登录成功',
            {
                'user': serialize_user(user)
            }
        )), 200
        
    except Exception as e:
        current_app.logger.error(f'登录异常: {e}')
        return jsonify(create_response(False, '登录失败，请重试')), 500


@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出
    
    Returns:
        JSON: 登出结果
    """
    try:
        user_id = session.get('user_id')
        if user_id:
            current_app.logger.info(f'用户登出: ID {user_id}')
            
        # 清除session
        session.clear()
        
        return jsonify(create_response(True, '登出成功')), 200
        
    except Exception as e:
        current_app.logger.error(f'登出异常: {e}')
        return jsonify(create_response(False, '登出失败')), 500


@auth_bp.route('/status', methods=['GET'])
def get_auth_status():
    """获取认证状态
    
    Returns:
        JSON: 认证状态和用户信息
    """
    try:
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify(create_response(False, '未登录')), 401
            
        # 查找用户
        user = User.query.filter_by(id=user_id).first()
        if not user or not user.is_active:
            # 用户不存在或被禁用，清除session
            session.clear()
            return jsonify(create_response(False, '用户状态异常')), 401
            
        return jsonify(create_response(
            True,
            '已登录',
            {
                'user': serialize_user(user)
            }
        )), 200
        
    except Exception as e:
        current_app.logger.error(f'认证状态检查异常: {e}')
        return jsonify(create_response(False, '状态检查失败')), 500


def require_auth(f):
    """认证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        function: 装饰后的函数
    """
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify(create_response(False, '请先登录')), 401
            
        # 查找用户并设置到request context
        user = User.query.filter_by(id=user_id).first()
        if not user or not user.is_active:
            session.clear()
            return jsonify(create_response(False, '用户状态异常')), 401
            
        # 将用户对象添加到request context
        request.current_user = user
        
        return f(*args, **kwargs)
        
    return decorated_function


def get_current_user():
    """获取当前登录用户
    
    Returns:
        User: 当前用户对象或None
    """
    return getattr(request, 'current_user', None)


def require_admin(f):
    """管理员权限装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        function: 装饰后的函数
    """
    from functools import wraps
    from backend.models.user import UserType
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = session.get('user_id')
        
        if not user_id:
            return jsonify(create_response(False, '请先登录')), 401
            
        # 查找用户并验证权限
        user = User.query.filter_by(id=user_id).first()
        if not user or not user.is_active:
            session.clear()
            return jsonify(create_response(False, '用户状态异常')), 401
            
        # 检查管理员权限
        if user.user_type != UserType.enterprise:
            return jsonify(create_response(False, '需要管理员权限')), 403
            
        # 将用户对象添加到request context
        request.current_user = user
        
        return f(*args, **kwargs)
        
    return decorated_function