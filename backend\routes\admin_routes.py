"""
管理员功能路由
"""
from flask import Blueprint, request, jsonify

from backend.routes.simple_auth_routes import require_auth, get_current_user, require_admin
from backend.models.database import db
from backend.models.user import User, UserType
from backend.models.credit import CreditsTransaction, CreditPackage, TransactionType
from backend.models.task import Task
from backend.models.system_settings import SystemSetting
from backend.services.credit_service import credit_service
from backend.utils.helpers import create_response
from datetime import datetime, timedelta
import uuid

# 创建管理员API路由蓝图
admin_bp = Blueprint('admin_api', __name__, url_prefix='/api/admin')


@admin_bp.route('/dashboard', methods=['GET'])
@require_admin
def admin_dashboard():
    """管理员仪表板数据"""
    try:
        # 获取系统统计数据
        system_stats = credit_service.get_system_credit_stats()
        
        # 获取用户统计
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        verified_users = User.query.filter_by(is_verified=True).count()
        
        # 获取任务统计
        total_tasks = Task.query.count()
        completed_tasks = Task.query.filter_by(status='completed').count()
        failed_tasks = Task.query.filter_by(status='failed').count()
        
        # 最近任务（过去24小时）
        recent_time = datetime.utcnow() - timedelta(hours=24)
        recent_tasks = Task.query.filter(Task.created_at >= recent_time).count()
        
        # 积分套餐统计
        total_packages = CreditPackage.query.count()
        active_packages = CreditPackage.query.filter_by(is_active=True).count()

        # 获取最近活动（最近10条记录）
        recent_activities = []

        try:
            # 最近的任务
            recent_task_records = Task.query.order_by(Task.created_at.desc()).limit(5).all()
            for task in recent_task_records:
                user = User.query.get(task.user_id) if task.user_id else None
                username = user.username if user else '未知用户'
                task_type = task.type.value if hasattr(task.type, 'value') else str(task.type)
                recent_activities.append({
                    'type': 'task',
                    'description': f'用户 {username} 创建了{task_type}任务',
                    'status': task.status.value if hasattr(task.status, 'value') else str(task.status),
                    'time': task.created_at.isoformat() if task.created_at else None
                })
        except Exception as e:
            print(f"获取任务记录失败: {e}")

        try:
            # 最近的积分交易
            recent_credit_records = CreditsTransaction.query.order_by(CreditsTransaction.created_at.desc()).limit(5).all()
            for transaction in recent_credit_records:
                user = User.query.get(transaction.user_id) if transaction.user_id else None
                username = user.username if user else '未知用户'
                action = '获得' if transaction.credits_amount > 0 else '消费'
                recent_activities.append({
                    'type': 'credit',
                    'description': f'用户 {username} {action}了 {abs(transaction.credits_amount)} 积分',
                    'status': 'completed',
                    'time': transaction.created_at.isoformat() if transaction.created_at else None
                })
        except Exception as e:
            print(f"获取积分交易记录失败: {e}")

        # 按时间排序，取最近10条
        recent_activities.sort(key=lambda x: x['time'] or '', reverse=True)
        recent_activities = recent_activities[:10]

        response_data = {
            'system_stats': system_stats,
            'user_stats': {
                'total_users': total_users,
                'active_users': active_users,
                'verified_users': verified_users
            },
            'task_stats': {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'recent_tasks': recent_tasks,
                'success_rate': round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 2)
            },
            'package_stats': {
                'total_packages': total_packages,
                'active_packages': active_packages
            },
            'recent_activities': recent_activities
        }
        
        return jsonify({'success': True, 'data': response_data})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users', methods=['GET'])
@require_admin
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        user_type = request.args.get('user_type', '')
        status = request.args.get('status', '')
        
        query = User.query.filter_by(is_deleted=False)  # 排除已删除的用户
        
        # 搜索过滤
        if search:
            query = query.filter(
                (User.username.like(f'%{search}%')) |
                (User.email.like(f'%{search}%')) |
                (User.phone.like(f'%{search}%'))
            )
        
        # 用户类型过滤
        if user_type:
            try:
                query = query.filter_by(user_type=UserType(user_type))
            except ValueError:
                # 如果用户类型无效，忽略过滤
                pass

        # 状态过滤
        if status:
            if status == 'active':
                query = query.filter_by(is_active=True)
            elif status == 'inactive':
                query = query.filter_by(is_active=False)
            elif status == 'verified':
                query = query.filter_by(is_verified=True)
            elif status == 'unverified':
                query = query.filter_by(is_verified=False)
        
        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        users_data = []
        for user in pagination.items:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'user_type': user.user_type.value,
                'is_active': user.is_active,
                'is_verified': user.is_verified,
                'total_credits': user.total_credits,
                'used_credits': user.used_credits,
                'available_credits': user.available_credits,
                'daily_limit': user.daily_limit,
                'daily_used': user.daily_used,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None
            }
            users_data.append(user_data)
        
        return jsonify({
            'success': True,
            'users': users_data,
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users/<user_id>', methods=['GET'])
@require_admin
def get_user_detail(user_id):
    """获取用户详情"""
    try:
        user = User.query.filter_by(id=user_id, is_deleted=False).first()
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 获取用户的详细信息，包括积分交易记录
        user_data = user.to_dict(include_sensitive=False)

        # 获取最近的积分交易记录
        recent_transactions = CreditsTransaction.query.filter_by(user_id=user_id)\
            .order_by(CreditsTransaction.created_at.desc()).limit(10).all()

        user_data['recent_transactions'] = [
            {
                'id': t.id,
                'type': t.transaction_type.value,
                'amount': t.credits_amount,
                'description': t.description,
                'created_at': t.created_at.isoformat() if t.created_at else None
            } for t in recent_transactions
        ]

        # 获取用户的任务统计
        from backend.models.task import TaskStatus
        total_tasks = Task.query.filter_by(user_id=user_id).count()
        completed_tasks = Task.query.filter_by(user_id=user_id, status=TaskStatus.completed).count()

        user_data['task_stats'] = {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'success_rate': round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 2)
        }

        return jsonify({'success': True, 'user': user_data})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users/<user_id>', methods=['PUT'])
@require_admin
def update_user(user_id):
    """更新用户信息"""
    try:
        user = User.query.filter_by(id=user_id, is_deleted=False).first()
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        data = request.get_json()

        # 更新允许的字段
        if 'user_type' in data:
            try:
                new_type = UserType(data['user_type'])
                user.upgrade_user_type(new_type)
            except ValueError:
                return jsonify({'success': False, 'message': '无效的用户类型'}), 400

        if 'is_active' in data:
            user.is_active = bool(data['is_active'])

        if 'is_verified' in data:
            user.is_verified = bool(data['is_verified'])

        if 'total_credits' in data:
            new_available_credits = int(data['total_credits'])

            # 管理员设置的是最终可用积分，需要重新计算积分系统
            current_available = user.available_credits

            if new_available_credits != current_available:
                # 重置积分系统：设置新的总积分，清零已使用积分
                user.total_credits = new_available_credits
                user.used_credits = 0

                # 记录管理员操作到积分交易表
                from backend.models.credit import CreditsTransaction, TransactionType
                transaction = CreditsTransaction(
                    user_id=user_id,
                    transaction_type=TransactionType.admin,
                    credits_amount=new_available_credits - current_available,
                    balance_after=new_available_credits,
                    description=f'管理员设置用户积分为 {new_available_credits}（原可用积分：{current_available}）'
                )
                db.session.add(transaction)

        if 'daily_limit' in data:
            user.daily_limit = int(data['daily_limit'])

        user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({'success': True, 'message': '用户信息更新成功', 'user': user.to_dict()})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users/<user_id>', methods=['DELETE'])
@require_admin
def delete_user(user_id):
    """软删除用户（注销）"""
    try:
        user = User.query.filter_by(id=user_id, is_deleted=False).first()
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404

        # 检查是否是管理员用户
        if user.user_type == UserType.enterprise:
            return jsonify({'success': False, 'message': '不能删除管理员用户'}), 403

        # 执行软删除
        user.soft_delete()
        user.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({'success': True, 'message': '用户已注销'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users/<user_id>/credits', methods=['PUT'])
@require_admin
def adjust_user_credits():
    """调整用户积分"""
    try:
        user_id = request.view_args['user_id']
        data = request.get_json()
        
        adjustment = data.get('adjustment', 0)  # 正数增加，负数减少
        reason = data.get('reason', '')
        
        if adjustment == 0:
            return jsonify({'success': False, 'message': '调整数量不能为0'}), 400
        
        # 验证用户存在
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404
        
        # 调整积分
        result = credit_service.admin_adjust_credits(
            user_id, adjustment, reason or f'管理员调整积分: {adjustment}'
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': f'成功{"增加" if adjustment > 0 else "减少"} {abs(adjustment)} 积分',
                'adjustment': adjustment,
                'new_balance': result['new_balance'],
                'transaction_id': result['transaction_id']
            })
        else:
            return jsonify({'success': False, 'message': result['message']}), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/users/<user_id>/upgrade', methods=['PUT'])
@require_admin
def upgrade_user():
    """升级用户类型"""
    try:
        user_id = request.view_args['user_id']
        data = request.get_json()
        
        new_type = data.get('user_type')
        reason = data.get('reason', '')
        
        if not new_type:
            return jsonify({'success': False, 'message': '请指定新的用户类型'}), 400
        
        # 验证用户存在
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'message': '用户不存在'}), 404
        
        try:
            new_user_type = UserType(new_type)
        except ValueError:
            return jsonify({'success': False, 'message': '无效的用户类型'}), 400
        
        old_type = user.user_type
        user.upgrade_user_type(new_user_type)
        
        # 记录升级日志
        credit_service.add_credits(
            user_id, 0, 
            f'管理员升级用户类型: {old_type.value} -> {new_user_type.value}. 原因: {reason}',
            'admin'
        )
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'用户类型已从 {old_type.value} 升级到 {new_user_type.value}',
            'old_type': old_type.value,
            'new_type': new_user_type.value,
            'new_daily_limit': user.daily_limit
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/credit-packages', methods=['GET'])
@require_admin
def get_credit_packages():
    """获取积分套餐列表（管理员）"""
    try:
        packages = CreditPackage.query.order_by(CreditPackage.sort_order).all()
        
        packages_data = []
        for package in packages:
            package_data = package.to_dict()
            # 添加统计信息（如果需要）
            packages_data.append(package_data)
        
        return jsonify({
            'success': True,
            'packages': packages_data
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/credit-packages', methods=['POST'])
@require_admin
def create_credit_package():
    """创建积分套餐"""
    try:
        data = request.get_json()
        
        required_fields = ['name', 'credits_amount', 'price']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'{field} 是必填项'}), 400
        
        # 检查是否已存在同名套餐
        existing = CreditPackage.query.filter_by(name=data['name']).first()
        if existing:
            return jsonify({'success': False, 'message': '套餐名称已存在'}), 400
        
        package = CreditPackage(
            name=data['name'],
            credits_amount=data['credits_amount'],
            price=data['price'],
            currency=data.get('currency', 'USD'),
            discount_percentage=data.get('discount_percentage', 0),
            description=data.get('description', ''),
            features=data.get('features', []),
            sort_order=data.get('sort_order', 0),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(package)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '积分套餐创建成功',
            'package': package.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/credit-packages/<package_id>', methods=['PUT'])
@require_admin
def update_credit_package():
    """更新积分套餐"""
    try:
        package_id = request.view_args['package_id']
        data = request.get_json()
        
        package = CreditPackage.query.get(package_id)
        if not package:
            return jsonify({'success': False, 'message': '套餐不存在'}), 404
        
        # 更新字段
        updateable_fields = [
            'name', 'credits_amount', 'price', 'currency', 
            'discount_percentage', 'description', 'features', 
            'sort_order', 'is_active'
        ]
        
        for field in updateable_fields:
            if field in data:
                setattr(package, field, data[field])
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '积分套餐更新成功',
            'package': package.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/credit-packages/<package_id>', methods=['DELETE'])
@require_admin
def delete_credit_package():
    """删除积分套餐"""
    try:
        package_id = request.view_args['package_id']
        
        package = CreditPackage.query.get(package_id)
        if not package:
            return jsonify({'success': False, 'message': '套餐不存在'}), 404
        
        # 软删除 - 设为不活跃而不是真的删除
        package.is_active = False
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '积分套餐已停用'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/transactions', methods=['GET'])
@require_admin
def get_credit_transactions():
    """获取积分交易记录"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        user_id = request.args.get('user_id', '')
        transaction_type = request.args.get('transaction_type', '')
        
        query = CreditsTransaction.query
        
        # 用户过滤
        if user_id:
            query = query.filter_by(user_id=user_id)
        
        # 交易类型过滤
        if transaction_type:
            try:
                query = query.filter_by(transaction_type=TransactionType(transaction_type))
            except ValueError:
                pass
        
        # 按时间倒序排列
        query = query.order_by(CreditsTransaction.created_at.desc())
        
        # 分页
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        transactions_data = []
        for trans in pagination.items:
            trans_data = trans.to_dict()
            # 添加用户信息
            if trans.user:
                trans_data['user'] = {
                    'id': trans.user.id,
                    'username': trans.user.username,
                    'email': trans.user.email
                }
            transactions_data.append(trans_data)
        
        return jsonify({
            'success': True,
            'transactions': transactions_data,
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/system-settings', methods=['GET'])
@require_admin
def get_system_settings():
    """获取系统设置"""
    try:
        print("正在获取系统设置...")
        settings = SystemSetting.query.all()
        print(f"找到 {len(settings)} 个系统设置")

        settings_data = {}
        for setting in settings:
            settings_data[setting.key] = {
                'value': setting.value,
                'description': setting.description,
                'value_type': setting.setting_type
            }

        print(f"返回设置数据，包含 {len(settings_data)} 个设置")
        return jsonify({
            'success': True,
            'settings': settings_data
        })

    except Exception as e:
        print(f"获取系统设置失败: {e}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': str(e)}), 500


@admin_bp.route('/system-settings', methods=['PUT'])
@require_admin
def update_system_settings():
    """更新系统设置"""
    try:
        data = request.get_json()
        
        for key, value in data.items():
            setting = SystemSetting.query.filter_by(key=key).first()
            if setting:
                setting.value = str(value)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '系统设置更新成功'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500
