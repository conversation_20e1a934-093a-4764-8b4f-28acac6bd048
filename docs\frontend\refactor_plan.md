# BFL AI 图像工作室前端重构计划

## 已完成工作

### 1. CSS 架构优化
- [x] 将 `base.html` 中的内联样式移至 `app.css`
- [x] 保持页面特定样式在各自页面中
- [x] 统一主题色和变量定义

### 2. 组件化重构
- [x] 创建 `components/` 目录用于存放可复用组件
- [x] 实现 `compare_widget.html` 图像对比组件
  - 完全独立的 HTML/CSS/JS
  - 支持参数化配置
  - 避免全局命名空间污染
  - 支持多实例并存
- [x] 创建组件演示页面 `/compare_widget_demo`

### 3. 导航与布局优化
- [x] 更新导航栏链接
- [x] 统一页面标题和页脚
- [x] 优化响应式布局

## 待完成工作

### 1. 进一步组件化
- [ ] 将文件上传区域封装为组件 `components/file_uploader.html`
- [ ] 将图像预览区域封装为组件 `components/image_preview.html`
- [ ] 将表单元素封装为组件 `components/form_elements.html`

### 2. JavaScript 模块化
- [ ] 重构 JS 为模块化结构
- [ ] 实现 `js/modules/` 目录下的功能模块:
  - [ ] `apiClient.js` - API 请求封装
  - [ ] `fileUploader.js` - 文件上传处理
  - [ ] `uiManager.js` - UI 状态管理

### 3. 主题系统增强
- [ ] 完善主题切换功能
- [ ] 支持用户自定义主题
- [ ] 实现暗色模式

### 4. 性能优化
- [ ] 图像延迟加载
- [ ] 资源预加载
- [ ] 代码分割与按需加载

## 技术栈与标准

- **HTML**: 语义化标签，Jinja2 模板
- **CSS**: 变量，Flexbox/Grid 布局，响应式设计
- **JavaScript**: ES6+，模块化，无依赖
- **组件化**: 自包含组件，参数化，样式隔离
- **性能**: 最小化网络请求，资源优化

## 命名规范

- **组件**: 使用前缀防止冲突 (如 `bfl-*`)
- **CSS 类**: 功能描述性命名 (如 `.bfl-image-comparison`)
- **JavaScript**: 驼峰命名，模块化封装

## 文件结构

```
backend/
├── templates/
│   ├── components/         # 可复用组件
│   │   ├── compare_widget.html
│   │   └── ...
│   ├── macros/            # Jinja2 宏
│   │   └── forms.html
│   └── pages/             # 页面模板
├── static/
│   ├── css/
│   │   ├── app.css        # 主样式表
│   │   └── pages/         # 页面特定样式
│   ├── js/
│   │   ├── modules/       # JS 模块
│   │   └── pages/         # 页面特定脚本
│   └── img/
│       └── demo/          # 演示图像
```

## 后续迭代计划

1. **第一阶段**: 完成基础组件化 (当前)
2. **第二阶段**: JavaScript 模块化重构
3. **第三阶段**: 主题系统增强与性能优化
4. **第四阶段**: 用户体验改进与新功能 