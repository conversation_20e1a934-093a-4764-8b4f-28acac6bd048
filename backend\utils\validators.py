import os
import re
from werkzeug.utils import secure_filename
from typing import <PERSON><PERSON>, Dict, Any

def validate_file_extension(filename: str, allowed_extensions: set) -> bool:
    """验证文件扩展名"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def validate_uploaded_file(request, field_name: str, allowed_extensions: set) -> Tuple[bool, str, Dict[str, Any]]:
    """验证上传的文件"""
    # 检查文件是否存在
    if field_name not in request.files:
        return False, '未找到上传文件', {'error': '未找到上传文件'}
    
    file = request.files[field_name]
    
    # 检查文件名
    if file.filename == '':
        return False, '未选择文件', {'error': '未选择文件'}
    
    # 检查文件类型
    if not validate_file_extension(file.filename, allowed_extensions):
        return False, f'不支持的文件类型。允许的类型: {", ".join(allowed_extensions)}', \
               {'error': f'不支持的文件类型。允许的类型: {", ".join(allowed_extensions)}'}
    
    return True, '', {}

def validate_text_input(text: str, field_name: str = '文本') -> Dict[str, Any]:
    """
    验证文本输入
    
    Returns:
        dict: {'valid': bool, 'error': str}
    """
    if not text or not text.strip():
        return {'valid': False, 'error': f'{field_name}不能为空'}
    
    return {'valid': True, 'error': ''}

def validate_model_name(model: str, valid_models: list) -> Tuple[bool, str]:
    """验证模型名称"""
    if model not in valid_models:
        return False, f'不支持的模型: {model}。可用模型: {", ".join(valid_models)}'
    
    return True, ''

def validate_numeric_parameter(value: str, param_name: str, min_val: float = None, max_val: float = None) -> Tuple[bool, str, float]:
    """验证数值参数"""
    try:
        num_value = float(value)
        
        if min_val is not None and num_value < min_val:
            return False, f'{param_name}不能小于{min_val}', 0
        
        if max_val is not None and num_value > max_val:
            return False, f'{param_name}不能大于{max_val}', 0
        
        return True, '', num_value
        
    except ValueError:
        return False, f'{param_name}必须是有效的数字', 0


def validate_email(email: str) -> Tuple[bool, str]:
    """验证邮箱格式"""
    if not email:
        return False, '邮箱不能为空'

    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, '邮箱格式不正确'

    return True, '邮箱格式正确'


def validate_username(username: str) -> Tuple[bool, str]:
    """验证用户名"""
    if not username:
        return False, '用户名不能为空'

    username = username.strip()

    if len(username) < 3:
        return False, '用户名长度不能少于3个字符'

    if len(username) > 20:
        return False, '用户名长度不能超过20个字符'

    # 检查用户名格式（只允许字母、数字、下划线）
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return False, '用户名只能包含字母、数字和下划线'

    return True, '用户名格式正确'


def validate_password(password: str) -> Tuple[bool, str]:
    """验证密码强度"""
    if not password:
        return False, '密码不能为空'

    if len(password) < 8:
        return False, '密码长度不能少于8个字符'

    if len(password) > 128:
        return False, '密码长度不能超过128个字符'

    # 检查是否包含字母
    if not re.search(r'[a-zA-Z]', password):
        return False, '密码必须包含字母'

    # 检查是否包含数字
    if not re.search(r'\d', password):
        return False, '密码必须包含数字'

    return True, '密码强度符合要求'