#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版BFL API尺寸计算器
基于观察，BFL可能使用预设的候选尺寸表
"""

import math

def generate_candidate_dimensions(target_pixels=1040000, step=16, tolerance=0.05):
    """
    生成所有可能的候选尺寸 - 已重构为调用统一函数
    
    Args:
        target_pixels: 目标像素数
        step: 步长（16）
        tolerance: 像素数容忍度（5%）
    
    Returns:
        list: [(width, height, pixels, ratio), ...]
    """
    # 导入统一工具函数
    from .common_utils import generate_bfl_dimensions
    
    # 使用稍微宽松的宽高比范围（7:3 到 3:7）
    ratio_range = (3/7, 7/3)
    
    return generate_bfl_dimensions(
        target_pixels=target_pixels,
        step=step,
        tolerance=tolerance,
        aspect_ratio_range=ratio_range
    )


def find_best_match(input_width, input_height, candidates):
    """
    从候选尺寸中找到最佳匹配
    
    Args:
        input_width: 输入宽度
        input_height: 输入高度
        candidates: 候选尺寸列表
    
    Returns:
        tuple: 最佳匹配的(width, height, pixels, ratio)
    """
    input_ratio = input_width / input_height
    
    best_match = None
    min_ratio_diff = float('inf')
    
    for width, height, pixels, ratio in candidates:
        ratio_diff = abs(ratio - input_ratio)
        
        if ratio_diff < min_ratio_diff:
            min_ratio_diff = ratio_diff
            best_match = (width, height, pixels, ratio)
    
    return best_match