# 前端结构

## 模板文件
1. `base.html` - 基础模板
   - 包含通用布局
   - 引入CSS和JavaScript
   - 导航菜单

2. 功能页面
   - `index.html` - 首页
   - `translate.html` - 翻译页面
   - `generate.html` - 图像生成
   - `edit.html` - 图像编辑
   - `style.html` - 风格迁移
   - `restore.html` - 图像修复
   - `compare.html` - 图像对比
   - `gallery.html` - 图片画廊

## 静态资源
- `static/css/` - 样式文件
- `static/js/` - JavaScript文件
- `static/webfonts/` - 字体文件

## 特点
- 响应式设计
- Bootstrap框架
- Font Awesome图标
- jQuery支持
- 模块化结构