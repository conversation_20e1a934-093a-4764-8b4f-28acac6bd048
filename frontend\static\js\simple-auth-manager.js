/**
 * 简化的认证管理器
 * 设计原则：
 * 1. 单一状态源 - 只维护一个认证状态
 * 2. 事件驱动 - 状态变更通过事件通知
 * 3. 简化Token - 只使用access token
 * 4. 职责分离 - 前端只管状态，后端只管验证
 */
class SimpleAuthManager {
    constructor() {
        // 认证状态枚举
        this.AuthState = {
            LOGGED_OUT: 'logged_out',
            LOGGING_IN: 'logging_in', 
            LOGGED_IN: 'logged_in',
            ERROR: 'error'
        };
        
        // 当前状态
        this.currentState = this.AuthState.LOGGED_OUT;
        this.currentUser = null;
        this.errorMessage = null;
        
        // 事件监听器
        this.listeners = [];
        
        // 初始化
        this.init();
    }
    
    /**
     * 初始化认证管理器
     */
    init() {
        // 检查是否有有效的登录状态（通过cookie）
        this.checkInitialAuthState();
    }
    
    /**
     * 检查初始认证状态
     */
    async checkInitialAuthState() {
        try {
            const response = await fetch('/api/auth/status', {
                method: 'GET',
                credentials: 'include' // 包含cookies
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.user) {
                    this.setState(this.AuthState.LOGGED_IN, data.user);
                    return;
                }
            }
        } catch (error) {
            console.log('初始认证检查失败，用户未登录');
        }
        
        this.setState(this.AuthState.LOGGED_OUT);
    }
    
    /**
     * 用户登录
     * @param {string} identifier - 用户名/邮箱/手机号
     * @param {string} password - 密码
     * @param {boolean} rememberMe - 是否记住登录
     * @returns {Promise<{success: boolean, message?: string}>}
     */
    async login(identifier, password, rememberMe = false) {
        this.setState(this.AuthState.LOGGING_IN);
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    identifier,
                    password,
                    remember_me: rememberMe
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                this.setState(this.AuthState.LOGGED_IN, data.user);
                return { success: true };
            } else {
                this.setState(this.AuthState.ERROR, null, data.message || '登录失败');
                return { success: false, message: data.message || '登录失败' };
            }
        } catch (error) {
            const errorMsg = '网络错误，请检查连接';
            this.setState(this.AuthState.ERROR, null, errorMsg);
            return { success: false, message: errorMsg };
        }
    }
    
    /**
     * 用户登出
     */
    async logout() {
        try {
            await fetch('/api/auth/logout', {
                method: 'POST',
                credentials: 'include'
            });
        } catch (error) {
            console.log('登出请求失败，但继续清除本地状态');
        }
        
        this.setState(this.AuthState.LOGGED_OUT);
    }
    
    /**
     * 发送认证请求
     * @param {string} url - 请求URL
     * @param {object} options - 请求选项
     * @returns {Promise<Response>}
     */
    async request(url, options = {}) {
        const requestOptions = {
            ...options,
            credentials: 'include', // 自动包含cookies
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };
        
        try {
            const response = await fetch(url, requestOptions);
            
            // 如果返回401，说明认证失效
            if (response.status === 401) {
                this.setState(this.AuthState.LOGGED_OUT);
            }
            
            return response;
        } catch (error) {
            console.error('请求失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置认证状态
     * @param {string} state - 新状态
     * @param {object} user - 用户信息
     * @param {string} errorMessage - 错误信息
     */
    setState(state, user = null, errorMessage = null) {
        const oldState = this.currentState;
        
        this.currentState = state;
        this.currentUser = user;
        this.errorMessage = errorMessage;
        
        // 通知所有监听器
        this.notifyListeners({
            oldState,
            newState: state,
            user,
            errorMessage
        });
        
        // 触发全局事件
        this.dispatchAuthEvent(state, user, errorMessage);
    }
    
    /**
     * 添加状态监听器
     * @param {function} listener - 监听器函数
     */
    addListener(listener) {
        this.listeners.push(listener);
    }
    
    /**
     * 移除状态监听器
     * @param {function} listener - 监听器函数
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * 通知所有监听器
     * @param {object} eventData - 事件数据
     */
    notifyListeners(eventData) {
        this.listeners.forEach(listener => {
            try {
                listener(eventData);
            } catch (error) {
                console.error('监听器执行错误:', error);
            }
        });
    }
    
    /**
     * 触发全局认证事件
     * @param {string} state - 认证状态
     * @param {object} user - 用户信息
     * @param {string} errorMessage - 错误信息
     */
    dispatchAuthEvent(state, user, errorMessage) {
        let eventType;
        
        switch (state) {
            case this.AuthState.LOGGED_IN:
                eventType = 'authSuccess';
                break;
            case this.AuthState.LOGGED_OUT:
                eventType = 'authLogout';
                break;
            case this.AuthState.ERROR:
                eventType = 'authError';
                break;
            default:
                return;
        }
        
        const event = new CustomEvent(eventType, {
            detail: { user, errorMessage }
        });
        
        window.dispatchEvent(event);
    }
    
    /**
     * 获取当前认证状态
     * @returns {object}
     */
    getState() {
        return {
            state: this.currentState,
            user: this.currentUser,
            errorMessage: this.errorMessage,
            isLoggedIn: this.currentState === this.AuthState.LOGGED_IN,
            isLoggingIn: this.currentState === this.AuthState.LOGGING_IN
        };
    }
}

// 创建全局实例
window.simpleAuth = new SimpleAuthManager();

// 兼容性：保持原有的authManager接口
window.authManager = {
    getToken: () => null, // 不再使用token
    checkAuthStatus: () => window.simpleAuth.checkInitialAuthState(),
    logout: () => window.simpleAuth.logout(),
    request: (url, options) => window.simpleAuth.request(url, options)
};