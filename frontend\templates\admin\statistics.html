{% extends "base.html" %}

{% block title %}数据统计 - 管理员面板{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-chart-bar"></i> 数据统计</h2>
                <div class="btn-group">
                    <a href="{{ url_for('pages.admin_dashboard_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回仪表板
                    </a>
                    <button class="btn btn-info" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                    <button class="btn btn-success" onclick="exportReport()">
                        <i class="fas fa-download"></i> 导出报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围选择 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <label class="form-label">时间范围</label>
                            <select class="form-select" id="timeRange">
                                <option value="today">今天</option>
                                <option value="week" selected>最近7天</option>
                                <option value="month">最近30天</option>
                                <option value="quarter">最近3个月</option>
                                <option value="year">最近1年</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button class="btn btn-primary" onclick="updateCharts()">
                                    <i class="fas fa-search"></i> 查询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计图表 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> 用户增长趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="userGrowthChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tasks"></i> 任务完成统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="taskStatsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-coins"></i> 积分使用统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="creditsChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-dollar-sign"></i> 收入统计</h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> 详细数据</h5>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="dataTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button">
                                用户数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button">
                                任务数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="revenue-tab" data-bs-toggle="tab" data-bs-target="#revenue" type="button">
                                收入数据
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="dataTabContent">
                        <div class="tab-pane fade show active" id="users" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>新增用户</th>
                                            <th>活跃用户</th>
                                            <th>总用户数</th>
                                        </tr>
                                    </thead>
                                    <tbody id="usersTableBody">
                                        <tr>
                                            <td colspan="4" class="text-center">
                                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="tasks" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>总任务数</th>
                                            <th>成功任务</th>
                                            <th>失败任务</th>
                                            <th>成功率</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tasksTableBody">
                                        <tr>
                                            <td colspan="5" class="text-center">
                                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="revenue" role="tabpanel">
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>积分销售</th>
                                            <th>收入金额</th>
                                            <th>订单数量</th>
                                        </tr>
                                    </thead>
                                    <tbody id="revenueTableBody">
                                        <tr>
                                            <td colspan="4" class="text-center">
                                                <i class="fas fa-spinner fa-spin"></i> 加载中...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let charts = {};

$(document).ready(function() {
    // 检查管理员权限
    checkAdminAuth();
    
    // 初始化日期
    initializeDates();
    
    // 初始化图表
    initializeCharts();
    
    // 加载数据
    updateCharts();
    
    // 绑定时间范围变化事件
    $('#timeRange').on('change', function() {
        updateDateRange();
    });
});

function checkAdminAuth() {
    const token = getToken();
    
    if (!token) {
        showError('请先登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
        return;
    }
    
    // 验证管理员权限
    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success && response.user) {
                if (response.user.user_type !== 'enterprise') {
                    showError('需要管理员权限访问此页面');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } else {
                showError('获取用户信息失败');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            }
        },
        error: function() {
            showError('认证失败，请重新登录');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    });
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;
    
    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function initializeDates() {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    $('#endDate').val(today.toISOString().split('T')[0]);
    $('#startDate').val(weekAgo.toISOString().split('T')[0]);
}

function updateDateRange() {
    const range = $('#timeRange').val();
    const today = new Date();
    let startDate;
    
    switch(range) {
        case 'today':
            startDate = new Date(today);
            break;
        case 'week':
            startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        case 'quarter':
            startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        case 'year':
            startDate = new Date(today.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
        default:
            startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    }
    
    $('#startDate').val(startDate.toISOString().split('T')[0]);
    $('#endDate').val(today.toISOString().split('T')[0]);
}

function initializeCharts() {
    // 用户增长图表
    const userCtx = document.getElementById('userGrowthChart').getContext('2d');
    charts.userGrowth = new Chart(userCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '新增用户',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: '累计用户',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 任务统计图表
    const taskCtx = document.getElementById('taskStatsChart').getContext('2d');
    charts.taskStats = new Chart(taskCtx, {
        type: 'doughnut',
        data: {
            labels: ['成功', '失败', '进行中'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#28a745', '#dc3545', '#ffc107']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // 积分使用图表
    const creditsCtx = document.getElementById('creditsChart').getContext('2d');
    charts.credits = new Chart(creditsCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: '积分消耗',
                data: [],
                backgroundColor: '#ffc107'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 收入图表
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    charts.revenue = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '收入',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateCharts() {
    // 这里应该调用后端API获取统计数据
    // 目前显示模拟数据
    showInfo('数据统计功能正在开发中，当前显示的是模拟数据');
    
    // 模拟数据
    const mockData = generateMockData();
    updateChartsWithData(mockData);
}

function generateMockData() {
    const days = 7;
    const labels = [];
    const userData = [];
    const taskData = [];
    const creditData = [];
    const revenueData = [];
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString());
        
        userData.push(Math.floor(Math.random() * 10) + 1);
        taskData.push(Math.floor(Math.random() * 50) + 10);
        creditData.push(Math.floor(Math.random() * 100) + 20);
        revenueData.push(Math.floor(Math.random() * 1000) + 100);
    }
    
    return { labels, userData, taskData, creditData, revenueData };
}

function updateChartsWithData(data) {
    // 更新用户增长图表
    charts.userGrowth.data.labels = data.labels;
    charts.userGrowth.data.datasets[0].data = data.userData;
    charts.userGrowth.data.datasets[1].data = data.userData.reduce((acc, val, i) => {
        acc.push((acc[i-1] || 0) + val);
        return acc;
    }, []);
    charts.userGrowth.update();
    
    // 更新任务统计图表
    const totalTasks = data.taskData.reduce((a, b) => a + b, 0);
    charts.taskStats.data.datasets[0].data = [
        Math.floor(totalTasks * 0.8),
        Math.floor(totalTasks * 0.15),
        Math.floor(totalTasks * 0.05)
    ];
    charts.taskStats.update();
    
    // 更新积分图表
    charts.credits.data.labels = data.labels;
    charts.credits.data.datasets[0].data = data.creditData;
    charts.credits.update();
    
    // 更新收入图表
    charts.revenue.data.labels = data.labels;
    charts.revenue.data.datasets[0].data = data.revenueData;
    charts.revenue.update();
}

function refreshData() {
    updateCharts();
    showSuccess('数据已刷新');
}

function exportReport() {
    showInfo('报告导出功能正在开发中');
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 3000);
}

function showInfo(message) {
    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
}
</script>
{% endblock %}
