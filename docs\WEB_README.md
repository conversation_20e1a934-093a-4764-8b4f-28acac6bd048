# BFL AI 图像生成器 Web 应用

基于 Flask 的 Web 界面，为 BFL AI 图像生成器提供友好的用户界面。

## 项目结构

```
项目根目录/
├── docs/                  # 所有项目文档
│   ├── WEB_README.md
│   ├── BFL_README.md
│   └── ...
├── BFL/                   # 图像生成核心代码
│   ├── bfl_image_generator.py
│   └── ...
├── backend/               # Flask Web 应用
│   ├── app.py             # Flask 主应用
│   ├── config.py
│   ├── templates/
│   ├── static/
│   └── ...
├── utils/                 # 通用工具模块
├── tests/                 # 测试代码
├── run_web.py             # Web 应用启动脚本
├── uploads/               # 上传文件目录
└── outputs/               # 生成文件目录
```

## 功能特性

### 🎨 核心功能
- **图像生成**: 基于文本描述生成高质量图像
- **图像编辑**: 基于文本指令编辑现有图像
- **风格迁移**: 基于参考图像进行风格迁移
- **图像画廊**: 查看和管理生成的图像

### 🚀 技术特性
- **实时状态监控**: 任务进度实时更新
- **异步处理**: 后台处理，不阻塞界面
- **响应式设计**: 支持桌面和移动设备
- **任务管理**: 完整的任务历史和状态跟踪
- **文件管理**: 自动文件命名和组织

## 安装和运行

### 1. 安装依赖

```bash
# 安装 Web 应用依赖 (在项目根目录)
pip install -r backend/requirements_web.txt

# 安装核心生成器依赖
pip install -r BFL/requirements.txt
```

### 2. 设置API密钥

**方法1: 环境变量 (推荐)**
```bash
# Windows
set BFL_API_KEY=your_api_key_here
set DEEPSEEK_API_KEY=your_deepseek_api_key

# Linux/Mac
export BFL_API_KEY=your_api_key_here
export DEEPSEEK_API_KEY=your_deepseek_api_key
```

**方法2: 直接修改代码**
在 `backend/config.py` 文件中修改。

### 3. 启动应用

```bash
# 在项目根目录运行
python run_web.py
```

### 4. 访问应用

打开浏览器访问: http://localhost:5000

## 界面说明

- **主页**: 功能介绍和快速入口。
- **图像生成页面**: 基于提示词、模型、尺寸等参数生成图像。
- **图像编辑页面**: 上传并使用文本指令编辑图像。
- **风格迁移页面**: 将参考图的风格应用到新内容。
- **图像画廊**: 查看、管理所有生成的图像和任务。
- **翻译页面**: 测试和管理翻译服务。

## API 接口

### 图像生成/编辑/风格迁移
```
POST /api/generate
POST /api/edit
POST /api/style
```

### 任务状态
```
GET /api/status/{task_id}
GET /api/tasks
```

## 翻译与润色服务

项目集成了本地Ollama和云端DeepSeek两种翻译服务，由一个统一的服务管理器进行调度。

### 主要API接口

- **获取服务状态**: `GET /api/translate/status`
- **翻译接口**: `POST /api/translate`
- **英文润色接口**: `POST /api/translate/polish`
- **切换服务**: `POST /api/translate/switch-service`

### 服务配置

- **Ollama**: 本地安装并运行Ollama服务。
- **DeepSeek**: 设置 `DEEPSEEK_API_KEY` 环境变量。
- **服务逻辑**: 在 `backend/translation_service_manager.py` 中配置。

## 配置说明

- **应用配置**: `backend/config.py`
- **模型配置**: 支持 FLUX 系列模型，可在生成页面选择。

## 故障排除

- **启动失败**: 检查依赖是否安装，端口5000是否被占用。
- **API错误**: 检查 `BFL_API_KEY` 和 `DEEPSEEK_API_KEY` 是否正确设置。
- **文件问题**: 检查 `uploads` 和 `outputs` 目录权限。

## 安全注意事项

1. **API 密钥**: 使用环境变量，不要硬编码在代码中。
2. **文件上传**: 已限制上传文件的类型和大小。
3. **生产环境**: 考虑添加身份验证并使用 HTTPS。
