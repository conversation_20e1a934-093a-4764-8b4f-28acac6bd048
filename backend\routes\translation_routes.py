#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译服务路由 - 重构版
基于 unified_translator.py 的简洁实现
"""

import sys
import os

# 添加项目根目录到路径，以便导入Translation模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from flask import Blueprint, request, jsonify
from backend.config.app_config import AppConfig

# 创建蓝图
translation_bp = Blueprint('translation', __name__, url_prefix='/api/translate')

def _create_unified_translator():
    """创建统一翻译器实例"""
    from Translation.unified_translator import UnifiedTranslator
    
    config = AppConfig.TRANSLATION_CONFIG
    
    # 从配置文件读取主服务设置
    primary_service = config.get('primary', 'deepseek')
    
    # 处理Ollama提示词
    prompts = config.get('prompts', {})
    if AppConfig.NO_THINK:
        ollama_prompts = {
            'translation_prompt': prompts.get('translation', '') + ' /no_think',
            'polish_prompt_template': prompts.get('polish', '') + ' /no_think'
        }
    else:
        ollama_prompts = {
            'translation_prompt': prompts.get('translation', ''),
            'polish_prompt_template': prompts.get('polish', '')
        }
    
    return UnifiedTranslator(
        primary=primary_service,
        deepseek_kwargs={
            'api_key': config['deepseek']['api_key'],
            'model': config['deepseek']['model'],
            'base_url': config['deepseek']['api_url'].replace('/v1/chat/completions', '')
        },
        ollama_kwargs={
            'base_url': config['ollama']['api_url'],
            'model': config['ollama']['model'],
            'timeout': config['ollama']['timeout'],
            **ollama_prompts  # 传入处理后的提示词
        }
    )

def _format_translate_response(result):
    """格式化翻译响应，保持与前端的兼容性"""
    if result['success']:
        return {
            'success': True,
            'translated_text': result['translated'],
            'original_text': result['original']
        }
    else:
        return {
            'success': False,
            'error': result.get('error', '翻译失败'),
            'translated_text': result['original'],  # 失败时返回原文
            'original_text': result['original']
        }

def _format_polish_response(result):
    """格式化润色响应，保持与前端的兼容性"""
    if result['success']:
        return {
            'success': True,
            'polished_text': result['polished'],
            'original_text': result['original']
        }
    else:
        return {
            'success': False,
            'error': result.get('error', '润色失败'),
            'polished_text': result['original'],  # 失败时返回原文
            'original_text': result['original']
        }

def _get_service_status():
    """获取服务状态"""
    try:
        translator = _create_unified_translator()
        deepseek_available = translator.deepseek.check_service_status()
        ollama_available = translator.ollama.check_service_status()
        
        # 构建服务列表，主服务优先
        primary_service = translator.primary
        backup_service = translator.backup
        
        services = []
        
        # 添加主服务（优先显示）
        if primary_service == 'deepseek':
            services.append({
                'type': 'deepseek',
                'status': 'available' if deepseek_available else 'unavailable',
                'model': translator.deepseek.model,
                'is_primary': True
            })
            services.append({
                'type': 'ollama',
                'status': 'available' if ollama_available else 'unavailable', 
                'model': translator.ollama.model,
                'is_primary': False
            })
        else:  # primary_service == 'ollama'
            services.append({
                'type': 'ollama',
                'status': 'available' if ollama_available else 'unavailable',
                'model': translator.ollama.model,
                'is_primary': True
            })
            services.append({
                'type': 'deepseek',
                'status': 'available' if deepseek_available else 'unavailable',
                'model': translator.deepseek.model,
                'is_primary': False
            })
        
        return {
            'success': True,
            'manager_status': 'running',
            'primary_service': primary_service,
            'backup_service': backup_service,
            'services': services
        }
    except Exception as e:
        return {
            'success': False,
            'manager_status': 'error',
            'error': str(e),
            'services': []
        }

@translation_bp.route('', methods=['POST'])
def translate():
    """翻译接口"""
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({
            'success': False,
            'error': '请提供要翻译的文本'
        }), 400
    
    try:
        translator = _create_unified_translator()
        result = translator.translate(data['text'])
        return jsonify(_format_translate_response(result))
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'翻译服务异常: {str(e)}',
            'translated_text': data['text'],
            'original_text': data['text']
        }), 500

@translation_bp.route('/polish', methods=['POST'])
def polish():
    """润色接口"""
    data = request.get_json()
    if not data or 'text' not in data:
        return jsonify({
            'success': False,
            'error': '请提供要润色的文本'
        }), 400
    
    try:
        translator = _create_unified_translator()
        result = translator.polish(data['text'])
        return jsonify(_format_polish_response(result))
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'润色服务异常: {str(e)}',
            'polished_text': data['text'],
            'original_text': data['text']
        }), 500

@translation_bp.route('/status', methods=['GET'])
def status():
    """服务状态检查"""
    return jsonify(_get_service_status())

# ===== 兼容性函数 =====

def translate_text(text: str) -> dict:
    """
    简单的翻译函数，供其他模块调用
    返回格式：{'success': bool, 'text': str}
    """
    if not text or not text.strip():
        return {'success': False, 'text': text}
    
    try:
        translator = _create_unified_translator()
        result = translator.translate(text)
        
        if result['success']:
            return {
                'success': True,
                'text': result['translated']
            }
        else:
            return {
                'success': False,
                'text': text  # 失败时返回原文
            }
    except Exception as e:
        return {
            'success': False, 
            'text': text  # 失败时返回原文
        } 