{% extends "base.html" %}

{% block title %}积分管理 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 积分概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4><i class="fas fa-coins"></i> 积分管理中心</h4>
                            <p class="mb-0">管理您的积分，查看使用记录，购买更多积分</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h2 class="mb-0">{{ user.available_credits }}</h2>
                            <small>可用积分</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-wallet fa-2x text-primary mb-2"></i>
                    <h5>总积分</h5>
                    <h3 class="text-primary">{{ user.total_credits }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                    <h5>已使用</h5>
                    <h3 class="text-success">{{ user.used_credits }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-calendar-day fa-2x text-warning mb-2"></i>
                    <h5>今日已用</h5>
                    <h3 class="text-warning">{{ user.daily_used }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-percentage fa-2x text-info mb-2"></i>
                    <h5>使用率</h5>
                    <h3 class="text-info">{{ "%.1f"|format((user.used_credits / user.total_credits * 100) if user.total_credits > 0 else 0) }}%</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧：积分购买 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shopping-cart"></i> 购买积分 <span class="badge bg-warning">模拟购买</span></h5>
                </div>
                <div class="card-body">
                    <div class="credit-packages">
                        <div class="package-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>基础包</h6>
                                    <p class="text-muted mb-0">50 积分</p>
                                </div>
                                <div class="text-end">
                                    <div class="h5 mb-0">¥19</div>
                                    <button class="btn btn-sm btn-primary" onclick="purchaseCreditsPackage('basic')">模拟购买</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="package-item mb-3 p-3 border rounded bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>推荐包 <span class="badge bg-success">热门</span></h6>
                                    <p class="text-muted mb-0">150 积分</p>
                                </div>
                                <div class="text-end">
                                    <div class="h5 mb-0">¥49</div>
                                    <button class="btn btn-sm btn-success" onclick="purchaseCreditsPackage('standard')">模拟购买</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="package-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>专业包</h6>
                                    <p class="text-muted mb-0">500 积分</p>
                                </div>
                                <div class="text-end">
                                    <div class="h5 mb-0">¥149</div>
                                    <button class="btn btn-sm btn-warning" onclick="purchaseCreditsPackage('premium')">模拟购买</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="package-item mb-3 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6>企业包</h6>
                                    <p class="text-muted mb-0">1500 积分</p>
                                </div>
                                <div class="text-end">
                                    <div class="h5 mb-0">¥399</div>
                                    <button class="btn btn-sm btn-dark" onclick="purchaseCreditsPackage('enterprise')">模拟购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i>
                        <small>积分永不过期，支持微信、支付宝、银行卡支付</small>
                    </div>
                </div>
            </div>
            
            <!-- 积分使用说明 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6><i class="fas fa-question-circle"></i> 积分使用说明</h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="mb-2">
                            <strong>图像生成：</strong>
                            <ul class="mb-1">
                                <li>FLUX Pro: 1 积分</li>
                                <li>FLUX Kontext Pro: 2 积分</li>
                                <li>FLUX Kontext Max: 3 积分</li>
                            </ul>
                        </div>
                        <div class="mb-2">
                            <strong>图像编辑：</strong>
                            <ul class="mb-1">
                                <li>基础编辑: 2-4 积分</li>
                                <li>高级编辑: 3-6 积分</li>
                            </ul>
                        </div>
                        <div class="mb-2">
                            <strong>风格迁移：</strong>
                            <ul class="mb-1">
                                <li>标准风格: 3-6 积分</li>
                                <li>高级风格: 4-8 积分</li>
                            </ul>
                        </div>
                        <div class="text-muted">
                            <small>* 高分辨率和商业授权会增加额外费用</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧：交易记录 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-history"></i> 积分交易记录</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active" data-filter="all">全部</button>
                        <button class="btn btn-outline-primary" data-filter="consume">消费</button>
                        <button class="btn btn-outline-primary" data-filter="purchase">购买</button>
                        <button class="btn btn-outline-primary" data-filter="refund">退还</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>类型</th>
                                    <th>描述</th>
                                    <th>积分变化</th>
                                    <th>余额</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="transactionHistory">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 正在加载...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav>
                        <ul class="pagination pagination-sm justify-content-center" id="pagination">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 购买确认模态框 -->
<div class="modal fade" id="purchaseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-shopping-cart"></i> 确认购买
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-coins fa-3x text-warning mb-3"></i>
                    <h5>购买积分包</h5>
                    <p class="mb-3">
                        您将购买 <strong id="purchaseCredits">0</strong> 积分<br>
                        支付金额：<strong class="text-danger">¥<span id="purchaseAmount">0</span></strong>
                    </p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        购买后积分将立即到账，支持多种支付方式
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmPurchase()">
                    <i class="fas fa-credit-card"></i> 立即支付
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentFilter = 'all';
let currentPage = 1;
let purchaseData = {};

// 初始化函数已移至页面底部

function loadCreditsData() {
    $.ajax({
        url: '/api/user/dashboard-data',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken()
        },
        success: function(response) {
            if (response.success) {
                updateCreditsDisplay(response.data.credits);
            }
        },
        error: function(xhr) {
            console.error('加载积分数据失败:', xhr.responseText);
        }
    });
}

function updateCreditsDisplay(credits) {
    // 更新页面顶部的积分显示
    $('.card.bg-gradient-primary .col-md-4 h2').text(credits.available_credits);

    // 更新统计卡片
    $('.card:contains("总积分") h3').text(credits.total_credits);
    $('.card:contains("已使用") h3').text(credits.used_credits);
    $('.card:contains("今日剩余") h3').text(credits.daily_remaining);
    $('.card:contains("每日限额") h3').text(credits.daily_limit === -1 ? '无限制' : credits.daily_limit);
}

function loadTransactionHistory(page = 1) {
    currentPage = page;
    
    $.ajax({
        url: '/api/user/credits/transactions',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('access_token')
        },
        data: {
            filter: currentFilter,
            page: page,
            per_page: 10
        },
        success: function(response) {
            if (response.success) {
                displayTransactions(response.transactions);
                updatePagination(response.pagination);
            }
        },
        error: function() {
            $('#transactionHistory').html('<tr><td colspan="6" class="text-center text-danger">加载失败</td></tr>');
        }
    });
}

function displayTransactions(transactions) {
    let html = '';
    
    if (transactions.length === 0) {
        html = '<tr><td colspan="6" class="text-center text-muted">暂无交易记录</td></tr>';
    } else {
        transactions.forEach(function(transaction) {
            const typeClass = getTransactionTypeClass(transaction.transaction_type);
            const typeText = getTransactionTypeText(transaction.transaction_type);
            const amountClass = transaction.credits_amount > 0 ? 'text-success' : 'text-danger';
            const amountPrefix = transaction.credits_amount > 0 ? '+' : '';
            
            html += `
                <tr>
                    <td>${new Date(transaction.created_at).toLocaleString('zh-CN')}</td>
                    <td><span class="badge ${typeClass}">${typeText}</span></td>
                    <td>${transaction.description}</td>
                    <td class="${amountClass}">${amountPrefix}${transaction.credits_amount}</td>
                    <td>${transaction.balance_after}</td>
                    <td><span class="badge bg-success">完成</span></td>
                </tr>
            `;
        });
    }
    
    $('#transactionHistory').html(html);
}

function updatePagination(pagination) {
    let html = '';
    
    // 上一页
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransactionHistory(${pagination.prev_num})">上一页</a></li>`;
    }
    
    // 页码
    for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
        const activeClass = i === pagination.page ? 'active' : '';
        html += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadTransactionHistory(${i})">${i}</a></li>`;
    }
    
    // 下一页
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadTransactionHistory(${pagination.next_num})">下一页</a></li>`;
    }
    
    $('#pagination').html(html);
}

function purchaseCredits(credits, amount) {
    purchaseData = { credits, amount };
    $('#purchaseCredits').text(credits);
    $('#purchaseAmount').text(amount);
    $('#purchaseModal').modal('show');
}

function confirmPurchase() {
    // 这里应该集成实际的支付系统
    showAlert('info', '支付功能即将推出，敬请期待！');
    $('#purchaseModal').modal('hide');

    // 模拟购买成功
    // setTimeout(() => {
    //     showAlert('success', `成功购买 ${purchaseData.credits} 积分！`);
    //     location.reload();
    // }, 2000);
}

// 新的模拟购买函数
function purchaseCreditsPackage(packageId) {
    const packages = {
        'basic': { credits: 50, name: '基础包' },
        'standard': { credits: 150, name: '推荐包' },
        'premium': { credits: 500, name: '专业包' },
        'enterprise': { credits: 1500, name: '企业包' }
    };

    const package = packages[packageId];
    if (!package) {
        showAlert('danger', '无效的套餐选择');
        return;
    }

    if (!confirm(`确认模拟购买 ${package.name}（${package.credits} 积分）吗？`)) {
        return;
    }

    $.ajax({
        url: '/api/user/credits/purchase',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'Authorization': 'Bearer ' + getToken()
        },
        data: JSON.stringify({
            package_id: packageId
        }),
        success: function(response) {
            if (response.success) {
                showAlert('success', response.message);
                // 刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert('danger', response.message);
            }
        },
        error: function(xhr) {
            console.error('购买失败:', xhr.responseText);
            showAlert('danger', '购买失败，请稍后重试');
        }
    });
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;

    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function getTransactionTypeClass(type) {
    switch(type) {
        case 'consume': return 'bg-danger';
        case 'purchase': return 'bg-success';
        case 'refund': return 'bg-warning';
        case 'bonus': return 'bg-info';
        default: return 'bg-secondary';
    }
}

function getTransactionTypeText(type) {
    switch(type) {
        case 'consume': return '消费';
        case 'purchase': return '购买';
        case 'refund': return '退还';
        case 'bonus': return '奖励';
        default: return '其他';
    }
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}

{% block extra_js %}
<script>
// 确保jQuery已加载
if (typeof $ === 'undefined') {
    console.error('jQuery not loaded!');
    // 如果jQuery未加载，尝试等待
    setTimeout(function() {
        if (typeof $ !== 'undefined') {
            initCreditsPage();
        }
    }, 100);
} else {
    // jQuery已加载，直接初始化
    $(document).ready(function() {
        initCreditsPage();
    });
}

function initCreditsPage() {
    loadCreditsData();
    loadTransactionHistory();

    // 筛选按钮事件
    $('[data-filter]').on('click', function() {
        $('[data-filter]').removeClass('active');
        $(this).addClass('active');
        currentFilter = $(this).data('filter');
        currentPage = 1;
        loadTransactionHistory();
    });
}
</script>
{% endblock %}
