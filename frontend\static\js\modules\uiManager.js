const uiManager = (() => {

    /**
     * Shows a loading state on an element (button/form).
     * @param {HTMLElement} element - The element to show loading state on.
     * @param {string} loadingText - The text to display during loading.
     * @param {string} [statusSelector] - Optional selector for status area.
     */
    function showLoadingState(element, loadingText = '处理中...', statusSelector = null) {
        if (element instanceof HTMLFormElement) {
            // 如果是表单，禁用所有表单元素
            element.querySelectorAll('input, button, select, textarea').forEach(el => {
                el.disabled = true;
            });
        } else if (element instanceof HTMLElement) {
            // 如果是单个元素（如按钮）
            element.disabled = true;
            // 保存原始内容
            element.dataset.originalContent = element.innerHTML;
            // 设置加载状态
            element.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${loadingText}`;
        }

        // 显示状态区域的加载信息
        if (statusSelector) {
            const statusArea = document.querySelector(statusSelector);
            if (statusArea) {
                statusArea.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>${loadingText}
                    </div>
                `;
                statusArea.style.display = 'block';
            }
        }
    }

    /**
     * Hides the loading state and re-enables the form.
     * @param {HTMLFormElement} formElement - The form to be re-enabled.
     */
    function hideLoadingState(element) {
        if (element instanceof HTMLFormElement) {
            // 如果是表单，重新启用所有表单元素
            element.querySelectorAll('input, button, select, textarea').forEach(el => {
                el.disabled = false;
            });
        } else if (element instanceof HTMLElement) {
            // 如果是单个元素（如按钮）
            element.disabled = false;
            // 恢复原始内容
            if (element.dataset.originalContent) {
                element.innerHTML = element.dataset.originalContent;
                delete element.dataset.originalContent;
            }
        }
    }

    /**
     * Displays the final result in a container.
     * @param {string} resultSelector - Selector for the result display area.
     * @param {object} taskResult - The final task object from the API.
     */
    function displayResult(resultSelector, taskResult) {
        const resultArea = document.querySelector(resultSelector);
        if (!resultArea) return;

        let content = '';
        if (taskResult.output_file) {
            const filename = taskResult.output_file.split('/').pop();
            const viewUrl = `/view/${filename}`;
            const downloadUrl = `/download/${filename}`;
            content = `
                <div class="card">
                    <div class="card-header">生成成功</div>
                    <div class="card-body text-center">
                        <img src="${viewUrl}" class="img-fluid rounded mb-3" alt="Generated Image">
                        <a href="${downloadUrl}" class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>下载图像
                        </a>
                    </div>
                </div>
            `;
        } else if (taskResult.translated_text) {
             content = `
                <div class="alert alert-success">
                    <strong>翻译结果:</strong>
                    <p>${taskResult.translated_text}</p>
                </div>
            `;
        }
        
        resultArea.innerHTML = content;
        resultArea.style.display = 'block';
    }
    
    /**
     * Shows a test mode result block.
     * @param {string} statusSelector - Selector for the status area.
     * @param {object} params - The parameters that would have been sent.
     */
    function showTestResult(statusSelector, params) {
        const statusArea = document.querySelector(statusSelector);
        if (!statusArea) return;
        
        const content = `
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark"><i class="fas fa-bug"></i> <strong>测试模式</strong></div>
                <div class="card-body">
                    <p><strong>请求参数:</strong></p>
                    <pre class="bg-light p-2 rounded"><code>${JSON.stringify(params, null, 2)}</code></pre>
                </div>
            </div>`;
        statusArea.innerHTML = content;
        statusArea.style.display = 'block';
    }

    /**
     * Updates the status area with progress information.
     * @param {string} statusSelector - Selector for the status area.
     * @param {string} status - The current status ('processing', 'completed', etc.).
     * @param {string} message - The status message.
     */
    function updateStatus(statusSelector, status, message, details = null) {
        const statusArea = document.querySelector(statusSelector);
        if (!statusArea) return;
        
        let iconClass = 'fas fa-info-circle';
        let alertClass = 'alert-info';
        let statusText = status;
        
        if (status === 'processing') {
            iconClass = 'fas fa-spinner fa-spin';
            alertClass = 'alert-info';
            statusText = '处理中';
        } else if (status === 'completed') {
            iconClass = 'fas fa-check-circle';
            alertClass = 'alert-success';
            statusText = '完成';
        } else if (status === 'failed') {
            iconClass = 'fas fa-times-circle';
            alertClass = 'alert-danger';
            statusText = '失败';
        } else if (status === 'Content Moderated') {
            iconClass = 'fas fa-shield-alt';
            alertClass = 'alert-warning';
            statusText = '内容审核未通过';
        }

        let content = `
            <div class="alert ${alertClass}">
                <i class="${iconClass} me-2"></i><strong>${statusText}:</strong> ${message || ''}`;
        
        // 如果有详细信息，添加到内容中
        if (details) {
            if (status === 'Content Moderated' && details['Moderation Reasons']) {
                content += `<div class="mt-2"><strong>审核原因:</strong>`;
                content += `<ul class="mb-0">`;
                details['Moderation Reasons'].forEach(reason => {
                    content += `<li>${reason}</li>`;
                });
                content += `</ul></div>`;
            } else {
                content += `<div class="mt-2"><pre class="mb-0">${JSON.stringify(details, null, 2)}</pre></div>`;
            }
        }
        
        content += `</div>`;
        statusArea.innerHTML = content;
        statusArea.style.display = 'block';
    }

    /**
     * Displays a temporary toast message.
     * @param {string} type - 'success', 'error', or 'info'.
     * @param {string} message - The message to display.
     */
    function showToast(type, message) {
        const toastContainer = document.createElement('div');
        toastContainer.className = `toast-notification toast-${type}`;
        toastContainer.textContent = message;
        
        document.body.appendChild(toastContainer);
        
        setTimeout(() => {
            toastContainer.classList.add('show');
        }, 100);

        setTimeout(() => {
            toastContainer.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toastContainer);
            }, 500);
        }, 3000);
    }

    /**
     * Displays a success message.
     * @param {string} statusSelector - Selector for the status display area.
     * @param {string} message - The success message to display.
     */
    function showSuccess(statusSelector, message) {
        const statusArea = document.querySelector(statusSelector);
        if (statusArea) {
            statusArea.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>
            `;
            statusArea.style.display = 'block';
        }
    }

    /**
     * Displays an error message.
     * @param {string} statusSelector - Selector for the status display area.
     * @param {string} message - The error message to display.
     */
    function showError(statusSelector, message) {
        const statusArea = document.querySelector(statusSelector);
        if (statusArea) {
            statusArea.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>错误: ${message}
                </div>
            `;
            statusArea.style.display = 'block';
        }
    }

    /**
     * Clears the status area.
     * @param {string} statusSelector - Selector for the status display area.
     */
    function clearStatus(statusSelector) {
        const statusArea = document.querySelector(statusSelector);
        if (statusArea) {
            statusArea.innerHTML = '';
            statusArea.style.display = 'none';
        }
    }

    /**
     * 添加重试按钮到指定区域
     * @param {string} selector - 目标元素选择器
     * @param {function} onRetry - 重试回调函数
     */
    function addRetryButton(selector, onRetry) {
        const container = document.querySelector(selector);
        if (!container) return;
        
        const retryBtn = document.createElement('button');
        retryBtn.className = 'btn btn-warning mt-3';
        retryBtn.innerHTML = '<i class="fas fa-redo"></i> 重试';
        retryBtn.onclick = onRetry;
        
        container.appendChild(retryBtn);
    }

    /**
     * 创建一个可复用的任务状态处理器
     * @param {string} statusSelector - 状态显示区域的CSS选择器
     * @param {string} resultSelector - 结果显示区域的CSS选择器
     * @returns {object} 一个包含 showProcessing, showSuccess, showError, reset 方法的对象
     */
    function createTaskStatusHandler(statusSelector, resultSelector) {
        const statusArea = document.querySelector(statusSelector);
        const resultArea = document.querySelector(resultSelector);

        if (!statusArea || !resultArea) {
            console.error('Status or result area not found for handler.');
            // 返回空操作函数以避免错误
            return {
                showProcessing: () => {},
                showSuccess: () => {},
                showError: () => {},
                reset: () => {}
            };
        }

        const showProcessing = (message) => {
            statusArea.innerHTML = `
                <div class="alert alert-info d-flex align-items-center">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    <span class="flex-grow-1">${message}</span>
                </div>`;
            resultArea.innerHTML = '';
        };

        const showSuccess = (message, imageUrl, originalImageUrl = null) => {
            statusArea.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>${message}
                </div>`;
            
            if (originalImageUrl) {
                 // 复用 displayComparisonResult 来显示对比图
                 displayComparisonResult(resultSelector, originalImageUrl, imageUrl, "任务结果", "原始图像", "处理后图像");
            } else if (imageUrl) {
                resultArea.innerHTML = `
                    <div class="card">
                        <div class="card-body text-center">
                            <img src="${imageUrl}" class="img-fluid rounded shadow-lg" style="max-height: 400px;" alt="结果图像">
                            <a href="${imageUrl}" class="btn btn-success mt-3" download target="_blank">
                                <i class="fas fa-download me-2"></i>下载图像
                            </a>
                        </div>
                    </div>`;
            }
        };

        const showError = (message) => {
            statusArea.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>${message}
                </div>`;
            resultArea.innerHTML = '';
        };

        const reset = (initialHtml) => {
            if (initialHtml) {
                statusArea.innerHTML = initialHtml;
            } else {
                statusArea.innerHTML = '';
            }
            resultArea.innerHTML = '';
        };

        return { showProcessing, showSuccess, showError, reset };
    }

    /**
     * 获取预览图像的源URL
     * @param {string} containerSelector - 容器选择器
     * @returns {string|null} 预览图像URL或null
     */
    function getPreviewImageSrc(containerSelector) {
        const container = document.querySelector(containerSelector);
        if (!container) return null;
        
        const previewImg = container.querySelector('.preview-image');
        return previewImg ? previewImg.src : null;
    }

    /**
     * 显示对比结果
     * @param {string} resultSelector - 结果区域选择器
     * @param {string} originalImageUrl - 原始图像URL
     * @param {string} processedImageUrl - 处理后图像URL
     * @param {string} title - 标题
     * @param {string} originalLabel - 原始图像标签
     * @param {string} processedLabel - 处理后图像标签
     */
    function displayComparisonResult(resultSelector, originalImageUrl, processedImageUrl, title = "处理结果", originalLabel = "原始图像", processedLabel = "处理后") {
        const resultArea = document.querySelector(resultSelector);
        if (!resultArea) return;

        // 构建图像URL - 处理不同的路径格式
        let finalProcessedUrl;
        if (/^https?:\/\//i.test(processedImageUrl)) {
            finalProcessedUrl = processedImageUrl;
        } else if (processedImageUrl.startsWith('/')) {
            finalProcessedUrl = processedImageUrl;
        } else {
            const fileName = processedImageUrl.split(/[\\/]/).pop();
            finalProcessedUrl = `/outputs/${fileName}`;
        }

        const comparisonHtml = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-images"></i> ${title}</h6>
                </div>
                <div class="card-body">
                    ${originalImageUrl ? `
                        <h6 class="text-center mb-3"><i class="fas fa-columns"></i> 前后对比</h6>
                        <div class="row">
                            <div class="col-md-6 text-center">
                                <h6 class="text-muted">${originalLabel}</h6>
                                <img src="${originalImageUrl}" class="img-fluid rounded" alt="${originalLabel}" style="max-height: 300px;">
                            </div>
                            <div class="col-md-6 text-center">
                                <h6 class="text-muted">${processedLabel}</h6>
                                <img src="${finalProcessedUrl}" class="img-fluid rounded" alt="${processedLabel}" style="max-height: 300px;">
                            </div>
                        </div>
                    ` : `
                        <div class="text-center">
                            <img src="${finalProcessedUrl}" class="img-fluid rounded shadow-lg" style="max-height: 400px;" alt="${processedLabel}">
                        </div>
                    `}
                    <div class="mt-3 text-center">
                        <a href="${finalProcessedUrl}" class="btn btn-success" download target="_blank">
                            <i class="fas fa-download me-2"></i>下载图像
                        </a>
                    </div>
                </div>
            </div>
        `;

        resultArea.innerHTML = comparisonHtml;
    }

    return {
        showLoadingState,
        hideLoadingState,
        displayResult,
        showTestResult,
        updateStatus,
        showToast,
        showSuccess,
        showError,
        clearStatus,
        addRetryButton,
        createTaskStatusHandler,
        getPreviewImageSrc,
        displayComparisonResult
    };

})();

export default uiManager; 