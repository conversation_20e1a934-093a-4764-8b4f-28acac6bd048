from flask import Blueprint, request, jsonify
# JWT imports removed - using simple auth system
from werkzeug.utils import secure_filename

from backend.config.app_config import AppConfig
from backend.utils.validators import validate_uploaded_file, validate_text_input
from backend.utils.helpers import create_response, generate_timestamp_filename
from backend.utils.image_preprocessor import preprocess_image_for_bfl
from backend.services.image_service import image_service
from backend.services.task_service import task_service
from backend.services.credit_service import credit_service
from backend.routes.translation_routes import translate_text
from backend.routes.simple_auth_routes import require_auth, get_current_user

# 创建图像API路由蓝图
image_bp = Blueprint('image_api', __name__, url_prefix='/api')

@image_bp.route('/generate', methods=['POST'])
@require_auth
def api_generate():
    """API: 生成图像"""
    try:
        # 从 FormData 获取数据，而不是 JSON
        data = request.form

        # 验证提示词
        prompt_text = data.get('prompt', '')
        validation_result = validate_text_input(prompt_text, field_name='提示词')
        if not validation_result['valid']:
            response, status_code = create_response(False, validation_result['error'])
            return jsonify(response), status_code

        # 获取当前用户（已通过 @require_auth 验证）
        current_user = get_current_user()

        # 计算积分消费
        model = data.get('model', AppConfig.DEFAULT_MODELS['generate'])
        kwargs = image_service.prepare_generation_parameters(data)
        credits_cost = credit_service.calculate_task_cost('generate', model, kwargs)

        # 检查用户积分和限制
        limit_check = credit_service.check_user_limits(current_user.id, credits_cost)
        if not limit_check['success']:
            response, status_code = create_response(False, limit_check['message'], status_code=402)
            return jsonify(response), status_code

        # 翻译提示词（如果需要）
        translation_result = translate_text(prompt_text)
        final_prompt = translation_result['text'] if translation_result['success'] else prompt_text

        # 创建任务
        task_id = task_service.create_task(
            'generate',
            prompt=prompt_text,
            final_prompt=final_prompt,
            model=model,
            user_id=user_id,
            credits_cost=credits_cost
        )

        # 预扣积分
        credit_result = credit_service.consume_credits(
            user_id, credits_cost, f'图像生成 - {model}', task_id
        )
        if not credit_result['success']:
            response, status_code = create_response(False, credit_result['message'], status_code=402)
            return jsonify(response), status_code
        transaction_id = credit_result.get('transaction_id')

        # 启动任务
        image_service.start_generation_task(
            task_id,
            final_prompt,
            model,
            **kwargs
        )

        response_data = {
            'task_id': task_id,
            'message': '任务已创建',
            'credits_cost': credits_cost,
            'remaining_credits': current_user.available_credits,
            'daily_remaining': current_user.daily_remaining,
            'transaction_id': transaction_id
        }

        response, status_code = create_response(True, response_data)
        return jsonify(response), status_code

    except Exception as e:
        response, status_code = create_response(False, str(e), status_code=500)
        return jsonify(response), status_code

@image_bp.route('/edit', methods=['POST'])
@require_auth
def api_edit():
    """API: 编辑图像"""
    try:
        # 验证上传的文件
        is_valid, error_msg, error_response = validate_uploaded_file(
            request, 'image', AppConfig.ALLOWED_EXTENSIONS
        )
        if not is_valid:
            response, status_code = create_response(False, error_msg)
            return jsonify(response), status_code

        file = request.files['image']

        # 验证编辑指令
        prompt = request.form.get('prompt', '')
        is_valid, error_msg = validate_text_input(prompt, '编辑指令')
        if not is_valid:
            response, status_code = create_response(False, error_msg)
            return jsonify(response), status_code

        # 获取当前用户（已通过 @require_auth 验证）
        current_user = get_current_user()
        user_id = current_user.id

        # 计算积分消费
        model = request.form.get('model', AppConfig.DEFAULT_MODELS['edit'])
        kwargs = image_service.prepare_edit_parameters(request.form)
        credits_cost = credit_service.calculate_task_cost('edit', model, kwargs)

        # 检查用户积分和限制
        limit_check = credit_service.check_user_limits(user_id, credits_cost)
        if not limit_check['success']:
            response, status_code = create_response(False, limit_check['message'], status_code=402)
            return jsonify(response), status_code

        # 翻译提示词（如果需要）
        translation_result = translate_text(prompt)
        if translation_result['success']:
            final_prompt = translation_result['text']
        else:
            final_prompt = prompt
        
        # 保存上传的文件
        filename = generate_timestamp_filename(secure_filename(file.filename))
        original_filepath = AppConfig.get_upload_path(filename)
        file.save(original_filepath)

        # 🆕 添加图片预处理 - 100万像素限制
        preprocessed_filename = f"preprocessed_{filename}"
        preprocessed_filepath = AppConfig.get_upload_path(preprocessed_filename)

        preprocess_success, preprocess_info = preprocess_image_for_bfl(
            original_filepath,
            preprocessed_filepath
        )

        if preprocess_success:
            # 使用预处理后的文件
            final_filepath = preprocessed_filepath
            print(f"✅ 图片预处理成功: {original_filepath} -> {preprocessed_filepath}")
            if preprocess_info.get('pixel_preprocessing', {}).get('resized', False):
                pixel_info = preprocess_info['pixel_preprocessing']
                print(f"📏 像素调整: {pixel_info['original_pixels']:,} -> {pixel_info['final_pixels']:,}")
        else:
            # 预处理失败，使用原文件
            final_filepath = original_filepath
            print(f"⚠️ 图片预处理失败，使用原文件: {preprocess_info.get('error', '未知错误')}")

        # 创建任务
        task_id = task_service.create_task(
            'edit',
            original_prompt=prompt,
            final_prompt=final_prompt,
            model=model,
            input_file=final_filepath,
            original_filename=file.filename,
            uploaded_filename=filename,
            preprocessed_filename=preprocessed_filename if preprocess_success else None,
            preprocessing_info=preprocess_info if preprocess_success else None,
            user_id=user_id,
            credits_cost=credits_cost
        )

        # 预扣积分
        credit_result = credit_service.consume_credits(
            user_id, credits_cost, f'图像编辑 - {model}', task_id
        )
        if not credit_result['success']:
            response, status_code = create_response(False, credit_result['message'], status_code=402)
            return jsonify(response), status_code
        transaction_id = credit_result.get('transaction_id')

        # 准备参数并启动任务
        # 自动检测图像宽高比
        aspect_ratio = image_service.detect_image_aspect_ratio(final_filepath)
        kwargs['aspect_ratio'] = aspect_ratio

        image_service.start_edit_task(task_id, final_filepath, final_prompt, model, **kwargs)
        
        # 准备响应
        response_data = {
            'task_id': task_id,
            'message': '任务已创建',
            'credits_cost': credits_cost
        }

        if current_user:
            response_data.update({
                'remaining_credits': current_user.available_credits,
                'daily_remaining': current_user.daily_remaining,
                'transaction_id': transaction_id
            })

        # 如果预处理成功，添加预处理信息
        if preprocess_success and preprocess_info.get('pixel_preprocessing', {}).get('resized', False):
            pixel_info = preprocess_info['pixel_preprocessing']
            response_data['preprocessing'] = {
                'pixel_adjustment': True,
                'original_size': f"{pixel_info['original_size'][0]}x{pixel_info['original_size'][1]}",
                'final_size': f"{pixel_info['final_size'][0]}x{pixel_info['final_size'][1]}",
                'pixel_reduction_percent': f"{pixel_info['pixel_reduction_percent']:.1f}%"
            }

        # 测试模式的处理应该在前端进行，后端不应该有特殊的测试模式逻辑

        response, status_code = create_response(True, response_data)
        return jsonify(response), status_code
        
    except Exception as e:
        response, status_code = create_response(False, error=str(e), status_code=500)
        return jsonify(response), status_code

@image_bp.route('/style', methods=['POST'])
@require_auth
def api_style():
    """API: 风格迁移"""
    try:
        # 验证上传的文件
        is_valid, error_msg, error_response = validate_uploaded_file(
            request, 'image', AppConfig.ALLOWED_EXTENSIONS
        )
        if not is_valid:
            response, status_code = create_response(False, error_msg)
            return jsonify(response), status_code

        file = request.files['image']

        # 验证内容描述
        prompt = request.form.get('prompt', '')
        is_valid, error_msg = validate_text_input(prompt, '内容描述')
        if not is_valid:
            response, status_code = create_response(False, error_msg)
            return jsonify(response), status_code

        # 获取当前用户（已通过 @require_auth 验证）
        current_user = get_current_user()
        user_id = current_user.id

        # 计算积分消费
        model = request.form.get('model', AppConfig.DEFAULT_MODELS['style'])
        kwargs = image_service.prepare_edit_parameters(request.form)
        credits_cost = credit_service.calculate_task_cost('style', model, kwargs)

        # 检查用户积分和限制
        limit_check = credit_service.check_user_limits(user_id, credits_cost)
        if not limit_check['success']:
            response, status_code = create_response(False, limit_check['message'], status_code=402)
            return jsonify(response), status_code

        # 翻译提示词（如果需要）
        translation_result = translate_text(prompt)
        if translation_result['success']:
            final_prompt = translation_result['text']
        else:
            final_prompt = prompt
        
        # 保存上传的文件
        filename = generate_timestamp_filename(secure_filename(file.filename))
        original_filepath = AppConfig.get_upload_path(filename)
        file.save(original_filepath)

        # 🆕 添加图片预处理 - 100万像素限制
        preprocessed_filename = f"preprocessed_{filename}"
        preprocessed_filepath = AppConfig.get_upload_path(preprocessed_filename)

        preprocess_success, preprocess_info = preprocess_image_for_bfl(
            original_filepath,
            preprocessed_filepath
        )

        if preprocess_success:
            # 使用预处理后的文件
            final_filepath = preprocessed_filepath
            print(f"✅ 图片预处理成功: {original_filepath} -> {preprocessed_filepath}")
            if preprocess_info.get('pixel_preprocessing', {}).get('resized', False):
                pixel_info = preprocess_info['pixel_preprocessing']
                print(f"📏 像素调整: {pixel_info['original_pixels']:,} -> {pixel_info['final_pixels']:,}")
        else:
            # 预处理失败，使用原文件
            final_filepath = original_filepath
            print(f"⚠️ 图片预处理失败，使用原文件: {preprocess_info.get('error', '未知错误')}")

        # 创建任务
        task_id = task_service.create_task(
            'style',
            original_prompt=prompt,
            final_prompt=final_prompt,
            model=model,
            reference_file=final_filepath,
            original_filename=file.filename,
            uploaded_filename=filename,
            preprocessed_filename=preprocessed_filename if preprocess_success else None,
            preprocessing_info=preprocess_info if preprocess_success else None,
            user_id=user_id,
            credits_cost=credits_cost
        )

        # 预扣积分
        credit_result = credit_service.consume_credits(
            user_id, credits_cost, f'风格迁移 - {model}', task_id
        )
        if not credit_result['success']:
            response, status_code = create_response(False, credit_result['message'], status_code=402)
            return jsonify(response), status_code
        transaction_id = credit_result.get('transaction_id')

        # 准备参数并启动任务
        image_service.start_style_transfer_task(task_id, final_filepath, final_prompt, model, **kwargs)
        
        # 准备响应
        response_data = {
            'task_id': task_id,
            'message': '任务已创建',
            'credits_cost': credits_cost
        }

        if current_user:
            response_data.update({
                'remaining_credits': current_user.available_credits,
                'daily_remaining': current_user.daily_remaining,
                'transaction_id': transaction_id
            })

        # 如果预处理成功，添加预处理信息
        if preprocess_success and preprocess_info.get('pixel_preprocessing', {}).get('resized', False):
            pixel_info = preprocess_info['pixel_preprocessing']
            response_data['preprocessing'] = {
                'pixel_adjustment': True,
                'original_size': f"{pixel_info['original_size'][0]}x{pixel_info['original_size'][1]}",
                'final_size': f"{pixel_info['final_size'][0]}x{pixel_info['final_size'][1]}",
                'pixel_reduction_percent': f"{pixel_info['pixel_reduction_percent']:.1f}%"
            }

        response, status_code = create_response(True, response_data)
        return jsonify(response), status_code
        
    except Exception as e:
        response, status_code = create_response(False, error=str(e), status_code=500)
        return jsonify(response), status_code

@image_bp.route('/status/<task_id>')
def api_status(task_id):
    """API: 获取任务状态"""
    task = task_service.get_task(task_id)
    if not task:
        response, status_code = create_response(False, '任务不存在或已过期', status_code=404)
        return jsonify(response), status_code
    
    # 保持向后兼容：原有 data 字段不变，只做增量扩展
    payload = {
        'data': task,                     # 旧字段
        'task': task,                     # 新字段：推荐前端使用
        'task_success': task.get('status') == 'completed'  # 便于快速判断
    }
    response, status_code = create_response(True, payload)
    return jsonify(response), status_code

@image_bp.route('/tasks')
def api_tasks():
    """API: 获取所有任务"""
    task_list = task_service.get_all_tasks()
    response, status_code = create_response(True, {'tasks': task_list})
    return jsonify(response), status_code 


@image_bp.route('/preview-cost', methods=['POST'])
# @optional_login - 改为不需要认证的路由
def preview_task_cost():
    """预览任务积分消费"""
    try:
        data = request.get_json()
        
        task_type = data.get('task_type', 'generate')  # generate, edit, style
        model = data.get('model', AppConfig.DEFAULT_MODELS.get(task_type, 'flux-kontext-pro'))
        parameters = data.get('parameters', {})
        
        # 计算积分消费
        credits_cost = credit_service.calculate_task_cost(task_type, model, parameters)
        
        # 获取当前用户信息（如果已登录）
        current_user = None
        try:
            verify_jwt_in_request(optional=True)
            user_id = get_jwt_identity()
            if user_id:
                from backend.models.user import User
                current_user = User.query.get(user_id)
        except:
            pass
        
        response_data = {
            'task_type': task_type,
            'model': model,
            'parameters': parameters,
            'credits_cost': credits_cost,
            'can_afford': False,  # 需要登录才能使用
            'user_info': None,
            'login_required': True
        }
        
        # 如果用户已登录，提供更详细的信息
        if current_user:
            can_afford = current_user.available_credits >= credits_cost
            daily_can_perform = current_user.daily_limit == -1 or current_user.daily_used < current_user.daily_limit

            response_data.update({
                'can_afford': can_afford and daily_can_perform,
                'login_required': False,
                'user_info': {
                    'available_credits': current_user.available_credits,
                    'daily_remaining': current_user.daily_remaining,
                    'daily_limit': current_user.daily_limit,
                    'daily_used': current_user.daily_used,
                    'user_type': current_user.user_type.value
                }
            })
            
            # 如果积分不足，提供建议
            if not can_afford:
                credits_needed = credits_cost - current_user.available_credits
                response_data['suggestion'] = {
                    'type': 'purchase_credits',
                    'message': f'您还需要 {credits_needed} 积分才能执行此任务',
                    'credits_needed': credits_needed
                }
            elif not daily_can_perform:
                response_data['suggestion'] = {
                    'type': 'upgrade_plan',
                    'message': f'今日使用次数已达上限，升级账户解除限制',
                    'current_limit': current_user.daily_limit
                }
        
        response, status_code = create_response(True, response_data)
        return jsonify(response), status_code
        
    except Exception as e:
        response, status_code = create_response(False, str(e), status_code=500)
        return jsonify(response), status_code