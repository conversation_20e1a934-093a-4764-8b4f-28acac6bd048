# BFL AI 项目架构解耦方案

> **方案设计**: 2025-01-03  
> **目标**: 实现图像处理模块与SaaS管理平台的完全解耦  
> **架构模式**: 核心+插件架构 (Core + Plugin Architecture)

## 📋 方案概述

### 解耦目标

将现有项目分离为两个完全独立的模块：

1. **图像处理平台** (Image Processing Platform)
   - BFL API调用 + 智能翻译功能
   - 可独立运行的本地图像生成工具
   - 无用户系统依赖，支持匿名使用

2. **SaaS业务管理平台** (SaaS Business Platform)
   - 用户管理 + 积分系统 + 支付系统
   - 通用的业务管理框架
   - 支持多种业务插件接入

### 核心价值

- ✅ **技术解耦**: 降低模块间依赖，提高可维护性
- ✅ **产品矩阵**: 形成多产品线，扩大市场覆盖
- ✅ **商业灵活**: 支持多种商业模式和部署方式
- ✅ **生态建设**: 支持第三方插件开发

## 🏗️ 技术可行性分析

### 现有架构优势

基于项目当前状态，解耦具备良好的技术基础：

| 架构层面 | 现状 | 解耦优势 |
|---------|------|----------|
| **路由层** | 蓝图分离 (image_bp vs user_bp) | ✅ 天然的模块边界 |
| **服务层** | 服务独立 (ImageService vs CreditService) | ✅ 业务逻辑已分离 |
| **数据层** | 任务表支持匿名用户 | ✅ 数据模型相对独立 |
| **前端层** | 组件化架构 | ✅ UI组件可复用 |

### 技术挑战与解决方案

**挑战1**: 用户认证的解耦
```python
# 解决方案：认证适配器模式
class AuthAdapter:
    def get_current_user(self):
        # 插件模式：可选择不同的认证方式
        if self.auth_mode == 'saas':
            return self.saas_auth.get_user()
        elif self.auth_mode == 'anonymous':
            return AnonymousUser()
        elif self.auth_mode == 'local':
            return LocalUser()
```

**挑战2**: 积分系统的解耦
```python
# 解决方案：计费接口抽象
class BillingInterface:
    def check_credits(self, user_id, task_type): pass
    def deduct_credits(self, user_id, amount): pass
    def get_pricing(self, task_type): pass

# 实现类
class SaaSBilling(BillingInterface): pass
class FreeBilling(BillingInterface): pass  # 本地免费模式
```

**挑战3**: 任务管理的解耦
```python
# 解决方案：任务处理器接口
class TaskProcessor:
    def process_task(self, task_data): pass
    def get_task_status(self, task_id): pass
    def cancel_task(self, task_id): pass
```

## 🎯 解耦架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    SaaS 业务管理平台 (Core)                    │
├─────────────────────────────────────────────────────────────┤
│  用户管理  │  积分系统  │  支付系统  │  插件管理  │  API网关   │
├─────────────────────────────────────────────────────────────┤
│                      插件接口层 (Plugin Interface)              │
├─────────────────────────────────────────────────────────────┤
│  图像处理插件  │  文档处理插件  │  音频处理插件  │  自定义插件   │
└─────────────────────────────────────────────────────────────┘
```

### 核心接口设计

#### 1. 业务插件接口 (IBusinessPlugin)

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, List

class IBusinessPlugin(ABC):
    """业务插件标准接口"""
    
    @abstractmethod
    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        pass
    
    @abstractmethod
    def get_task_types(self) -> List[str]:
        """获取支持的任务类型"""
        pass
    
    @abstractmethod
    def get_pricing_info(self) -> Dict[str, int]:
        """获取计费信息"""
        pass
    
    @abstractmethod
    def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务"""
        pass
    
    @abstractmethod
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        pass
    
    @abstractmethod
    def validate_task_data(self, task_data: Dict[str, Any]) -> bool:
        """验证任务数据"""
        pass
```

#### 2. 图像处理插件实现

```python
class ImageProcessingPlugin(IBusinessPlugin):
    """图像处理插件实现"""
    
    def __init__(self):
        self.image_service = ImageService()
        self.translator = UnifiedTranslator()
    
    def get_plugin_info(self) -> Dict[str, Any]:
        return {
            "name": "图像处理插件",
            "version": "2.0.0",
            "description": "基于BFL API的AI图像生成和编辑",
            "author": "BFL Team",
            "capabilities": ["generate", "edit", "style_transfer", "restore"]
        }
    
    def get_task_types(self) -> List[str]:
        return ["image_generation", "image_editing", "style_transfer", "image_restore"]
    
    def get_pricing_info(self) -> Dict[str, int]:
        return {
            "image_generation": 2,
            "image_editing": 3,
            "style_transfer": 4,
            "image_restore": 3
        }
    
    def process_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        task_type = task_data.get('type')
        
        if task_type == 'image_generation':
            return self._process_generation(task_data)
        elif task_type == 'image_editing':
            return self._process_editing(task_data)
        # ... 其他任务类型
    
    def _process_generation(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        # 翻译提示词
        prompt = task_data.get('prompt', '')
        if self._is_chinese(prompt):
            prompt = self.translator.translate(prompt)
        
        # 调用图像生成服务
        result = self.image_service.generate_image(
            prompt=prompt,
            model=task_data.get('model', 'flux-pro'),
            **task_data.get('parameters', {})
        )
        
        return {
            "status": "completed",
            "result": result,
            "output_path": result.get('image_path')
        }
```

#### 3. SaaS核心平台

```python
class SaaSCorePlatform:
    """SaaS核心平台"""
    
    def __init__(self):
        self.plugin_manager = PluginManager()
        self.user_service = UserService()
        self.credit_service = CreditService()
        self.billing_service = BillingService()
    
    def register_plugin(self, plugin: IBusinessPlugin):
        """注册业务插件"""
        self.plugin_manager.register(plugin)
    
    def process_user_task(self, user_id: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户任务（带权限和计费）"""
        
        # 1. 用户认证
        user = self.user_service.get_user(user_id)
        if not user or not user.is_active:
            return {"error": "用户认证失败"}
        
        # 2. 获取任务处理插件
        task_type = task_data.get('type')
        plugin = self.plugin_manager.get_plugin_for_task(task_type)
        if not plugin:
            return {"error": "不支持的任务类型"}
        
        # 3. 检查积分
        required_credits = plugin.get_pricing_info().get(task_type, 0)
        if not self.credit_service.check_credits(user_id, required_credits):
            return {"error": "积分不足"}
        
        # 4. 处理任务
        try:
            result = plugin.process_task(task_data)
            
            # 5. 扣除积分
            if result.get('status') == 'completed':
                self.credit_service.deduct_credits(user_id, required_credits)
            
            return result
            
        except Exception as e:
            return {"error": f"任务处理失败: {str(e)}"}
    
    def process_anonymous_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理匿名任务（无权限和计费）"""
        
        task_type = task_data.get('type')
        plugin = self.plugin_manager.get_plugin_for_task(task_type)
        
        if not plugin:
            return {"error": "不支持的任务类型"}
        
        return plugin.process_task(task_data)
```

### 数据库分离设计

#### 核心数据库 (SaaS Platform)
```sql
-- 用户相关表
users, user_profiles, user_auths
-- 积分和支付相关表  
credits_transactions, subscriptions, payments
-- 系统配置表
system_settings, plugin_configs
```

#### 业务数据库 (Image Processing)
```sql
-- 任务相关表
image_tasks, task_results, task_files
-- 业务特定配置
image_models, translation_cache
```

#### 数据同步机制
```python
class DataSyncService:
    """数据同步服务"""
    
    def sync_task_to_core(self, task_data: Dict[str, Any]):
        """将业务任务同步到核心平台"""
        core_task = {
            "id": task_data["id"],
            "user_id": task_data.get("user_id"),
            "type": task_data["type"],
            "status": task_data["status"],
            "credits_cost": task_data.get("credits_cost", 0),
            "created_at": task_data["created_at"],
            "completed_at": task_data.get("completed_at")
        }
        
        # 通过API同步到核心平台
        self.core_api.create_task_record(core_task)
```

## 🚀 实施路径建议

### 阶段1: 接口抽象和标准化 (2-3周)

**目标**: 建立插件接口规范，重构现有代码

**具体任务**:
1. **定义插件接口规范**
   ```bash
   # 创建插件接口模块
   mkdir -p backend/core/interfaces
   touch backend/core/interfaces/__init__.py
   touch backend/core/interfaces/business_plugin.py
   touch backend/core/interfaces/auth_adapter.py
   touch backend/core/interfaces/billing_interface.py
   ```

2. **重构现有服务层**
   ```python
   # 将ImageService适配为插件
   class ImageServiceAdapter(IBusinessPlugin):
       def __init__(self):
           self.image_service = ImageService()
           # ... 实现接口方法
   ```

3. **创建适配器层**
   ```python
   # 认证适配器
   class AuthAdapter:
       def __init__(self, mode='saas'):
           self.mode = mode
           if mode == 'saas':
               self.auth = SaaSAuth()
           elif mode == 'anonymous':
               self.auth = AnonymousAuth()
   ```

**验收标准**:
- ✅ 插件接口定义完成
- ✅ 现有功能通过适配器正常工作
- ✅ 单元测试覆盖率 > 80%

### 阶段2: 模块分离和独立化 (3-4周)

**目标**: 实现两个模块的物理分离

**具体任务**:
1. **创建独立的图像处理应用**
   ```bash
   # 创建独立的图像处理应用
   mkdir -p image_processing_app
   cp -r backend/services/image_service.py image_processing_app/
   cp -r Translation/ image_processing_app/
   cp -r BFL/ image_processing_app/
   ```

2. **重构SaaS核心平台**
   ```python
   # 创建核心平台应用
   class SaaSCoreApp:
       def __init__(self):
           self.plugin_manager = PluginManager()
           self.load_plugins()
       
       def load_plugins(self):
           # 动态加载插件
           for plugin_config in self.get_plugin_configs():
               plugin = self.load_plugin(plugin_config)
               self.plugin_manager.register(plugin)
   ```

3. **建立API通信机制**
   ```python
   # 插件API客户端
   class PluginAPIClient:
       def __init__(self, base_url):
           self.base_url = base_url
       
       def process_task(self, task_data):
           response = requests.post(
               f"{self.base_url}/api/tasks",
               json=task_data
           )
           return response.json()
   ```

**验收标准**:
- ✅ 图像处理模块可独立运行
- ✅ SaaS平台可独立运行
- ✅ API通信机制正常工作

### 阶段3: 优化和扩展 (2-3周)

**目标**: 性能优化和功能完善

**具体任务**:
1. **性能优化**
   - 实现异步任务处理
   - 添加缓存机制
   - 优化数据库查询

2. **监控和日志**
   - 添加性能监控
   - 实现分布式日志
   - 建立告警机制

3. **文档和示例**
   - 编写插件开发文档
   - 提供示例插件
   - 创建部署指南

**验收标准**:
- ✅ 系统性能满足要求
- ✅ 监控和日志完善
- ✅ 文档和示例完整

## 💼 商业价值评估

### 直接商业价值

1. **产品线扩展** (预期收入增长: 200-300%)
   - SaaS平台可支持多种业务场景
   - 图像处理可作为独立产品销售
   - 形成产品矩阵，增加收入来源

2. **市场机会扩大**
   - B2B市场: SaaS平台授权
   - 开发者生态: 第三方插件
   - 企业定制: 定制化解决方案

3. **成本优化**
   - 开发成本: 模块复用，降低重复开发
   - 运维成本: 独立部署，按需扩展
   - 人力成本: 团队分工更清晰

### 技术价值

1. **架构优势**
   - 提高系统可扩展性
   - 降低技术债务
   - 支持敏捷开发

2. **风险控制**
   - 业务风险分散
   - 技术风险隔离
   - 更灵活的商业模式

### 风险评估

**技术风险** (低)
- 现有架构基础良好
- 渐进式迁移降低风险
- 完善的测试保障

**商业风险** (中)
- 市场接受度需要验证
- 竞争对手可能跟进
- 需要投入营销资源

**运营风险** (低)
- 团队技术能力充足
- 项目管理经验丰富
- 有完整的实施计划

## 📋 总结建议

### 核心建议

1. **立即启动**: 技术可行性高，商业价值明确
2. **分阶段实施**: 降低风险，确保稳定性
3. **重点关注接口设计**: 良好的接口是成功的关键
4. **建立生态**: 支持第三方插件开发

### 成功关键因素

1. **技术层面**
   - 接口设计的标准化和稳定性
   - 性能和可靠性保障
   - 完善的文档和示例

2. **商业层面**
   - 明确的产品定位和差异化
   - 有效的市场推广策略
   - 良好的客户服务体系

3. **团队层面**
   - 清晰的分工和协作机制
   - 持续的技术学习和改进
   - 敏捷的项目管理方法

这个解耦方案将为项目带来巨大的技术和商业价值，建议尽快启动实施。

## 🛠️ 具体实施代码示例

### 插件管理器实现

```python
# backend/core/plugin_manager.py
import importlib
import os
from typing import Dict, List, Optional
from .interfaces.business_plugin import IBusinessPlugin

class PluginManager:
    """插件管理器"""

    def __init__(self):
        self.plugins: Dict[str, IBusinessPlugin] = {}
        self.task_type_mapping: Dict[str, str] = {}

    def register_plugin(self, plugin: IBusinessPlugin):
        """注册插件"""
        plugin_info = plugin.get_plugin_info()
        plugin_name = plugin_info['name']

        self.plugins[plugin_name] = plugin

        # 建立任务类型映射
        for task_type in plugin.get_task_types():
            self.task_type_mapping[task_type] = plugin_name

        print(f"插件注册成功: {plugin_name}")

    def get_plugin_for_task(self, task_type: str) -> Optional[IBusinessPlugin]:
        """根据任务类型获取插件"""
        plugin_name = self.task_type_mapping.get(task_type)
        return self.plugins.get(plugin_name) if plugin_name else None

    def load_plugins_from_directory(self, plugin_dir: str):
        """从目录加载插件"""
        for filename in os.listdir(plugin_dir):
            if filename.endswith('_plugin.py'):
                module_name = filename[:-3]
                try:
                    module = importlib.import_module(f"plugins.{module_name}")
                    plugin_class = getattr(module, 'Plugin')
                    plugin = plugin_class()
                    self.register_plugin(plugin)
                except Exception as e:
                    print(f"加载插件失败 {filename}: {e}")

    def get_all_plugins(self) -> List[Dict]:
        """获取所有插件信息"""
        return [plugin.get_plugin_info() for plugin in self.plugins.values()]
```

### 配置管理系统

```python
# backend/core/config_manager.py
import yaml
from typing import Dict, Any

class ConfigManager:
    """配置管理器"""

    def __init__(self, config_file: str = 'config/app_config.yaml'):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            return self.get_default_config()

    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'mode': 'integrated',  # integrated, saas_only, plugin_only
            'auth': {
                'type': 'saas',  # saas, anonymous, local
                'session_timeout': 3600
            },
            'billing': {
                'type': 'saas',  # saas, free, custom
                'default_credits': 10
            },
            'plugins': {
                'enabled': ['image_processing'],
                'auto_load': True,
                'plugin_dir': 'plugins'
            },
            'database': {
                'core_db': 'sqlite:///core.db',
                'plugin_db': 'sqlite:///plugins.db'
            }
        }

    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            value = value.get(k, {})
        return value if value != {} else default

    def is_saas_mode(self) -> bool:
        """是否为SaaS模式"""
        return self.get('mode') in ['integrated', 'saas_only']

    def is_plugin_mode(self) -> bool:
        """是否为插件模式"""
        return self.get('mode') in ['integrated', 'plugin_only']
```

### 部署配置示例

```yaml
# config/saas_mode.yaml - SaaS平台模式
mode: saas_only
auth:
  type: saas
  session_timeout: 3600
billing:
  type: saas
  stripe_enabled: true
plugins:
  enabled: ['image_processing', 'document_processing']
  auto_load: true
database:
  core_db: postgresql://user:pass@localhost/saas_core

---
# config/standalone_mode.yaml - 独立图像处理模式
mode: plugin_only
auth:
  type: anonymous
billing:
  type: free
plugins:
  enabled: ['image_processing']
  auto_load: false
database:
  plugin_db: sqlite:///image_processing.db
```

### API网关实现

```python
# backend/core/api_gateway.py
from flask import Blueprint, request, jsonify
from .plugin_manager import PluginManager
from .config_manager import ConfigManager

api_gateway = Blueprint('api_gateway', __name__, url_prefix='/api/v1')

class APIGateway:
    def __init__(self, plugin_manager: PluginManager, config_manager: ConfigManager):
        self.plugin_manager = plugin_manager
        self.config_manager = config_manager

    def route_task_request(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """路由任务请求"""
        task_type = task_data.get('type')

        # 获取处理插件
        plugin = self.plugin_manager.get_plugin_for_task(task_type)
        if not plugin:
            return {"error": "不支持的任务类型", "code": 400}

        # 验证任务数据
        if not plugin.validate_task_data(task_data):
            return {"error": "任务数据验证失败", "code": 400}

        # 根据模式处理任务
        if self.config_manager.is_saas_mode():
            return self._process_saas_task(task_data, plugin)
        else:
            return self._process_standalone_task(task_data, plugin)

    def _process_saas_task(self, task_data: Dict[str, Any], plugin: IBusinessPlugin):
        """SaaS模式任务处理"""
        # 用户认证和积分检查
        user_id = task_data.get('user_id')
        if not user_id:
            return {"error": "需要用户认证", "code": 401}

        # 检查积分等业务逻辑
        # ...

        return plugin.process_task(task_data)

    def _process_standalone_task(self, task_data: Dict[str, Any], plugin: IBusinessPlugin):
        """独立模式任务处理"""
        # 直接处理，无需认证和计费
        return plugin.process_task(task_data)

@api_gateway.route('/tasks', methods=['POST'])
def create_task():
    """创建任务API"""
    task_data = request.get_json()

    gateway = current_app.api_gateway
    result = gateway.route_task_request(task_data)

    if 'error' in result:
        return jsonify(result), result.get('code', 500)

    return jsonify(result), 200

@api_gateway.route('/plugins', methods=['GET'])
def list_plugins():
    """获取插件列表API"""
    gateway = current_app.api_gateway
    plugins = gateway.plugin_manager.get_all_plugins()
    return jsonify({"plugins": plugins}), 200
```

## 📦 部署方案

### Docker化部署

```dockerfile
# Dockerfile.saas - SaaS平台
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ ./backend/
COPY config/saas_mode.yaml ./config/app_config.yaml

EXPOSE 5000

CMD ["python", "-m", "backend.app_new"]

---
# Dockerfile.image_processing - 图像处理插件
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY image_processing_app/ ./
COPY config/standalone_mode.yaml ./config/app_config.yaml

EXPOSE 5001

CMD ["python", "app.py"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  saas-platform:
    build:
      context: .
      dockerfile: Dockerfile.saas
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=************************************/saas_core
    depends_on:
      - postgres
      - redis

  image-processing:
    build:
      context: .
      dockerfile: Dockerfile.image_processing
    ports:
      - "5001:5001"
    environment:
      - BFL_API_KEY=${BFL_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: saas_core
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## 🔄 迁移脚本

```python
# scripts/migrate_to_decoupled.py
"""
项目解耦迁移脚本
"""
import os
import shutil
import sqlite3
from pathlib import Path

class DecouplingMigrator:
    def __init__(self, source_dir: str, target_dir: str):
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)

    def migrate_image_processing_module(self):
        """迁移图像处理模块"""
        print("开始迁移图像处理模块...")

        # 创建目标目录
        image_app_dir = self.target_dir / "image_processing_app"
        image_app_dir.mkdir(exist_ok=True)

        # 复制核心文件
        files_to_copy = [
            "BFL/",
            "Translation/",
            "backend/services/image_service.py",
            "backend/utils/image_preprocessor.py",
            "backend/utils/validators.py"
        ]

        for file_path in files_to_copy:
            source = self.source_dir / file_path
            target = image_app_dir / file_path

            if source.is_dir():
                shutil.copytree(source, target, dirs_exist_ok=True)
            else:
                target.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, target)

        # 创建独立的应用入口
        self._create_image_app_entry(image_app_dir)

        print("图像处理模块迁移完成")

    def migrate_saas_platform(self):
        """迁移SaaS平台"""
        print("开始迁移SaaS平台...")

        # 创建目标目录
        saas_dir = self.target_dir / "saas_platform"
        saas_dir.mkdir(exist_ok=True)

        # 复制SaaS相关文件
        saas_files = [
            "backend/models/",
            "backend/auth/",
            "backend/services/credit_service.py",
            "backend/routes/user_routes.py",
            "backend/routes/admin_routes.py",
            "backend/config/",
            "backend/migrations/"
        ]

        for file_path in saas_files:
            source = self.source_dir / file_path
            target = saas_dir / file_path

            if source.is_dir():
                shutil.copytree(source, target, dirs_exist_ok=True)
            else:
                target.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(source, target)

        # 创建SaaS平台入口
        self._create_saas_app_entry(saas_dir)

        print("SaaS平台迁移完成")

    def _create_image_app_entry(self, app_dir: Path):
        """创建图像处理应用入口"""
        app_content = '''
from flask import Flask, request, jsonify
from image_service import ImageService
from Translation.unified_translator import UnifiedTranslator

app = Flask(__name__)
image_service = ImageService()
translator = UnifiedTranslator()

@app.route('/api/tasks', methods=['POST'])
def process_task():
    task_data = request.get_json()
    # 处理图像任务
    result = image_service.process_task(task_data)
    return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001, debug=True)
'''

        with open(app_dir / "app.py", "w", encoding="utf-8") as f:
            f.write(app_content)

    def _create_saas_app_entry(self, app_dir: Path):
        """创建SaaS平台应用入口"""
        app_content = '''
from flask import Flask
from backend.core.plugin_manager import PluginManager
from backend.core.config_manager import ConfigManager
from backend.core.api_gateway import api_gateway

def create_app():
    app = Flask(__name__)

    # 初始化核心组件
    config_manager = ConfigManager()
    plugin_manager = PluginManager()

    # 注册API网关
    app.register_blueprint(api_gateway)

    # 加载插件
    if config_manager.get('plugins.auto_load'):
        plugin_manager.load_plugins_from_directory(
            config_manager.get('plugins.plugin_dir')
        )

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
'''

        with open(app_dir / "app.py", "w", encoding="utf-8") as f:
            f.write(app_content)

# 使用示例
if __name__ == "__main__":
    migrator = DecouplingMigrator(".", "./decoupled_apps")
    migrator.migrate_image_processing_module()
    migrator.migrate_saas_platform()
    print("解耦迁移完成！")
```

这个完整的解耦方案提供了从技术可行性分析到具体实施代码的全套解决方案，将帮助您实现项目的完全解耦，为未来的商业化发展奠定坚实基础。
