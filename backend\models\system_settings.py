"""
系统设置相关数据模型
"""
from .database import db, BaseModel
import uuid


class SystemSetting(BaseModel):
    """系统设置模型"""
    __tablename__ = 'system_settings'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    setting_type = db.Column(db.String(20), default='string')  # string, int, float, bool, json
    
    @classmethod
    def get_setting(cls, key, default=None):
        """获取系统设置值"""
        setting = cls.query.filter_by(key=key).first()
        if not setting:
            return default
        
        # 根据类型转换值
        if setting.setting_type == 'int':
            return int(setting.value) if setting.value else default
        elif setting.setting_type == 'float':
            return float(setting.value) if setting.value else default
        elif setting.setting_type == 'bool':
            return setting.value.lower() in ('true', '1', 'yes') if setting.value else default
        elif setting.setting_type == 'json':
            import json
            return json.loads(setting.value) if setting.value else default
        else:
            return setting.value if setting.value else default
    
    @classmethod
    def set_setting(cls, key, value, description="", setting_type='string'):
        """设置系统设置值"""
        setting = cls.query.filter_by(key=key).first()
        
        # 转换值为字符串
        if setting_type == 'json':
            import json
            value = json.dumps(value)
        else:
            value = str(value)
        
        if setting:
            setting.value = value
            setting.description = description
            setting.setting_type = setting_type
        else:
            setting = cls(
                key=key,
                value=value,
                description=description,
                setting_type=setting_type
            )
            db.session.add(setting)
        
        db.session.commit()
        return setting
    
    @classmethod
    def get_all_settings(cls):
        """获取所有系统设置"""
        settings = cls.query.all()
        result = {}
        for setting in settings:
            result[setting.key] = cls.get_setting(setting.key)
        return result
    
    def __repr__(self):
        return f'<SystemSetting {self.key}={self.value}>'


def init_system_settings():
    """初始化系统设置"""
    default_settings = [
        # 用户限制设置
        ('free_daily_limit', '5', '免费用户每日生成限制', 'int'),
        ('premium_daily_limit', '50', '付费用户每日生成限制', 'int'),
        ('pro_daily_limit', '200', 'Pro用户每日生成限制', 'int'),
        
        # 积分消费设置
        ('credit_cost_generate', '1', '图像生成积分消费', 'int'),
        ('credit_cost_edit', '2', '图像编辑积分消费', 'int'),
        ('credit_cost_style', '3', '风格迁移积分消费', 'int'),
        ('credit_cost_high_res', '1', '高分辨率额外积分消费', 'int'),
        ('credit_cost_commercial_multiplier', '3', '商业授权积分倍数', 'int'),
        
        # 模型积分倍数
        ('model_multiplier_flux_dev', '0.8', 'Flux Dev模型积分倍数', 'float'),
        ('model_multiplier_flux_pro', '1.0', 'Flux Pro模型积分倍数', 'float'),
        ('model_multiplier_flux_kontext_pro', '1.5', 'Flux Kontext Pro模型积分倍数', 'float'),
        ('model_multiplier_flux_kontext_max', '2.0', 'Flux Kontext Max模型积分倍数', 'float'),
        ('model_multiplier_flux_pro_ultra', '3.0', 'Flux Pro Ultra模型积分倍数', 'float'),
        
        # 系统功能开关
        ('user_system_enabled', 'true', '是否启用用户系统', 'bool'),
        ('payment_system_enabled', 'false', '是否启用支付系统', 'bool'),
        ('watermark_enabled', 'true', '是否启用水印', 'bool'),
        ('maintenance_mode', 'false', '维护模式开关', 'bool'),
        ('registration_enabled', 'true', '是否允许用户注册', 'bool'),
        
        # 新用户设置
        ('new_user_credits', '10', '新用户初始积分', 'int'),
        ('new_user_daily_limit', '5', '新用户每日限制', 'int'),
        ('email_verification_required', 'false', '是否需要邮箱验证', 'bool'),
        
        # 文件设置
        ('max_file_size', '16777216', '最大文件大小（字节）', 'int'),  # 16MB
        ('allowed_file_types', '["png", "jpg", "jpeg", "gif", "webp"]', '允许的文件类型', 'json'),
        
        # 任务设置
        ('max_concurrent_tasks', '10', '最大并发任务数', 'int'),
        ('task_timeout', '300', '任务超时时间（秒）', 'int'),
        ('cleanup_old_tasks_days', '30', '清理旧任务的天数', 'int'),
        
        # 安全设置
        ('max_login_attempts', '5', '最大登录尝试次数', 'int'),
        ('login_lockout_duration', '300', '登录锁定时长（秒）', 'int'),
        ('jwt_access_token_expires', '3600', 'JWT访问令牌过期时间（秒）', 'int'),
        ('jwt_refresh_token_expires', '2592000', 'JWT刷新令牌过期时间（秒）', 'int'),
        
        # 邮件设置
        ('smtp_server', 'smtp.gmail.com', 'SMTP服务器', 'string'),
        ('smtp_port', '587', 'SMTP端口', 'int'),
        ('smtp_use_tls', 'true', '是否使用TLS', 'bool'),
        ('from_email', '<EMAIL>', '发件人邮箱', 'string'),
        
        # 社交功能设置
        ('public_gallery_enabled', 'true', '是否启用公共画廊', 'bool'),
        ('user_can_share', 'true', '用户是否可以分享作品', 'bool'),
        ('content_moderation_enabled', 'true', '是否启用内容审核', 'bool'),
        
        # 统计和分析
        ('analytics_enabled', 'true', '是否启用数据分析', 'bool'),
        ('user_behavior_tracking', 'true', '是否跟踪用户行为', 'bool'),
        
        # API设置
        ('api_rate_limit', '100', 'API速率限制（每小时）', 'int'),
        ('api_enabled', 'false', '是否启用API接口', 'bool'),
    ]
    
    for key, value, description, setting_type in default_settings:
        existing = SystemSetting.query.filter_by(key=key).first()
        if not existing:
            setting = SystemSetting(
                key=key,
                value=value,
                description=description,
                setting_type=setting_type
            )
            db.session.add(setting)
    
    try:
        db.session.commit()
        print("系统设置初始化完成")
    except Exception as e:
        db.session.rollback()
        print(f"系统设置初始化失败: {e}")


class UserLimit(BaseModel):
    """用户限制配置模型"""
    __tablename__ = 'user_limits'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    user_type = db.Column(db.String(20), unique=True, nullable=False)  # free, premium, pro, enterprise
    daily_limit = db.Column(db.Integer, default=5, nullable=False)
    monthly_limit = db.Column(db.Integer, default=150, nullable=False)
    concurrent_tasks = db.Column(db.Integer, default=1, nullable=False)
    max_file_size = db.Column(db.Integer, default=16777216, nullable=False)  # 16MB
    features = db.Column(db.JSON)  # 可用功能列表
    
    @classmethod
    def get_limits(cls, user_type):
        """获取用户类型的限制"""
        limit = cls.query.filter_by(user_type=user_type).first()
        if limit:
            return limit.to_dict()
        
        # 返回默认限制
        defaults = {
            'free': {'daily_limit': 5, 'monthly_limit': 150, 'concurrent_tasks': 1},
            'premium': {'daily_limit': 50, 'monthly_limit': 1500, 'concurrent_tasks': 3},
            'pro': {'daily_limit': 200, 'monthly_limit': 6000, 'concurrent_tasks': 5},
            'enterprise': {'daily_limit': -1, 'monthly_limit': -1, 'concurrent_tasks': 10}
        }
        
        return defaults.get(user_type, defaults['free'])
    
    def __repr__(self):
        return f'<UserLimit {self.user_type} daily:{self.daily_limit}>'
