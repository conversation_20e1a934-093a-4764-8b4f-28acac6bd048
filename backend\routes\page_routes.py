from flask import Blueprint, render_template, send_from_directory, current_app, redirect, url_for
import os
# JWT import removed - using simple auth system
from backend.config.app_config import AppConfig
from backend.routes.simple_auth_routes import require_auth, get_current_user
from backend.routes.simple_auth_routes import require_admin

# 创建页面路由蓝图
page_bp = Blueprint('pages', __name__)

@page_bp.route('/')
def index():
    """主页"""
    return render_template('index.html')

@page_bp.route('/generate')
def generate_page():
    """图像生成页面"""
    return render_template('generate.html')

@page_bp.route('/edit')
def edit_page():
    """图像编辑页面"""
    return render_template('edit.html')

@page_bp.route('/style')
def style_page():
    """风格迁移页面"""
    return render_template('style.html')

@page_bp.route('/translate')
def translate_page():
    """提示词翻译页面"""
    return render_template('translate.html', config=AppConfig.TRANSLATION_CONFIG)

@page_bp.route('/restore')
def restore_page():
    """旧照片修复页面"""
    return render_template('restore.html')

@page_bp.route('/compare')
def compare_page():
    """预处理演示页面 - 原图与裁切对比"""
    return render_template('compare.html')

@page_bp.route('/gallery')
def gallery():
    """图像画廊页面"""
    # 获取所有输出图像
    images = []
    if os.path.exists(AppConfig.OUTPUT_FOLDER):
        for filename in os.listdir(AppConfig.OUTPUT_FOLDER):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
                images.append(filename)
    
    # 按修改时间排序（最新的在前）
    images.sort(
        key=lambda x: os.path.getmtime(os.path.join(AppConfig.OUTPUT_FOLDER, x)), 
        reverse=True
    )
    
    return render_template('gallery.html', images=images)

@page_bp.route('/compare_widget_demo')
def compare_widget_demo():
    return render_template('compare_widget_demo.html')

@page_bp.route('/.well-known/appspecific/com.chrome.devtools.json')
def handle_chrome_devtools():
    """处理Chrome DevTools的请求"""
    return {}, 200, {'Content-Type': 'application/json'}

# 用户系统页面路由
@page_bp.route('/login')
def login_page():
    """登录页面"""
    return render_template('auth/login.html')

@page_bp.route('/simple-login')
def simple_login():
    """简化登录页面"""
    return render_template('auth/simple_login.html')

@page_bp.route('/test-auth')
def test_auth():
    """认证系统测试页面"""
    return render_template('test_simple_auth.html')

@page_bp.route('/register')
def register_page():
    """用户注册页面"""
    return render_template('auth/register.html')

@page_bp.route('/profile')
@page_bp.route('/profile/<tab>')
@require_auth
def profile_page(tab=None):
    """用户个人资料页面"""
    user = get_current_user()
    # 验证标签页参数
    valid_tabs = ['overview', 'info', 'credits', 'tasks', 'security', 'preferences', 'subscription']
    if tab and tab not in valid_tabs:
        tab = 'overview'  # 默认标签页
    return render_template('auth/profile.html', user=user, active_tab=tab or 'overview')

@page_bp.route('/credits')
@require_auth
def credits_store_page():
    """积分商城页面"""
    user = get_current_user()
    return render_template('credits.html', user=user)

@page_bp.route('/user/credits')
@require_auth
def user_credits_page():
    """用户积分管理页面"""
    user = get_current_user()
    return render_template('auth/credits.html', user=user)

@page_bp.route('/dashboard')
@require_auth
def dashboard_page():
    """用户仪表板页面"""
    user = get_current_user()
    return render_template('auth/dashboard.html', user=user)

# 管理员页面路由
@page_bp.route('/admin')
@page_bp.route('/admin/')
@require_admin
def admin_dashboard_page():
    """管理员仪表板页面"""
    return render_template('admin/dashboard.html')

@page_bp.route('/admin/users')
@require_admin
def admin_users_page():
    """管理员用户管理页面"""
    return render_template('admin/users.html')

@page_bp.route('/admin/settings')
@require_admin
def admin_settings_page():
    """管理员系统设置页面"""
    return render_template('admin/settings.html')

@page_bp.route('/admin/statistics')
@require_admin
def admin_statistics_page():
    """管理员数据统计页面"""
    return render_template('admin/statistics.html')

@page_bp.route('/test-frontend')
def test_frontend_page():
    """前端测试页面"""
    return render_template('test_frontend.html')

@page_bp.route('/test-auth')
def test_auth_page():
    """认证测试页面"""
    return render_template('test_auth.html')