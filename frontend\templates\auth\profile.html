{% extends "base.html" %}

{% block title %}个人中心 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-user-circle"></i> 个人中心</h2>
                <div class="btn-group">
                    <a href="{{ url_for('pages.dashboard_page') }}" class="btn btn-outline-primary">
                        <i class="fas fa-tachometer-alt"></i> 返回仪表板
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-list"></i> 设置菜单</h6>
                </div>
                <div class="list-group list-group-flush">
                    <a href="#" class="list-group-item list-group-item-action {% if active_tab == 'info' %}active{% endif %}" onclick="showTab('info')">
                         <i class="fas fa-user-edit"></i> 基本信息
                     </a>
                     <a href="#" class="list-group-item list-group-item-action {% if active_tab == 'security' %}active{% endif %}" onclick="showTab('security')">
                         <i class="fas fa-shield-alt"></i> 安全设置
                     </a>
                     <a href="#" class="list-group-item list-group-item-action {% if active_tab == 'preferences' %}active{% endif %}" onclick="showTab('preferences')">
                         <i class="fas fa-cog"></i> 偏好设置
                     </a>
                     <a href="#" class="list-group-item list-group-item-action {% if active_tab == 'subscription' %}active{% endif %}" onclick="showTab('subscription')">
                         <i class="fas fa-crown"></i> 订阅管理
                     </a>
                </div>
            </div>
        </div>
        
        <!-- 右侧内容 -->
        <div class="col-md-9">
            <!-- 基本信息标签页 -->
            <div id="info-tab" class="tab-content" {% if active_tab != 'info' %}style="display: none;"{% endif %}>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-edit"></i> 基本信息</h5>
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">用户名</label>
                                        <input type="text" class="form-control" id="username" readonly>
                                        <small class="text-muted">用户名不可修改</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">邮箱</label>
                                        <input type="email" class="form-control" id="email">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">手机号</label>
                                        <input type="tel" class="form-control" id="phone" readonly>
                                        <small class="text-muted">手机号不可修改</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="displayName" class="form-label">显示名称</label>
                                        <input type="text" class="form-control" id="displayName">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="bio" class="form-label">个人简介</label>
                                <textarea class="form-control" id="bio" rows="3" placeholder="介绍一下自己..."></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="website" class="form-label">个人网站</label>
                                        <input type="url" class="form-control" id="website" placeholder="https://...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="location" class="form-label">所在地</label>
                                        <input type="text" class="form-control" id="location" placeholder="城市, 国家">
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存更改
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 安全设置标签页 -->
            <div id="security-tab" class="tab-content" {% if active_tab != 'security' %}style="display: none;"{% endif %}>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> 安全设置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 修改密码 -->
                        <div class="mb-4">
                            <h6>修改密码</h6>
                            <form id="passwordForm">
                                <div class="mb-3">
                                    <label for="currentPassword" class="form-label">当前密码</label>
                                    <input type="password" class="form-control" id="currentPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label for="newPassword" class="form-label">新密码</label>
                                    <input type="password" class="form-control" id="newPassword" required>
                                </div>
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">确认新密码</label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key"></i> 更新密码
                                </button>
                            </form>
                        </div>

                        <!-- 登录历史 -->
                        <div class="mb-4">
                            <h6>最近登录记录</h6>
                            <div id="loginHistory">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 正在加载...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 偏好设置标签页 -->
            <div id="preferences-tab" class="tab-content" {% if active_tab != 'preferences' %}style="display: none;"{% endif %}>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> 偏好设置</h5>
                    </div>
                    <div class="card-body">
                        <form id="preferencesForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="theme" class="form-label">主题</label>
                                        <select class="form-select" id="theme">
                                            <option value="light">浅色主题</option>
                                            <option value="dark">深色主题</option>
                                            <option value="auto">跟随系统</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">语言</label>
                                        <select class="form-select" id="language">
                                            <option value="zh-CN">简体中文</option>
                                            <option value="en-US">English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="defaultModel" class="form-label">默认模型</label>
                                        <select class="form-select" id="defaultModel">
                                            <option value="flux-kontext-pro">Flux Kontext Pro</option>
                                            <option value="flux-dev">Flux Dev</option>
                                            <option value="flux-schnell">Flux Schnell</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="defaultAspectRatio" class="form-label">默认比例</label>
                                        <select class="form-select" id="defaultAspectRatio">
                                            <option value="1:1">1:1 (正方形)</option>
                                            <option value="16:9">16:9 (宽屏)</option>
                                            <option value="9:16">9:16 (竖屏)</option>
                                            <option value="4:3">4:3 (标准)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <h6 class="mt-4">通知设置</h6>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="emailNotifications">
                                <label class="form-check-label" for="emailNotifications">
                                    邮件通知
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="taskNotifications">
                                <label class="form-check-label" for="taskNotifications">
                                    任务完成通知
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="promotionNotifications">
                                <label class="form-check-label" for="promotionNotifications">
                                    促销活动通知
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 订阅管理标签页 -->
            <div id="subscription-tab" class="tab-content" {% if active_tab != 'subscription' %}style="display: none;"{% endif %}>
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-crown"></i> 订阅管理</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">当前套餐</h6>
                                        <h4 class="text-primary" id="currentPlan">免费用户</h4>
                                        <p class="text-muted">每日限制: <span id="dailyLimitDisplay">10</span> 积分</p>
                                        <a href="{{ url_for('pages.credits_store_page') }}" class="btn btn-primary">升级套餐</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">积分余额</h6>
                                        <h4 class="text-success" id="creditBalance">0</h4>
                                        <p class="text-muted">可用积分</p>
                                        <a href="{{ url_for('pages.credits_store_page') }}" class="btn btn-success">购买积分</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 初始化
    loadUserProfile();
    loadPreferences();
    
    // 根据当前激活的标签页加载数据
    const currentTab = '{{ active_tab }}';
    loadTabData(currentTab);
    
    // 个人资料表单提交
    $('#profileForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            email: $('#email').val(),
            display_name: $('#displayName').val(),
            website: $('#website').val(),
            location: $('#location').val(),
            bio: $('#bio').val()
        };
        
        updateProfile(formData);
    });
    
    // 密码修改表单提交
    $('#passwordForm').on('submit', function(e) {
        e.preventDefault();
        
        const newPassword = $('#newPassword').val();
        const confirmPassword = $('#confirmPassword').val();
        
        if (newPassword !== confirmPassword) {
            showAlert('danger', '新密码不匹配');
            return;
        }
        
        const formData = {
            old_password: $('#currentPassword').val(),
            new_password: newPassword
        };
        
        updatePassword(formData);
    });
    
    // 偏好设置表单提交
    $('#preferencesForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            theme: $('#theme').val(),
            language: $('#language').val(),
            default_model: $('#defaultModel').val(),
            default_aspect_ratio: $('#defaultAspectRatio').val(),
            email_notifications: $('#emailNotifications').is(':checked'),
            task_notifications: $('#taskNotifications').is(':checked'),
            promotion_notifications: $('#promotionNotifications').is(':checked')
        };
        
        updatePreferences(formData);
    });
});

// 获取认证头部
function getAuthHeaders() {
    const token = localStorage.getItem('access_token') || getCookie('access_token');
    return token ? { 'Authorization': 'Bearer ' + token } : {};
}

// 获取Cookie
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
}

// 加载用户资料
function loadUserProfile() {
    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(response) {
            if (response.success) {
                populateProfileForm(response.user);
            }
        },
        error: function() {
            showAlert('danger', '加载用户资料失败');
        }
    });
}

// 填充个人资料表单
function populateProfileForm(user) {
    $('#username').val(user.username || '');
    $('#email').val(user.email || '');
    $('#phone').val(user.phone || '');
    $('#displayName').val(user.display_name || '');
    $('#bio').val(user.bio || '');
    $('#website').val(user.website || '');
    $('#location').val(user.location || '');
    
    // 更新订阅信息
    $('#currentPlan').text(getUserTypeText(user.user_type));
    $('#dailyLimitDisplay').text(user.daily_limit === -1 ? '无限制' : user.daily_limit);
    $('#creditBalance').text(user.available_credits || 0);
}

function getUserTypeText(userType) {
    const types = {
        'free': '免费用户',
        'premium': '付费用户',
        'pro': 'Pro用户',
        'enterprise': '企业用户'
    };
    return types[userType] || userType;
}

// 更新个人资料
function updateProfile(formData) {
    $.ajax({
        url: '/api/user/profile',
        method: 'PUT',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert('success', '个人资料更新成功');
            } else {
                showAlert('danger', response.message || '更新失败');
            }
        },
        error: function() {
            showAlert('danger', '更新个人资料失败');
        }
    });
}

// 更新密码
function updatePassword(formData) {
    $.ajax({
        url: '/api/user/change-password',
        method: 'POST',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert('success', '密码更新成功');
                $('#passwordForm')[0].reset();
            } else {
                showAlert('danger', response.message || '密码更新失败');
            }
        },
        error: function() {
            showAlert('danger', '密码更新失败');
        }
    });
}

// 加载偏好设置
function loadPreferences() {
    $.ajax({
        url: '/api/user/preferences',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(response) {
            if (response.success) {
                populatePreferencesForm(response.preferences);
            }
        },
        error: function() {
            console.log('加载偏好设置失败');
        }
    });
}

// 填充偏好设置表单
function populatePreferencesForm(preferences) {
    $('#theme').val(preferences.theme || 'light');
    $('#language').val(preferences.language || 'zh-CN');
    $('#defaultModel').val(preferences.default_model || 'flux-kontext-pro');
    $('#defaultAspectRatio').val(preferences.default_aspect_ratio || '1:1');
    $('#emailNotifications').prop('checked', preferences.email_notifications !== false);
    $('#taskNotifications').prop('checked', preferences.task_notifications !== false);
    $('#promotionNotifications').prop('checked', preferences.promotion_notifications === true);
}

// 更新偏好设置
function updatePreferences(formData) {
    $.ajax({
        url: '/api/user/preferences',
        method: 'PUT',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify(formData),
        success: function(response) {
            if (response.success) {
                showAlert('success', '偏好设置保存成功');
            } else {
                showAlert('danger', response.message || '保存失败');
            }
        },
        error: function() {
            showAlert('danger', '保存偏好设置失败');
        }
    });
}

// 根据标签页加载数据
function loadTabData(tab) {
    switch(tab) {
        case 'security':
            loadLoginHistory();
            break;
        default:
            // 其他标签页不需要额外的数据加载
            break;
    }
}

// 加载登录历史
function loadLoginHistory() {
    $.ajax({
        url: '/api/user/login-history',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(response) {
            if (response.success) {
                displayLoginHistory(response.history);
            }
        },
        error: function() {
            $('#loginHistory').html('<p class="text-danger">加载登录历史失败</p>');
        }
    });
}

// 显示登录历史
function displayLoginHistory(history) {
    if (!history || history.length === 0) {
        $('#loginHistory').html('<p class="text-muted">暂无登录记录</p>');
        return;
    }
    
    let html = '<div class="list-group">';
    history.forEach(function(record) {
        const date = new Date(record.login_time).toLocaleString();
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${record.ip_address || '未知IP'}</strong>
                        <small class="text-muted d-block">${record.user_agent || '未知设备'}</small>
                    </div>
                    <small class="text-muted">${date}</small>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    $('#loginHistory').html(html);
}

// 显示提示信息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container').prepend(alertHtml);
    
    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
