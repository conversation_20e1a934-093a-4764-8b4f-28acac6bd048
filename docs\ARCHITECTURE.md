# BLK Web 项目架构设计

## 系统架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────────┤
│  Bootstrap 5 + Font Awesome + 现代化 CSS + 响应式设计              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────────┐    │
│  │ 图像生成     │ 图像编辑     │ 风格迁移     │ 提示词翻译        │    │
│  │ generate.html│ edit.html   │ style.html  │ translate.html  │    │
│  ├─────────────┼─────────────┼─────────────┼─────────────────┤    │
│  │ 旧照片修复   │ 图像对比     │ 图像画廊     │ 首页导航          │    │
│  │ restore.html │ compare.html│ gallery.html│ index.html      │    │
│  └─────────────┴─────────────┴─────────────┴─────────────────┘    │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    前端 JavaScript 层 (Client Layer)              │
├─────────────────────────────────────────────────────────────────┤
│  模块化设计 + ES6+ + 异步处理                                      │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    核心模块 (modules/)                       │ │
│  │  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │ │
│  │  │ apiClient.js│taskProcessor│fileUploader │ uiManager.js│  │ │
│  │  │ API通信     │ 任务处理     │ 文件上传     │ UI管理      │  │ │
│  │  └─────────────┴─────────────┴─────────────┴─────────────┘  │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    页面脚本 (pages/)                         │ │
│  │  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │ │
│  │  │generate.js  │ edit.js     │ style.js    │translate.js │  │ │
│  │  │restore.js   │ compare.js  │ gallery.js  │             │  │ │
│  │  └─────────────┴─────────────┴─────────────┴─────────────┘  │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼ HTTP/AJAX
┌─────────────────────────────────────────────────────────────────┐
│                      Flask Web 服务层 (Web Layer)                │
├─────────────────────────────────────────────────────────────────┤
│  Flask + Jinja2 + Werkzeug                                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                      路由层 (routes/)                        │ │
│  │  ┌─────────────┬─────────────┬─────────────────────────────┐ │ │
│  │  │image_routes │page_routes  │ translation_routes          │ │ │
│  │  │图像处理API   │页面渲染     │ 翻译服务API                  │ │ │
│  │  └─────────────┴─────────────┴─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      业务服务层 (Service Layer)                   │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    核心服务 (services/)                      │ │
│  │  ┌─────────────────────────┬─────────────────────────────┐  │ │
│  │  │    ImageService         │      TaskService            │  │ │
│  │  │  - 图像生成             │  - 任务管理                  │  │ │
│  │  │  - 图像编辑             │  - 状态跟踪                  │  │ │
│  │  │  - 风格迁移             │  - 并发处理                  │  │ │
│  │  │  - 旧照片修复           │  - 任务清理                  │  │ │
│  │  └─────────────────────────┴─────────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                   翻译服务 (Translation/)                    │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │              UnifiedTranslator                          │ │ │
│  │  │                 统一翻译接口                             │ │ │
│  │  │  ┌─────────────────────┬─────────────────────────────┐  │ │ │
│  │  │  │  DeepseekService    │     OllamaService           │  │ │ │
│  │  │  │  - API 翻译         │  - 本地模型翻译              │  │ │ │
│  │  │  │  - 提示词润色       │  - 自定义模板                │  │ │ │
│  │  │  │  - 健康检测         │  - no_think 模式             │  │ │ │
│  │  │  └─────────────────────┴─────────────────────────────┘  │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      工具层 (Utility Layer)                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                     utils/ 模块                             │ │
│  │  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │ │
│  │  │common_utils │ helpers.py  │image_preproc│validators.py│  │ │
│  │  │通用工具     │ 辅助函数     │ 图像预处理   │ 数据验证     │  │ │
│  │  └─────────────┴─────────────┴─────────────┴─────────────┘  │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    外部服务层 (External Services)                 │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────────┐   │
│  │ DeepSeek    │ Ollama      │ BFL API     │ 文件系统         │   │
│  │ API         │ 本地服务     │ 图像生成     │ uploads/outputs │   │
│  │ 翻译润色     │ AI模型      │ 编辑修复     │ 静态资源         │   │
│  └─────────────┴─────────────┴─────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 数据流架构

### 1. 图像生成流程
```
用户输入 → 前端验证 → API请求 → 任务创建 → BFL服务 → 结果存储 → 状态更新 → 前端展示
    │                                                                    ▲
    └─── 中文提示词 → 翻译服务 → 英文提示词 ──────────────────────────────────┘
```

### 2. 翻译服务流程
```
中文文本 → 前端提交 → 翻译路由 → UnifiedTranslator → 服务选择 → 结果返回
                                      │
                                      ├─ DeepSeek API
                                      └─ Ollama 本地服务
```

### 3. 任务管理流程
```
任务创建 → TaskService → 状态管理 → 并发处理 → 结果存储 → 状态查询
    │                                                        ▲
    └─── 任务ID ──────────────────────────────────────────────┘
```

## 核心设计模式

### 1. 服务层模式 (Service Layer Pattern)
- **ImageService**: 封装图像处理业务逻辑
- **TaskService**: 管理异步任务生命周期
- **翻译服务**: 统一翻译接口，支持多引擎

### 2. 策略模式 (Strategy Pattern)
- **UnifiedTranslator**: 根据配置选择翻译引擎
- **模型选择**: 支持不同AI模型的动态切换

### 3. 观察者模式 (Observer Pattern)
- **任务状态监控**: 前端轮询任务状态变化
- **UI状态管理**: 响应式界面更新

### 4. 工厂模式 (Factory Pattern)
- **服务实例化**: 根据配置创建服务实例
- **任务创建**: 统一任务对象创建接口

## 安全架构

### 1. 输入验证
```
前端验证 → 后端验证 → 数据清理 → 业务处理
    │           │          │
    │           │          └─ SQL注入防护
    │           └─ XSS防护
    └─ 文件类型检查
```

### 2. 文件安全
- 文件类型白名单
- 文件大小限制
- 安全文件名生成
- 上传目录隔离

### 3. API安全
- 请求频率限制
- 参数验证
- 错误信息脱敏
- 日志记录

## 性能优化架构

### 1. 前端优化
- **模块化加载**: 按需加载JavaScript模块
- **图片懒加载**: 画廊页面图片延迟加载
- **缓存策略**: 静态资源浏览器缓存
- **压缩优化**: CSS/JS文件压缩

### 2. 后端优化
- **异步处理**: 长时间任务异步执行
- **连接池**: 数据库连接复用
- **缓存机制**: 翻译结果缓存
- **资源清理**: 定期清理临时文件

### 3. 系统优化
- **负载均衡**: 支持多实例部署
- **监控告警**: 系统状态监控
- **日志管理**: 结构化日志记录

## 扩展性设计

### 1. 水平扩展
- 无状态服务设计
- 外部存储支持
- 负载均衡就绪
- 微服务架构兼容

### 2. 功能扩展
- 插件化翻译引擎
- 可配置AI模型
- 模块化功能组件
- 标准化API接口

### 3. 技术栈扩展
- 数据库抽象层
- 消息队列支持
- 容器化部署
- 云服务集成

## 监控与运维

### 1. 应用监控
- 任务执行状态
- API响应时间
- 错误率统计
- 资源使用情况

### 2. 业务监控
- 用户行为分析
- 功能使用统计
- 性能瓶颈识别
- 用户体验指标

### 3. 运维支持
- 健康检查接口
- 配置热更新
- 优雅停机
- 故障恢复机制

## 技术债务管理

### 1. 代码质量
- 代码规范检查
- 单元测试覆盖
- 代码审查流程
- 重构计划

### 2. 依赖管理
- 依赖版本控制
- 安全漏洞扫描
- 许可证合规
- 更新策略

### 3. 文档维护
- API文档同步
- 架构文档更新
- 部署文档完善
- 故障排除指南

---

**架构设计原则**:
1. **单一职责**: 每个模块专注单一功能
2. **开闭原则**: 对扩展开放，对修改封闭
3. **依赖倒置**: 依赖抽象而非具体实现
4. **接口隔离**: 细粒度接口设计
5. **最小知识**: 模块间最小化依赖

**设计目标**:
- 高可用性 (99.9%+)
- 高性能 (响应时间 < 2s)
- 高扩展性 (支持水平扩展)
- 高安全性 (多层防护)
- 易维护性 (模块化设计)