document.addEventListener('DOMContentLoaded', () => {

    const restoreForm = document.getElementById('restoreForm');
    if (!restoreForm) return;

    // 1. 初始化文件上传器
    const uploader = new FileUploader('#uploadArea', 'image');

    // 2. 处理选项卡点击
    const optionCards = document.querySelectorAll('.option-card');
    const selectedModeInput = document.getElementById('selectedMode');

    optionCards.forEach(card => {
        card.addEventListener('click', () => {
            optionCards.forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
            selectedModeInput.value = card.dataset.mode;
        });
    });

    // 3. 绑定表单提交事件
    restoreForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const file = uploader.getFile();
        if (!file) {
            alert('请先上传一张需要修复的图片！');
            return;
        }

        if (!selectedModeInput.value) {
            alert('请选择一种修复模式！');
            return;
        }

        // UI: 显示加载状态
        uiManager.showLoadingState(restoreForm, '#statusArea');

        // 准备表单数据
        const formData = new FormData();
        formData.append('mode', selectedModeInput.value);
        uploader.appendToFormData(formData);
        
        try {
            // API: 提交任务
            const taskUrl = await apiClient.submitTask('/api/restore', formData);

            // API: 轮询结果
            const result = await apiClient.pollTaskStatus(taskUrl);
            
            // UI: 显示最终结果
            uiManager.displayResult('#resultArea', result);

        } catch (error) {
            // UI: 显示错误
            uiManager.showError('#statusArea', error.message);
        } finally {
            // UI: 隐藏加载状态
            uiManager.hideLoadingState(restoreForm);
        }
    });
}); 