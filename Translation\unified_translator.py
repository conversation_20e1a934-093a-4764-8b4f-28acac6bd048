"""
统一翻译/润色服务入口
=====================

【功能说明】
- 支持 DeepSeek 与 Ollama 两种后端，参数完全统一。
- 可通过参数设定优先服务（'deepseek' 或 'ollama'），主服务不可用时自动切换到备选。
- 提供统一的 translate(text) 和 polish(text) 方法，输入输出格式与 deepseek/ollama 完全一致。
- 可直接运行（main演示）或 import 使用。

【输入需求】
- 初始化时需指定各服务参数（详见下方示例），以及优先服务（primary='deepseek' 或 'ollama'）。
- translate(text: str): 输入需翻译的原始文本（中文）。
- polish(text: str): 输入需润色的原始文本（中英文均可）。

【输出格式】
- 返回结构化字典：
  {
    'success': True/False,
    'original': '原始文本',
    'translated': '翻译结果',   # translate
    'polished': '润色结果',     # polish
    'error': '',
    'service': 'deepseek'/'ollama',
    'model': 'xxx',
    'timestamp': ...
  }

【调用方式示例】
from unified_translator import UnifiedTranslator

translator = UnifiedTranslator(
    primary='deepseek',
    deepseek_kwargs={
        'api_key': 'your_deepseek_api_key',
        'model': 'deepseek-chat',
    },
    ollama_kwargs={
        'base_url': 'http://localhost:11434',
        'model': 'gemma3',
    }
)

result = translator.translate('唯美少女，油画风，金色长发，阳光，微笑')
print(result)

result = translator.polish('A beautiful girl, oil painting style, golden long hair, sunshine, smile')
print(result)
"""

from .ollama_service import OllamaService
from .deepseek_service import DeepseekService

class UnifiedTranslator:
    def __init__(self, primary='deepseek', deepseek_kwargs=None, ollama_kwargs=None):
        """
        primary: 'deepseek' 或 'ollama'，优先使用的服务
        deepseek_kwargs: dict，传递给DeepseekService的参数
        ollama_kwargs: dict，传递给OllamaService的参数
        """
        self.primary = primary.lower()
        self.deepseek = DeepseekService(**(deepseek_kwargs or {}))
        self.ollama = OllamaService(**(ollama_kwargs or {}))
        self.services = {
            'deepseek': self.deepseek,
            'ollama': self.ollama
        }
        self.backup = 'ollama' if self.primary == 'deepseek' else 'deepseek'

    def _get_available_service(self):
        # 优先主服务，不可用则切备选
        if self.services[self.primary].check_service_status():
            return self.services[self.primary]
        elif self.services[self.backup].check_service_status():
            return self.services[self.backup]
        else:
            return None

    def translate(self, text: str):
        """统一翻译接口，自动主备切换"""
        service = self._get_available_service()
        if not service:
            return {
                'success': False,
                'original': text,
                'translated': '',
                'error': '所有服务均不可用',
                'service': None,
                'model': None,
                'timestamp': None
            }
        result = service.translate(text)
        result['service'] = getattr(service, 'service_type', None)
        result['model'] = getattr(service, 'model', None)
        return result

    def polish(self, text: str):
        """统一润色接口，自动主备切换"""
        service = self._get_available_service()
        if not service:
            return {
                'success': False,
                'original': text,
                'polished': '',
                'error': '所有服务均不可用',
                'service': None,
                'model': None,
                'timestamp': None
            }
        result = service.polish(text)
        result['service'] = getattr(service, 'service_type', None)
        result['model'] = getattr(service, 'model', None)
        return result

if __name__ == '__main__':
    # 示例：优先用DeepSeek，备选Ollama
    translator = UnifiedTranslator(
        primary='deepseek',
        deepseek_kwargs={
            'api_key': 'your_deepseek_api_key',
            'model': 'deepseek-chat',
        },
        ollama_kwargs={
            'base_url': 'http://localhost:11434',
            'model': 'gemma3',
        }
    )
    print('服务信息：')
    print('DeepSeek:', translator.deepseek.get_service_info())
    print('Ollama:', translator.ollama.get_service_info())

    print('\n翻译演示：')
    result = translator.translate('唯美少女，油画风，金色长发，阳光，微笑')
    print(result)

    print('\n润色演示：')
    result = translator.polish('A beautiful girl, oil painting style, golden long hair, sunshine, smile')
    print(result) 