import TaskProcessor from '../modules/taskProcessor.js';
import apiClient from '../modules/apiClient.js';
import uiManager from '../modules/uiManager.js';

document.addEventListener('DOMContentLoaded', () => {
    // 1. 获取关键DOM元素
    const generateForm = document.getElementById('generateForm');
    const smartGenerateBtn = document.getElementById('smartGenerateBtn');
    const polishBtn = document.getElementById('polishBtn');
    const promptEl = document.getElementById('prompt');
    const modelSelect = document.getElementById('model');
    const aspectRatioSelect = document.getElementById('aspect_ratio');
    const customSizeArea = document.getElementById('customSizeArea');
    const statusArea = document.getElementById('statusArea');
    const timelineArea = document.getElementById('timelineArea');
    
    // 2. 初始化任务处理器 - 生成页面没有文件上传
    const taskProcessor = new TaskProcessor({
        form: generateForm,
        submitBtnSelector: '#smartGenerateBtn',  // 修复：使用正确的选择器
        statusSelector: '#statusArea',
        resultSelector: '#statusArea'  // 生成页面结果也显示在状态区域
    });

    // 3. 绑定事件
    // 润色按钮
    if (polishBtn) {
        polishBtn.addEventListener('click', handlePolish);
    }

    // 模型选择变化
    if (modelSelect) {
        modelSelect.addEventListener('change', handleModelChange);
        // 初始化
        handleModelChange();
    }

    // 宽高比选择变化
    if (aspectRatioSelect) {
        aspectRatioSelect.addEventListener('change', handleAspectRatioChange);
    }

    // 风格按钮
    const styleButtons = document.querySelectorAll('.style-btn');
    styleButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            // 切换激活状态
            styleButtons.forEach(otherBtn => otherBtn.classList.remove('active'));
            btn.classList.add('active');
            
            // 添加视觉反馈
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 150);
        });
    });

    // 4. 事件处理函数
    async function handlePolish() {
        const text = promptEl.value.trim();
        if (!text) {
            alert('请先输入提示词');
            return;
        }

        // 检查用户积分（润色功能需要1积分）
        const creditsCheck = await checkUserCreditsForAction(1, '提示词润色');
        if (!creditsCheck.success) {
            return; // 积分检查失败，已显示相应提示
        }

        uiManager.showLoadingState(polishBtn, '润色中...');
        addTimeline('开始润色提示词...');

        try {
            const polishedText = await apiClient.polishPrompt(text);
            promptEl.value = polishedText;
            addTimeline('✅ 提示词润色完成');
            
            // 显示处理状态
            const processStatusArea = document.getElementById('processStatusArea');
            const processStatusText = document.getElementById('processStatusText');
            if (processStatusArea && processStatusText) {
                processStatusText.textContent = '提示词润色完成';
                processStatusArea.style.display = 'block';
                setTimeout(() => {
                    processStatusArea.style.display = 'none';
                }, 3000);
            }
        } catch (error) {
            addTimeline('❌ 润色失败: ' + error.message);
            console.error('润色失败:', error);
        } finally {
            uiManager.hideLoadingState(polishBtn);
        }
    }

    function handleModelChange() {
        const selectedModel = modelSelect.value;
        const isKontext = selectedModel.includes('kontext');
        
        // 控制自定义尺寸的显示
        const kontextWarning = document.getElementById('kontextModelWarning');
        if (kontextWarning) {
            kontextWarning.style.display = isKontext && aspectRatioSelect.value === 'custom' ? 'block' : 'none';
        }
        
        // 控制高级参数的显示
        const rawOption = document.getElementById('rawOption');
        if (rawOption) {
            rawOption.style.display = selectedModel.includes('ultra') ? 'block' : 'none';
        }
    }

    function handleAspectRatioChange() {
        const isCustom = aspectRatioSelect.value === 'custom';
        const isKontext = modelSelect.value.includes('kontext');
        
        if (customSizeArea) {
            customSizeArea.style.display = isCustom ? 'block' : 'none';
        }
        
        // 显示Kontext模型警告
        const kontextWarning = document.getElementById('kontextModelWarning');
        if (kontextWarning) {
            kontextWarning.style.display = isKontext && isCustom ? 'block' : 'none';
        }
    }

    function addTimeline(message) {
        if (timelineArea) {
            const time = new Date().toLocaleTimeString();
            const timelineItem = document.createElement('div');
            timelineItem.className = 'timeline-item';
            timelineItem.innerHTML = `<span class="text-muted">${time}</span> ${message}`;
            timelineArea.appendChild(timelineItem);
            timelineArea.scrollTop = timelineArea.scrollHeight;
        }
    }

    // 5. 全局函数 - 供HTML onclick使用
    window.useExample = function(element) {
        const text = element.textContent;
        const promptText = text.split(':')[1].trim();
        promptEl.value = promptText;
        
        // 添加视觉反馈
        element.style.backgroundColor = '#fff3cd';
        setTimeout(() => {
            element.style.backgroundColor = '';
        }, 500);
    };

    window.resetForm = function() {
        taskProcessor.reset();
        if (timelineArea) {
            timelineArea.innerHTML = '';
        }
        
        // 重置风格按钮
        styleButtons.forEach(btn => btn.classList.remove('active'));
        
        // 重置状态显示
        const processStatusArea = document.getElementById('processStatusArea');
        if (processStatusArea) {
            processStatusArea.style.display = 'none';
        }
    };

    /**
     * 检查用户积分是否足够执行特定操作
     */
    async function checkUserCreditsForAction(creditsNeeded, actionName) {
        try {
            // 使用新的认证系统 - 基于session cookie，无需手动发送token
            // 检查用户积分
            const response = await fetch('/api/user/credits/check-limits', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include', // 包含session cookie
                body: JSON.stringify({
                    credits_needed: creditsNeeded
                })
            });

            const result = await response.json();

            if (!response.ok) {
                if (response.status === 401) {
                    showCreditError('请先登录', '您需要登录后才能使用此功能');
                } else {
                    const result = await response.json();
                    showCreditError('检查积分失败', result.message || '网络错误');
                }
                return { success: false };
            }

            if (!result.success) {
                if (result.limit_type === 'credits') {
                    showInsufficientCreditsDialog(result.available_credits, result.needed_credits, actionName);
                } else if (result.limit_type === 'daily') {
                    showDailyLimitDialog(result.daily_limit, result.daily_used);
                } else {
                    showCreditError('积分检查失败', result.message);
                }
                return { success: false };
            }

            return { success: true };

        } catch (error) {
            console.error('积分检查失败:', error);
            showCreditError('积分检查失败', '网络连接错误，请稍后重试');
            return { success: false };
        }
    }

    /**
     * 显示积分不足对话框
     */
    function showInsufficientCreditsDialog(availableCredits, neededCredits, actionName) {
        const shortfall = neededCredits - availableCredits;
        let message;

        if (availableCredits <= 0) {
            message = `您当前的积分为 ${availableCredits}，无法执行"${actionName}"操作。请前往积分管理页面充值。`;
        } else {
            message = `"${actionName}"需要 ${neededCredits} 积分，您当前有 ${availableCredits} 积分，还需要 ${shortfall} 积分。`;
        }

        showCreditDialog('积分不足', message, '前往充值', '/user/credits');
    }

    /**
     * 显示每日限制对话框
     */
    function showDailyLimitDialog(dailyLimit, dailyUsed) {
        showCreditDialog(
            '每日使用次数已达上限',
            `您今日已使用 ${dailyUsed}/${dailyLimit} 次，已达到每日限制。请明天再试或升级账户。`,
            '了解升级',
            '/user/profile'
        );
    }

    /**
     * 显示积分错误
     */
    function showCreditError(title, message) {
        addTimeline(`❌ ${title}: ${message}`);
    }

    /**
     * 显示积分对话框
     */
    function showCreditDialog(title, message, buttonText, redirectUrl) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="creditModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-body text-center">
                            <h5 class="mb-3">${title}</h5>
                            <p class="mb-4">${message}</p>
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-primary" onclick="window.location.href='${redirectUrl}'">${buttonText}</button>
                                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('creditModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('creditModal'));
        modal.show();

        // 模态框关闭后移除DOM
        document.getElementById('creditModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 6. 初始化
    addTimeline('页面加载完成，等待用户输入...');
});