# BFL AI 图像生成器项目结构

## 核心目录
1. `backend/` - Flask Web应用后端
   - `routes/` - API路由处理
   - `services/` - 业务逻辑服务
   - `config/` - 配置文件
   - `templates/` - 前端模板
   - `static/` - 静态资源
   - `utils/` - 后端工具函数

2. `BFL/` - 图像生成核心代码
   - 负责与BFL API交互
   - 处理图像生成请求
   - 管理API调用

3. `Translation/` - 翻译服务模块
   - `translation_service_manager.py` - 翻译服务管理器
   - `deepseek_service.py` - DeepSeek翻译服务
   - `ollama_service.py` - Ollama本地翻译服务

4. `docs/` - 项目文档
   - `AUTO_TRANSLATION_README.md` - 翻译功能说明
   - `BFL_README.md` - BFL功能说明
   - `WEB_README.md` - Web应用说明

5. `utils/` - 全局工具函数
   - 通用工具和辅助函数

6. `tests/` - 测试代码
   - 单元测试
   - 集成测试

## 数据目录
- `outputs/` - 生成的图像输出目录
- `uploads/` - 用户上传文件目录

## 入口文件
- `run_web.py` - Web应用启动脚本
- `test_api.py` - API测试脚本