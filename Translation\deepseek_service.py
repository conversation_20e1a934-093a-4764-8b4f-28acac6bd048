import requests
from openai import OpenAI
from typing import List, Dict, Any
import time
import logging

logger = logging.getLogger(__name__)

class DeepseekService:
    """Deepseek翻译与润色服务（openai SDK实现，风格对齐OllamaService）"""
    def __init__(self, api_key: str, model: str = "deepseek-chat", base_url: str = "https://api.deepseek.com"):
        self.api_key = api_key
        self.model = model
        self.base_url = base_url
        self.service_type = 'deepseek'
        self.client = self._create_client()
        # 初始化时检查服务可用性
        if not self.check_service_status():
            logger.warning(f"⚠️ Deepseek服务不可用: {base_url}")
        else:
            logger.info(f"✅ Deepseek服务可用: {base_url}")

    def check_service_status(self) -> bool:
        """用GET请求检测网络联通性，不消耗API额度，只要有响应就判定为联通"""
        try:
            resp = requests.get(self.base_url, timeout=5)
            # 只要能收到响应（无论200/404/401等）就认为联通
            return True
        except requests.RequestException as e:
            logger.error(f"联通性检测异常: {e}")
            return False

    def check_api_status(self) -> bool:
        """（可选）用API chat/completions检测API健康，消耗额度"""
        try:
            messages = self._build_messages("You are a helpful assistant", "ping")
            resp = self._chat_completion(messages)
            _ = self._parse_response(resp)
            return True
        except Exception as e:
            logger.error(f"API健康检测失败: {e}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        info = {
            'type': self.service_type,
            'url': self.base_url,
            'model': self.model,
            'status': 'unavailable',
            'message': ''
        }
        try:
            if self.check_service_status():
                info['status'] = 'available'
                info['message'] = '服务正常'
            else:
                info['message'] = '服务不可用'
        except Exception as e:
            info['message'] = str(e)
        return info

    def translate(self, chinese_text: str) -> Dict[str, Any]:
        """将中文文本翻译为英文，返回结构化字典"""
        result = {
            'success': False,
            'original': chinese_text,
            'translated': '',
            'error': '',
            'service': self.service_type,
            'model': self.model,
            'timestamp': time.time()
        }
        if not chinese_text or not chinese_text.strip():
            result['error'] = '输入文本不能为空'
            return result
        try:
            prompt = self._get_translation_prompt(chinese_text.strip())
            messages = self._build_messages("", prompt)
            resp = self._chat_completion(messages)
            translated = self._parse_response(resp)
            if translated:
                result['success'] = True
                result['translated'] = translated.strip()
            else:
                result['error'] = '翻译结果为空'
        except Exception as e:
            logger.error(f"翻译失败: {e}")
            result['error'] = str(e)
        return result

    def polish(self, original_text: str) -> Dict[str, Any]:
        """润色文本，返回结构化字典"""
        result = {
            'success': False,
            'original': original_text,
            'polished': '',
            'error': '',
            'service': self.service_type,
            'model': self.model,
            'timestamp': time.time()
        }
        if not original_text or not original_text.strip():
            result['error'] = '输入文本不能为空'
            return result
        try:
            prompt = self._get_polish_prompt(original_text.strip())
            messages = self._build_messages("", prompt)
            resp = self._chat_completion(messages)
            polished = self._parse_response(resp)
            if polished:
                result['success'] = True
                result['polished'] = polished.strip()
            else:
                result['error'] = '润色结果为空'
        except Exception as e:
            logger.error(f"润色失败: {e}")
            result['error'] = str(e)
        return result

    # ================== 私有方法 ==================
    def _create_client(self) -> OpenAI:
        return OpenAI(api_key=self.api_key, base_url=self.base_url)

    def _build_messages(self, system_prompt: str, user_prompt: str) -> List[Dict[str, str]]:
        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

    def _chat_completion(self, messages: List[Dict[str, str]]) -> Any:
        return self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            stream=False
        )

    def _parse_response(self, response) -> str:
        return response.choices[0].message.content

    def _get_translation_prompt(self, chinese_prompt: str) -> str:
        return f"""你是一个专业的AI图像生成提示词翻译专家。请将以下中文提示词翻译成英文，要求：\n\n1. 保持原意准确，适合AI图像生成\n2. 使用专业的艺术和摄影术语\n3. 保持描述的生动性和具体性\n4. 如果有艺术风格、技法等专业词汇，请使用准确的英文对应词\n5. 只返回翻译结果，不要添加其他解释\n\n中文提示词：{chinese_prompt}\n\n英文翻译："""

    def _get_polish_prompt(self, original_text: str) -> str:
        return f"""你是一名精通人工智能绘图的数字艺术家。现在有如下的文字描述，请根据你的理解在原语义的基础上补充更多细节，以便能方便人工智能更好的理解并绘制出优质的图像画面。\n\n要求：\n1. 保持原始语义和主题不变\n2. 添加更多视觉细节描述（光线、色彩、构图、材质等）\n3. 补充艺术风格和技法描述\n4. 增强画面的层次感和氛围感\n5. 使用专业的艺术和摄影术语\n6. 只返回润色后的结果，不要添加其他解释\n\n原始描述：{original_text}\n\n润色后的描述："""

# ================== 主函数 ==================
def main():
    api_key = "sk-4c12435735b543f79abfbf29a3641a98"
    service = DeepseekService(api_key)

    # 检查服务状态
    status = service.check_service_status()
    print("服务状态：", "可用" if status else "不可用")

    # 获取服务信息
    info = service.get_service_info()
    print("服务信息：", info)

    # 翻译功能示例
    # chinese_prompt = "唯美少女，油画风，金色长发，阳光，微笑"
    # translation = service.translate(chinese_prompt)
    # print("翻译结构化结果：", translation)

    # # 润色功能示例
    # original_text = "唯美少女，油画风，金色长发，阳光，微笑"
    # polished = service.polish(original_text)
    # print("润色结构化结果：", polished)

if __name__ == "__main__":
    main()
