@echo off
echo 🚀 设置 BFL Web 应用环境（Windows）
echo.

echo 📦 安装 Python 依赖...
python -m pip install --upgrade pip
pip install flask requests pillow

echo ✅ 配置验证...
python -c "from config.translation_config import translation_config; translation_config.print_config()"

echo 📁 创建必要目录...
if not exist uploads mkdir uploads
if not exist outputs mkdir outputs

echo 🎉 设置完成！
echo.
echo 💡 使用说明:
echo    python app.py  # 启动 Web 应用
echo.
pause 