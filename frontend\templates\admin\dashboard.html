{% extends "base.html" %}

{% block title %}管理员仪表板 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-tachometer-alt"></i> 管理员仪表板</h2>
                <div class="btn-group">
                    <a href="{{ url_for('pages.admin_users_page') }}" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> 用户管理
                    </a>
                    <a href="{{ url_for('pages.admin_settings_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog"></i> 系统设置
                    </a>
                    <a href="{{ url_for('pages.admin_statistics_page') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> 数据统计
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalUsers">-</h4>
                            <p class="mb-0">总用户数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block">
                        活跃用户: <span id="activeUsers">-</span> | 
                        今日新增: <span id="newUsersToday">-</span>
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalTasks">-</h4>
                            <p class="mb-0">总任务数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-2x"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block">
                        已完成: <span id="completedTasks">-</span> | 
                        今日任务: <span id="tasksToday">-</span>
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalCreditsIssued">-</h4>
                            <p class="mb-0">已发放积分</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-coins fa-2x"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block">
                        已使用: <span id="totalCreditsUsed">-</span> | 
                        使用率: <span id="creditsUsageRate">-</span>%
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="systemStatus">正常</h4>
                            <p class="mb-0">系统状态</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-server fa-2x"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block">
                        运行时间: <span id="uptime">-</span>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表和表格 -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> 用户增长趋势</h5>
                </div>
                <div class="card-body">
                    <canvas id="userGrowthChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 最近活动</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <div id="recentActivities">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> 快速操作</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-primary w-100" onclick="showAddUserModal()">
                                <i class="fas fa-user-plus"></i><br>
                                添加用户
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-success w-100" onclick="showBulkCreditModal()">
                                <i class="fas fa-coins"></i><br>
                                批量发放积分
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-warning w-100" onclick="showSystemMaintenanceModal()">
                                <i class="fas fa-tools"></i><br>
                                系统维护
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-info w-100" onclick="exportData()">
                                <i class="fas fa-download"></i><br>
                                导出数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-control" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-control" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">用户类型</label>
                        <select class="form-select" name="user_type">
                            <option value="free">免费用户</option>
                            <option value="premium">付费用户</option>
                            <option value="pro">Pro用户</option>
                            <option value="enterprise">企业用户</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">初始积分</label>
                        <input type="number" class="form-control" name="total_credits" value="10">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addUser()">添加用户</button>
            </div>
        </div>
    </div>
</div>

<style>
.activity-item {
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
}

.activity-icon {
    width: 24px;
    text-align: center;
}

.activity-description {
    font-size: 14px;
    line-height: 1.4;
}

.activity-status .badge {
    font-size: 11px;
}

#recentActivities {
    max-height: 400px;
    overflow-y: auto;
}

#recentActivities::-webkit-scrollbar {
    width: 6px;
}

#recentActivities::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#recentActivities::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#recentActivities::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // 检查是否是登录成功后的跳转
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('login_success') === '1') {
        // 显示登录成功消息
        showSuccess('欢迎回来，管理员！');

        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // 检查管理员权限
    checkAdminAuth();

    // 加载仪表板数据
    loadDashboardStats();

    // 初始化图表
    initCharts();

    // 每30秒刷新一次数据
    setInterval(loadDashboardStats, 30000);
});

function checkAdminAuth() {
    if (!window.simpleAuth) {
        showError('认证系统未初始化');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
        return;
    }

    // 直接检查当前认证状态
    const authState = window.simpleAuth.getState();
    
    if (!authState.isLoggedIn) {
        showError('请先登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
        return;
    }

    // 检查管理员权限
    if (!authState.user || authState.user.user_type !== 'enterprise') {
        showError('需要管理员权限访问此页面');
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
        return;
    }

    // 监听认证状态变化
    window.addEventListener('authLogout', function(event) {
        showError('登录已过期，请重新登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
    });

    window.addEventListener('authError', function(event) {
        showError('认证失败，请重新登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
    });
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;
    
    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function loadDashboardStats() {
    if (!window.simpleAuth) {
        showError('认证系统未初始化');
        return;
    }

    window.simpleAuth.request('/api/admin/dashboard', {
        method: 'GET'
    })
    .then(response => response.json())
    .then(data => {
        console.log('统计数据响应:', data);
        if (data.success) {
            updateStatsDisplay(data.data);
        } else {
            console.error('统计数据API返回错误:', data.message);
            showError('加载统计数据失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('加载统计数据失败:', error);
        showError('无法连接到服务器，请检查网络连接');
    });
}

function updateStatsDisplay(data) {
    console.log('更新统计显示:', data);

    // 用户统计
    $('#totalUsers').text(data.user_stats.total_users || 0);
    $('#activeUsers').text(data.user_stats.active_users || 0);
    $('#newUsersToday').text(data.user_stats.verified_users || 0); // 暂时用验证用户数代替今日新增

    // 任务统计
    $('#totalTasks').text(data.task_stats.total_tasks || 0);
    $('#completedTasks').text(data.task_stats.completed_tasks || 0);
    $('#tasksToday').text(data.task_stats.recent_tasks || 0);

    // 积分统计
    if (data.system_stats) {
        $('#totalCreditsIssued').text(data.system_stats.total_credits_issued || 0);
        $('#totalCreditsUsed').text(data.system_stats.total_credits_used || 0);
        const usageRate = data.system_stats.total_credits_issued > 0 ?
            ((data.system_stats.total_credits_used / data.system_stats.total_credits_issued) * 100).toFixed(1) : 0;
        $('#creditsUsageRate').text(usageRate + '%');
    }

    // 更新最近活动
    updateRecentActivities(data.recent_activities || []);

    console.log('统计数据更新完成');
}

function updateRecentActivities(activities) {
    const container = $('#recentActivities');

    if (!activities || activities.length === 0) {
        container.html('<div class="text-center text-muted"><i class="fas fa-info-circle"></i> 暂无活动记录</div>');
        return;
    }

    let html = '';
    activities.forEach(activity => {
        const timeAgo = formatTimeAgo(activity.time);
        const statusClass = getStatusClass(activity.status);
        const iconClass = getActivityIcon(activity.type);

        html += `
            <div class="activity-item mb-3 p-2 border-bottom">
                <div class="d-flex align-items-start">
                    <div class="activity-icon me-2">
                        <i class="${iconClass} ${statusClass}"></i>
                    </div>
                    <div class="activity-content flex-grow-1">
                        <div class="activity-description">${activity.description}</div>
                        <small class="text-muted">${timeAgo}</small>
                    </div>
                    <div class="activity-status">
                        <span class="badge ${statusClass}">${getStatusText(activity.status)}</span>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

function getActivityIcon(type) {
    switch(type) {
        case 'task': return 'fas fa-tasks';
        case 'credit': return 'fas fa-coins';
        case 'user': return 'fas fa-user';
        default: return 'fas fa-info-circle';
    }
}

function getStatusClass(status) {
    switch(status) {
        case 'completed': return 'text-success';
        case 'failed': return 'text-danger';
        case 'pending': return 'text-warning';
        case 'processing': return 'text-info';
        default: return 'text-secondary';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'completed': return '完成';
        case 'failed': return '失败';
        case 'pending': return '等待';
        case 'processing': return '处理中';
        default: return status;
    }
}

function formatTimeAgo(timeString) {
    if (!timeString) return '未知时间';

    const now = new Date();
    const time = new Date(timeString);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return time.toLocaleDateString();
}

function initCharts() {
    // 用户增长趋势图
    const ctx = document.getElementById('userGrowthChart').getContext('2d');
    window.userGrowthChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '用户数量',
                data: [],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 3000);
}

function showAddUserModal() {
    $('#addUserModal').modal('show');
}

function addUser() {
    // TODO: 实现添加用户功能
    alert('添加用户功能待实现');
}

function showBulkCreditModal() {
    // TODO: 实现批量发放积分功能
    alert('批量发放积分功能待实现');
}

function showSystemMaintenanceModal() {
    // TODO: 实现系统维护功能
    alert('系统维护功能待实现');
}

function exportData() {
    // TODO: 实现数据导出功能
    alert('数据导出功能待实现');
}
</script>
{% endblock %}
