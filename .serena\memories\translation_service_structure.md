# 翻译服务架构

## 核心组件

1. **翻译服务管理器** (`Translation/translation_service_manager.py`)
   - 管理多个翻译服务提供者
   - 提供自动故障转移
   - 支持服务状态监控
   - 实现负载均衡

2. **翻译服务实现**
   - DeepSeek服务 (`deepseek_service.py`)
     - 使用DeepSeek API进行云端翻译
     - 支持高质量翻译和文本润色
   - Ollama服务 (`ollama_service.py`)
     - 本地部署的翻译服务
     - 使用Qwen模型
     - 支持离线翻译

3. **配置管理** (`backend/config/translation_config.py`)
   - 集中管理服务配置
   - 支持环境变量覆盖
   - 提供默认配置

4. **API路由** (`backend/routes/translation_routes.py`)
   - `/api/translate/text` - 文本翻译
   - `/api/translate/polish` - 文本润色
   - `/api/translate/batch` - 批量翻译
   - `/api/translate/status` - 服务状态
   - `/api/translate/switch` - 切换服务
   - `/api/translate/health` - 健康检查

## 特性
- 多服务提供者支持
- 自动故障转移
- 服务状态监控
- 批量处理能力
- 健康检查
- 可配置的服务优先级