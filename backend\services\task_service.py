import threading
from typing import Dict, Any, List
from datetime import datetime

from backend.utils.helpers import generate_unique_id, format_task_for_response, log_operation

class TaskService:
    """任务管理服务 - 负责任务的创建、状态跟踪和管理"""
    
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
    
    def create_task(self, task_type: str, **kwargs) -> str:
        """创建新任务"""
        task_id = generate_unique_id()
        
        with self._lock:
            self.tasks[task_id] = {
                'type': task_type,
                'status': 'queued',
                'message': '任务已排队',
                'created_at': datetime.now().isoformat(),
                **kwargs
            }
        
        log_operation(f"创建{task_type}任务", f"ID: {task_id[:8]}")
        return task_id
    
    def update_task_status(self, task_id: str, status: str, message: str = '', **kwargs):
        """更新任务状态"""
        if task_id not in self.tasks:
            # 记录警告，便于排查
            log_operation("警告：尝试更新不存在的任务", f"ID: {task_id}", success=False)
            return False
        
        with self._lock:
            self.tasks[task_id]['status'] = status
            self.tasks[task_id]['message'] = message
            
            if status == 'completed':
                self.tasks[task_id]['completed_at'] = datetime.now().isoformat()
            
            # 更新其他字段
            for key, value in kwargs.items():
                self.tasks[task_id][key] = value
        
        log_operation(f"更新任务状态", f"ID: {task_id[:8]}, 状态: {status}")
        return True
    
    def get_task(self, task_id: str) -> Dict[str, Any]:
        """获取单个任务信息"""
        if task_id not in self.tasks:
            return {}
        
        with self._lock:
            return format_task_for_response(task_id, self.tasks[task_id])
    
    def get_all_tasks(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取所有任务列表"""
        task_list = []
        
        with self._lock:
            for task_id, task_data in self.tasks.items():
                task_list.append(format_task_for_response(task_id, task_data))
        
        # 按创建时间排序（最新的在前）
        task_list.sort(key=lambda x: x['created_at'], reverse=True)
        
        if limit:
            task_list = task_list[:limit]
        
        return task_list
    
    def task_exists(self, task_id: str) -> bool:
        """检查任务是否存在"""
        return task_id in self.tasks
    
    def cleanup_old_tasks(self, max_tasks: int = 100):
        """清理旧任务，只保留最新的指定数量"""
        if len(self.tasks) <= max_tasks:
            return
        
        with self._lock:
            # 获取所有任务并按时间排序
            sorted_tasks = sorted(
                self.tasks.items(),
                key=lambda x: x[1]['created_at'],
                reverse=True
            )
            
            # 保留最新的任务
            tasks_to_keep = dict(sorted_tasks[:max_tasks])
            removed_count = len(self.tasks) - len(tasks_to_keep)
            
            self.tasks = tasks_to_keep
            
            if removed_count > 0:
                log_operation("清理旧任务", f"移除了 {removed_count} 个旧任务")

# 全局任务服务实例
task_service = TaskService() 