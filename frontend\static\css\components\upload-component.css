/* 现代化拖拽上传组件通用样式 */

.modern-upload-area {
    position: relative;
    border: 2px dashed #e2e8f0;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
    min-height: 200px;
}

.modern-upload-area:hover {
    background: rgba(var(--theme-color-rgb, 16, 185, 129), 0.02);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(var(--theme-color-rgb, 16, 185, 129), 0.1);
}

.modern-upload-area.drag-over {
    background: rgba(var(--theme-color-rgb, 16, 185, 129), 0.05) !important;
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    height: 100%;
    min-height: 200px;
    cursor: pointer;
}

.upload-icon {
    font-size: 3rem;
    color: rgb(var(--theme-color-rgb, 16, 185, 129));
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.modern-upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: rgb(var(--theme-color-rgb-dark, 5, 150, 105));
}

.upload-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.upload-subtitle {
    color: #6b7280;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.upload-size {
    color: #9ca3af;
    font-size: 0.8rem;
    margin-bottom: 1rem;
}

.upload-preview {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-preview img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    object-fit: contain;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.upload-preview:hover .preview-overlay {
    opacity: 1;
}

.preview-info {
    color: white;
}

.file-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    word-break: break-all;
}

.file-size {
    font-size: 0.8rem;
    opacity: 0.8;
    margin: 0;
}

.preview-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.preview-actions .btn {
    backdrop-filter: blur(10px);
    border-width: 1px;
}

/* 主题色彩变量适配 */
.theme-edit {
    --theme-color-rgb: 16, 185, 129;
    --theme-color-rgb-dark: 5, 150, 105;
}

.theme-style {
    --theme-color-rgb: 245, 158, 11;
    --theme-color-rgb-dark: 217, 119, 6;
}

/* 主题特定的悬停效果 */
.theme-edit .modern-upload-area:hover {
    border-color: #10b981;
}

.theme-edit .modern-upload-area.drag-over {
    border-color: #10b981 !important;
}

.theme-style .modern-upload-area:hover {
    border-color: #f59e0b;
}

.theme-style .modern-upload-area.drag-over {
    border-color: #f59e0b !important;
} 