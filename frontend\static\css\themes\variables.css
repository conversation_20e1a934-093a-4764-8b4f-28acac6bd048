/* 主题变量定义 */
:root {
    /* 默认主题变量 - 品牌蓝绿色 */
    --primary-gradient: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
    --primary-color: #0ea5e9;
    --primary-dark: #0284c7;
    --secondary-color: #06b6d4;
    --accent-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1e293b;
    --light-bg: #f1f5f9;
    --card-bg: rgba(213, 225, 238, 0.98);
    --shadow-primary: rgba(14, 165, 233, 0.25);
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --border-radius-sm: 8px;
}

/* 生成页面主题 - 蓝紫色创作 */
.theme-generate {
    --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --shadow-primary: rgba(99, 102, 241, 0.25);
    --light-bg: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 50%, #ede9fe 100%);
}

/* 编辑页面主题 - 绿色修改 */
.theme-edit {
    --primary-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --primary-color: #10b981;
    --primary-dark: #047857;
    --secondary-color: #059669;
    --shadow-primary: rgba(16, 185, 129, 0.25);
    --light-bg: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%);
}

/* 风格页面主题 - 橙色艺术 */
.theme-style {
    --primary-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --primary-color: #f59e0b;
    --primary-dark: #d97706;
    --secondary-color: #f59e0b;
    --shadow-primary: rgba(245, 158, 11, 0.25);
    --light-bg: linear-gradient(135deg, #fffbeb 0%, #fef3c7 50%, #fed7aa 100%);
}

/* 翻译页面主题 - 青色语言 */
.theme-translate {
    --primary-gradient: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --primary-color: #06b6d4;
    --primary-dark: #0891b2;
    --secondary-color: #0e7490;
    --shadow-primary: rgba(6, 182, 212, 0.25);
    --light-bg: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
}

/* 画廊页面主题 - 深色展示 */
.theme-gallery {
    --primary-gradient: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    --primary-color: #374151;
    --primary-dark: #1f2937;
    --secondary-color: #4b5563;
    --shadow-primary: rgba(55, 65, 81, 0.25);
    --light-bg: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 50%, #e5e7eb 100%);
}

/* 旧照片修复页面主题 - 复古棕色 */
.theme-restore {
    --primary-gradient: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    --primary-color: #8B4513;
    --primary-dark: #A0522D;
    --secondary-color: #D2691E;
    --shadow-primary: rgba(139, 69, 19, 0.25);
    --light-bg: linear-gradient(135deg, #FDF6E3 0%, #F4E4BC 50%, #E6D2A3 100%);
}

/* 生成页面主题 */
body.theme-generate {
    --header-bg: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
    --primary-bg: linear-gradient(135deg, #6366f1 0%, #7c3aed 100%);
    --success-bg: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* 生成页面主题卡片样式增强 */
.theme-generate .card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(99, 102, 241, 0.1);
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.12);
}

.theme-generate .card .card-header {
    background: var(--header-bg) !important;
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%) !important;
    color: white !important;
    border: none !important;
    transition: all 0.3s ease;
}

.theme-generate .card .card-header:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%) !important;
} 