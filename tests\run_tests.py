#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
统一运行项目中的所有测试
"""

import sys
import os
from typing import Dict, Any

# 添加父目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from tests.test_services import test_translation_services
    from tests.test_utils import test_common_utils
except ImportError as e:
    print(f"❌ 导入测试模块失败: {e}")
    sys.exit(1)


def run_all_tests() -> Dict[str, Any]:
    """
    运行所有测试套件
    
    Returns:
        完整的测试结果
    """
    print("🧪 BLK项目完整测试套件")
    print("=" * 80)
    
    results = {}
    
    # 运行工具函数测试
    print("\n1️⃣ 运行通用工具函数测试")
    print("-" * 40)
    try:
        utils_results = test_common_utils()
        results['utils'] = utils_results
        print(f"工具函数测试: {'✅ 通过' if utils_results['overall_success'] else '❌ 失败'}")
    except Exception as e:
        print(f"❌ 工具函数测试异常: {e}")
        results['utils'] = {'overall_success': False, 'error': str(e)}
    
    # 运行翻译服务测试
    print("\n\n2️⃣ 运行翻译服务测试")
    print("-" * 40)
    try:
        services_results = test_translation_services()
        results['services'] = services_results
        print(f"翻译服务测试: {'✅ 通过' if services_results['overall_success'] else '❌ 失败'}")
    except Exception as e:
        print(f"❌ 翻译服务测试异常: {e}")
        results['services'] = {'overall_success': False, 'error': str(e)}
    
    # 汇总所有结果
    overall_success = all(
        result.get('overall_success', False) 
        for result in results.values()
    )
    
    print("\n\n" + "=" * 80)
    print("🏁 完整测试套件结果")
    print("=" * 80)
    
    print("📊 各模块测试结果:")
    for module_name, result in results.items():
        status = "✅ 通过" if result.get('overall_success', False) else "❌ 失败"
        print(f"   {module_name}: {status}")
        
        if 'error' in result:
            print(f"     错误: {result['error']}")
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if overall_success else '❌ 部分失败'}")
    
    if overall_success:
        print("\n🎉 恭喜！所有测试都通过了！")
        print("💡 项目的核心功能运行正常，可以安全使用。")
    else:
        print("\n⚠️  部分测试失败，请检查相关功能。")
        print("💡 失败的测试可能需要修复或环境配置。")
    
    # 添加使用建议
    print("\n📚 测试使用建议:")
    print("1. 定期运行测试确保功能正常")
    print("2. 添加新功能时编写对应测试")
    print("3. 修改核心逻辑后运行相关测试")
    print("4. 部署前运行完整测试套件")
    
    return {
        'module_results': results,
        'overall_success': overall_success,
        'summary': {
            'total_modules': len(results),
            'passed_modules': sum(1 for r in results.values() if r.get('overall_success', False)),
            'failed_modules': sum(1 for r in results.values() if not r.get('overall_success', False))
        }
    }


def main():
    """主函数"""
    try:
        results = run_all_tests()
        
        # 根据测试结果设置退出码
        exit_code = 0 if results['overall_success'] else 1
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n\n❌ 测试运行器异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()