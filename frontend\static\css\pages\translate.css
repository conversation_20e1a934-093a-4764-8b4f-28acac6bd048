/* 翻译页面专用样式 */
body.theme-translate {
    /* This body background is already handled by the theme system in app.css */
    /* We keep the file for other specific styles */
}

/* 流程步骤卡片样式 */
.process-step {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.process-step:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.step-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px 16px 0 0;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    margin-right: 1rem;
}

.step-1 .step-number { background: linear-gradient(135deg, #06b6d4, #0891b2); }
.step-2 .step-number { background: linear-gradient(135deg, #10b981, #059669); }
.step-3 .step-number { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.step-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.step-content {
    padding: 2rem;
}

/* 文本框样式 */
.step-textarea {
    width: 100%;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.step-textarea:focus {
    outline: none;
    border-color: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
    background: white;
}

.step-textarea.polished {
    border-color: #10b981;
}

.step-textarea.translated {
    border-color: #8b5cf6;
}

/* 按钮样式 */
.step-btn {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none; /* Let background handle it */
    min-width: 120px;
}

.step-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-polish {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-polish:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.btn-translate {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
}

.btn-translate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.3);
}

.btn-generate {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

/* 状态显示 */
.service-status {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 流程指示器 */
.process-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
}

.indicator-step {
    display: flex;
    align-items: center;
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
}

.indicator-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e2e8f0;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-weight: 600;
}

.indicator-step.active .indicator-number {
    background: #06b6d4;
    color: white;
}

.indicator-step.completed .indicator-number {
    background: #10b981;
    color: white;
}

.indicator-arrow {
    color: #cbd5e1;
    font-size: 1.2rem;
} 