#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分系统集成测试
"""

import sys
import os
import unittest
import json
from unittest.mock import patch, MagicMock

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
project_dir = os.path.dirname(backend_dir)
sys.path.insert(0, backend_dir)
sys.path.insert(0, project_dir)

from backend.app_new import create_app
from backend.models.database import db
from backend.models.user import User, UserType
from backend.models.credit import CreditsTransaction, TransactionType
from backend.services.credit_service import credit_service
from backend.config.app_config import AppConfig


class TestCreditIntegration(unittest.TestCase):
    """积分系统集成测试"""
    
    def setUp(self):
        """测试前设置"""
        # 设置测试配置
        AppConfig.DATABASE_URL = 'sqlite:///:memory:'
        AppConfig.TESTING = True
        
        # 创建测试应用
        self.app = create_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # 创建测试用户
            self.test_user = User(
                username='testuser',
                email='<EMAIL>',
                user_type=UserType.free,
                total_credits=10,
                used_credits=0,
                daily_limit=5,
                daily_used=0
            )
            self.test_user.set_password('testpass123')
            db.session.add(self.test_user)
            db.session.commit()
            
            # 获取JWT令牌
            login_response = self.client.post('/api/user/login', 
                json={'email': '<EMAIL>', 'password': 'testpass123'})
            login_data = json.loads(login_response.data)
            self.access_token = login_data['access_token']
            self.headers = {'Authorization': f'Bearer {self.access_token}'}
    
    def tearDown(self):
        """测试后清理"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_credit_calculation(self):
        """测试积分计算"""
        with self.app.app_context():
            # 测试基础生成任务
            cost = credit_service.calculate_task_cost('generate', 'flux-pro')
            self.assertEqual(cost, 1)  # 基础消费1积分，flux-pro倍数1.0
            
            # 测试编辑任务
            cost = credit_service.calculate_task_cost('edit', 'flux-kontext-pro')
            self.assertEqual(cost, 3)  # 基础消费2积分，flux-kontext-pro倍数1.5
            
            # 测试风格迁移任务
            cost = credit_service.calculate_task_cost('style', 'flux-kontext-max')
            self.assertEqual(cost, 6)  # 基础消费3积分，flux-kontext-max倍数2.0
    
    def test_user_limits_check(self):
        """测试用户限制检查"""
        with self.app.app_context():
            # 测试积分充足的情况
            result = credit_service.check_user_limits(self.test_user.id, 5)
            self.assertTrue(result['success'])
            
            # 测试积分不足的情况
            result = credit_service.check_user_limits(self.test_user.id, 15)
            self.assertFalse(result['success'])
            self.assertIn('积分不足', result['message'])
    
    def test_credit_consumption(self):
        """测试积分消费"""
        with self.app.app_context():
            # 消费积分
            result = credit_service.consume_credits(
                self.test_user.id, 3, '测试消费', 'test_task_123'
            )
            self.assertTrue(result['success'])
            self.assertEqual(result['remaining_credits'], 7)
            
            # 验证用户积分更新
            db.session.refresh(self.test_user)
            self.assertEqual(self.test_user.used_credits, 3)
            self.assertEqual(self.test_user.daily_used, 1)
            
            # 验证交易记录
            transaction = CreditsTransaction.query.filter_by(
                user_id=self.test_user.id,
                related_task_id='test_task_123'
            ).first()
            self.assertIsNotNone(transaction)
            self.assertEqual(transaction.credits_amount, -3)
    
    def test_credit_refund(self):
        """测试积分退还"""
        with self.app.app_context():
            # 先消费积分
            credit_service.consume_credits(self.test_user.id, 5, '测试消费')
            
            # 退还积分
            result = credit_service.refund_credits(
                self.test_user.id, 3, '测试退还', 'test_task_456'
            )
            self.assertTrue(result['success'])
            self.assertEqual(result['new_balance'], 8)
            
            # 验证用户积分更新
            db.session.refresh(self.test_user)
            self.assertEqual(self.test_user.used_credits, 2)
    
    @patch('backend.services.image_service.image_service.start_generation_task')
    def test_generate_api_with_credits(self, mock_start_task):
        """测试图像生成API的积分集成"""
        mock_start_task.return_value = None
        
        # 测试有足够积分的情况
        response = self.client.post('/api/generate', 
            data={'prompt': '测试提示词', 'model': 'flux-pro'},
            headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('task_id', data['data'])
        self.assertIn('credits_cost', data['data'])
        self.assertIn('remaining_credits', data['data'])
    
    @patch('backend.services.image_service.image_service.start_generation_task')
    def test_generate_api_insufficient_credits(self, mock_start_task):
        """测试积分不足时的API响应"""
        mock_start_task.return_value = None
        
        # 先消费大部分积分
        with self.app.app_context():
            credit_service.consume_credits(self.test_user.id, 9, '预消费')
        
        # 尝试生成图像（需要1积分，但只剩1积分，应该成功）
        response = self.client.post('/api/generate', 
            data={'prompt': '测试提示词', 'model': 'flux-pro'},
            headers=self.headers)
        
        self.assertEqual(response.status_code, 200)
        
        # 再次尝试（现在积分不足）
        response = self.client.post('/api/generate', 
            data={'prompt': '测试提示词', 'model': 'flux-pro'},
            headers=self.headers)
        
        self.assertEqual(response.status_code, 402)  # Payment Required
        data = json.loads(response.data)
        self.assertFalse(data['success'])
        self.assertIn('积分不足', data['message'])
    
    def test_daily_limit_check(self):
        """测试每日限制检查"""
        with self.app.app_context():
            # 达到每日限制
            self.test_user.daily_used = 5
            db.session.commit()
            
            # 检查限制
            result = credit_service.check_user_limits(self.test_user.id, 1)
            self.assertFalse(result['success'])
            self.assertIn('今日使用次数已达上限', result['message'])
    
    def test_credit_api_endpoints(self):
        """测试积分相关API端点"""
        # 测试获取积分信息
        response = self.client.get('/api/user/credits', headers=self.headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertIn('credits', data)
        
        # 测试获取积分摘要
        response = self.client.get('/api/user/credits/summary', headers=self.headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        
        # 测试计算任务成本
        response = self.client.post('/api/user/credits/calculate-cost',
            json={'task_type': 'generate', 'model': 'flux-pro'},
            headers=self.headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        self.assertEqual(data['cost'], 1)
        
        # 测试检查用户限制
        response = self.client.post('/api/user/credits/check-limits',
            json={'credits_needed': 5},
            headers=self.headers)
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertTrue(data['success'])


if __name__ == '__main__':
    unittest.main()
