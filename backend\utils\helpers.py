import os
import time
import uuid
from typing import Dict, Any, <PERSON><PERSON>
from datetime import datetime

def generate_unique_id() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())

def generate_timestamp_filename(original_filename: str, prefix: str = '') -> str:
    """生成带时间戳的文件名"""
    timestamp = int(time.time())
    name, ext = os.path.splitext(original_filename)
    if prefix:
        return f"{timestamp}_{prefix}_{name}{ext}"
    return f"{timestamp}_{name}{ext}"

def calculate_gcd(a: int, b: int) -> int:
    """计算最大公约数"""
    while b:
        a, b = b, a % b
    return a

def calculate_aspect_ratio(width: int, height: int) -> str:
    """计算并返回标准化的宽高比字符串"""
    # 简化比例
    common_divisor = calculate_gcd(width, height)
    simplified_width = width // common_divisor
    simplified_height = height // common_divisor
    
    # BFL API 支持的宽高比范围是 21:9 到 9:21
    ratio = simplified_width / simplified_height
    
    # 如果比例超出支持范围，调整到最近的支持比例
    if ratio > 21/9:  # 太宽
        simplified_width = 21
        simplified_height = 9
    elif ratio < 9/21:  # 太高
        simplified_width = 9
        simplified_height = 21
    else:
        # 匹配常见宽高比
        common_ratios = [
            (1, 1), (4, 3), (3, 4), (16, 9), (9, 16),
            (21, 9), (9, 21), (3, 2), (2, 3), (5, 4), (4, 5)
        ]
        
        current_ratio = simplified_width / simplified_height
        best_match = (simplified_width, simplified_height)
        min_diff = float('inf')
        
        for w, h in common_ratios:
            ratio_diff = abs(current_ratio - w/h)
            if ratio_diff < min_diff:
                min_diff = ratio_diff
                best_match = (w, h)
        
        # 如果差异不大（小于15%），使用常见比例
        if min_diff < 0.15:
            simplified_width, simplified_height = best_match
    
    return f"{simplified_width}:{simplified_height}"

def create_response(success: bool, message_or_data=None, data=None, status_code: int = 200):
    """
    创建标准化的API响应
    
    支持两种调用方式：
    1. create_response(success, message, data) - 用于翻译路由
    2. create_response(success, data, error, status_code) - 用于其他路由
    
    Returns:
        dict: 标准化的响应字典
    """
    response = {'success': success}
    
    if success:
        if data is not None:
            # 方式1: create_response(True, "message", {data})
            response['message'] = str(message_or_data) if message_or_data else '操作成功'
            if isinstance(data, dict):
                response.update(data)
            else:
                response['data'] = data
        elif isinstance(message_or_data, dict):
            # 方式2: create_response(True, {data})
            response.update(message_or_data)
        else:
            # 只有消息
            response['message'] = str(message_or_data) if message_or_data else '操作成功'
    else:
        # 失败的情况
        error_msg = str(message_or_data) if message_or_data else '操作失败'
        response['error'] = error_msg
        if status_code == 200:
            status_code = 400
    
    return response

def format_task_for_response(task_id: str, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """格式化任务数据用于API响应"""
    task_info = task_data.copy()
    task_info['task_id'] = task_id
    
    # 如果任务完成且有输出文件，添加URL
    if task_info['status'] == 'completed' and 'output_file' in task_info:
        filename = os.path.basename(task_info['output_file'])
        task_info['download_url'] = f'/download/{filename}'
        task_info['view_url'] = f'/view/{filename}'
    
    return task_info

def log_operation(operation: str, details: str = '', success: bool = True):
    """记录操作日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    status = "✅" if success else "❌"
    print(f"[{timestamp}] {status} {operation}: {details}")

def safe_int_conversion(value: str, default: int = 0) -> int:
    """安全的整数转换"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float_conversion(value: str, default: float = 0.0) -> float:
    """安全的浮点数转换"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default