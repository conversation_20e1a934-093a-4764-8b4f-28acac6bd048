"""
用户相关数据模型
"""
from .database import db, BaseModel
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import enum
import uuid


class UserType(enum.Enum):
    """用户类型枚举"""
    free = "free"
    premium = "premium"
    pro = "pro"
    enterprise = "enterprise"


class User(BaseModel):
    """用户模型"""
    __tablename__ = 'users'
    
    # 主键UUID
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    # 基本信息 - 手机号为主要身份标识
    phone = db.Column(db.String(20), unique=True, nullable=False, index=True)  # 主要身份标识
    username = db.Column(db.String(50), unique=True, nullable=True, index=True)  # 显示标识
    email = db.Column(db.String(100), unique=True, nullable=True, index=True)  # 辅助身份标识
    password_hash = db.Column(db.String(255), nullable=False)
    
    # 状态字段
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_verified = db.Column(db.Boolean, default=False, nullable=False)
    verification_token = db.Column(db.String(255))
    reset_token = db.Column(db.String(255))
    reset_token_expires = db.Column(db.DateTime)
    last_login = db.Column(db.DateTime)
    
    # 用户类型和权限
    user_type = db.Column(db.Enum(UserType), default=UserType.free, nullable=False)
    subscription_expires = db.Column(db.DateTime)
    
    # 积分系统
    total_credits = db.Column(db.Integer, default=10, nullable=False)  # 总积分
    used_credits = db.Column(db.Integer, default=0, nullable=False)    # 已使用积分
    
    # 使用限制
    daily_limit = db.Column(db.Integer, default=5, nullable=False)     # 每日限制
    daily_used = db.Column(db.Integer, default=0, nullable=False)      # 今日已使用
    daily_reset_date = db.Column(db.Date, default=date.today)          # 每日重置日期

    # 软删除
    is_deleted = db.Column(db.Boolean, default=False, nullable=False)  # 是否已删除（注销）
    deleted_at = db.Column(db.DateTime)                                # 删除时间
    
    # 关系
    profile = db.relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    auth_methods = db.relationship("UserAuth", back_populates="user", cascade="all, delete-orphan")
    tasks = db.relationship("Task", back_populates="user", lazy='dynamic')
    credit_transactions = db.relationship("CreditsTransaction", back_populates="user", lazy='dynamic')
    subscriptions = db.relationship("Subscription", back_populates="user", lazy='dynamic')
    payments = db.relationship("Payment", back_populates="user", lazy='dynamic')
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 根据用户类型设置默认限制
        if self.user_type == UserType.free:
            self.daily_limit = 5
            self.total_credits = 10
        elif self.user_type == UserType.premium:
            self.daily_limit = 50
            self.total_credits = 100
        elif self.user_type == UserType.pro:
            self.daily_limit = 200
            self.total_credits = 500
        elif self.user_type == UserType.enterprise:
            self.daily_limit = -1  # 无限制
            self.total_credits = 1000
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    @property
    def available_credits(self):
        """可用积分"""
        return self.total_credits - self.used_credits
    
    @property
    def daily_remaining(self):
        """今日剩余次数"""
        if self.daily_limit == -1:
            return -1  # 无限制
        return max(0, self.daily_limit - self.daily_used)
    
    def can_perform_task(self, credits_needed=1):
        """检查是否可以执行任务"""
        # 检查积分
        if self.available_credits < credits_needed:
            return False, "积分不足"
        
        # 检查每日限制
        if self.daily_limit != -1 and self.daily_used >= self.daily_limit:
            return False, "今日使用次数已达上限"
        
        return True, "可以执行"
    
    def consume_credits(self, amount, description=""):
        """消费积分"""
        if self.available_credits < amount:
            raise ValueError("积分不足")
        
        self.used_credits += amount
        self.daily_used += 1
        
        # 记录积分交易
        from .credit import CreditsTransaction
        transaction = CreditsTransaction(
            user_id=self.id,
            transaction_type='usage',
            credits_amount=-amount,
            balance_after=self.available_credits,
            description=description
        )
        db.session.add(transaction)
        
        return transaction
    
    def add_credits(self, amount, description="", transaction_type='purchase'):
        """增加积分"""
        self.total_credits += amount
        
        # 记录积分交易
        from .credit import CreditsTransaction
        transaction = CreditsTransaction(
            user_id=self.id,
            transaction_type=transaction_type,
            credits_amount=amount,
            balance_after=self.available_credits,
            description=description
        )
        db.session.add(transaction)
        
        return transaction
    
    def reset_daily_usage(self):
        """重置每日使用次数"""
        today = date.today()
        # 处理 daily_reset_date 为 None 的情况
        if self.daily_reset_date is None or self.daily_reset_date < today:
            self.daily_used = 0
            self.daily_reset_date = today
            return True
        return False
    
    def upgrade_user_type(self, new_type):
        """升级用户类型"""
        if isinstance(new_type, str):
            new_type = UserType(new_type)
        
        self.user_type = new_type
        
        # 更新权限
        if new_type == UserType.premium:
            self.daily_limit = 50
        elif new_type == UserType.pro:
            self.daily_limit = 200
        elif new_type == UserType.enterprise:
            self.daily_limit = -1

    def soft_delete(self):
        """软删除用户（注销）"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.is_active = False  # 同时设置为非活跃状态

    def restore(self):
        """恢复已删除的用户"""
        self.is_deleted = False
        self.deleted_at = None
        self.is_active = True

    def to_dict(self, include_sensitive=False):
        """转换为字典，可选择是否包含敏感信息"""
        data = super().to_dict()
        
        # 移除敏感信息
        if not include_sensitive:
            data.pop('password_hash', None)
            data.pop('verification_token', None)
            data.pop('reset_token', None)
        
        # 添加计算字段
        data['available_credits'] = self.available_credits
        data['daily_remaining'] = self.daily_remaining
        data['user_type'] = self.user_type.value if self.user_type else 'free'
        data['is_deleted'] = self.is_deleted
        data['deleted_at'] = self.deleted_at.isoformat() if self.deleted_at else None

        return data
    
    def __repr__(self):
        return f'<User {self.username}>'


class UserProfile(BaseModel):
    """用户资料模型"""
    __tablename__ = 'user_profiles'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, unique=True)
    display_name = db.Column(db.String(100))
    avatar_url = db.Column(db.String(255))
    bio = db.Column(db.Text)
    website = db.Column(db.String(255))
    location = db.Column(db.String(100))
    preferences = db.Column(db.JSON)  # 存储用户偏好设置
    
    # 关系
    user = db.relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f'<UserProfile {self.display_name or self.user.username}>'
