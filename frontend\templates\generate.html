{% extends "base.html" %}

{% block title %}图像生成 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-generate{% endblock %}

{% block extra_head %}
<link href="{{ url_for('static', filename='css/pages/generate.css') }}" rel="stylesheet">
{% endblock %}

{% block extra_css %}
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paint-brush"></i> 智能图像生成
                </h5>
            </div>
            <div class="card-body">
                <form id="generateForm" action="/api/generate" method="POST">
                    <!-- 提示词输入 -->
                    <div class="mb-3">
                        <label for="prompt" class="form-label">
                            <i class="fas fa-pen"></i> 图像描述 *
                        </label>
                        <textarea class="form-control" id="prompt" name="prompt" rows="4" 
                                placeholder="请详细描述您想要生成的图像，支持中文或英文..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-lightbulb"></i> 提示：详细的描述能获得更好的效果，支持中文输入
                        </div>
                    </div>
                    
                    <!-- 处理状态显示 -->
                    <div class="mb-3" id="processStatusArea" style="display: none;">
                        <div class="alert alert-success d-flex align-items-center" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <span id="processStatusText">处理完成</span>
                        </div>
                    </div>
                    
                    <!-- 智能工作流按钮 -->
                    <div class="mb-3">
                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-success w-100 unified-btn" id="polishBtn">
                                    <i class="fas fa-sparkles"></i> 润色
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary w-100 unified-btn" id="smartGenerateBtn">
                                    <i class="fas fa-magic"></i> <span id="smartBtnText">开始绘图</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 超级润色选项 -->
                        <div class="row g-2 mt-2">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="promptUpsampling" name="prompt_upsampling">
                                    <label class="form-check-label" for="promptUpsampling">
                                        <i class="fas fa-rocket"></i> 超级润色 (AI提示词增强)
                                    </label>
                                    <div class="form-text">
                                        <small>开启后可获得更具创意的生成效果，但结果可能不太稳定</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-text mt-2">
                            <i class="fas fa-info-circle"></i> 
                            <strong>智能工作流：</strong> 可先润色提升质量，开始绘图会自动检测语言并处理，确保最佳生成效果
                        </div>
                        
                        <!-- 智能处理状态 -->
                        <div id="smartProcessStatus" class="mt-2" style="display: none;">
                            <div class="alert alert-info d-flex align-items-center" role="alert">
                                <i class="fas fa-cogs fa-spin me-2"></i>
                                <span id="smartStatusText">智能处理中...</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 风格选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-palette"></i> 绘画风格 (可选)
                        </label>
                        <div class="row g-2" id="styleButtons">
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="watercolor">
                                    <i class="fas fa-tint"></i> 水彩
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="chinese-painting">
                                    <i class="fas fa-mountain"></i> 国画
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="oil-painting">
                                    <i class="fas fa-brush"></i> 油画
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="realistic">
                                    <i class="fas fa-camera"></i> 写实
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="cinematic">
                                    <i class="fas fa-film"></i> 电影
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="anime">
                                    <i class="fas fa-star"></i> 动漫
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="sketch">
                                    <i class="fas fa-pencil-alt"></i> 素描
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button type="button" class="btn btn-outline-secondary style-btn w-100" data-style="abstract">
                                    <i class="fas fa-shapes"></i> 抽象
                                </button>
                            </div>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i> 选择风格后将在生成图像时自动应用相应的风格描述，不会修改您的原始描述
                        </div>
                    </div>
                    
                    <!-- 模型选择和尺寸设置 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">
                                    <i class="fas fa-cog"></i> 模型选择
                                </label>
                                <select class="form-select model-select-enhanced" id="model" name="model">
                                    <option value="flux-kontext-pro" selected>FLUX Kontext Pro (推荐)</option>
                                    <option value="flux-kontext-max">FLUX Kontext Max (最高质量)</option>
                                    <option value="flux-pro-1.1-ultra">FLUX Pro 1.1 Ultra</option>
                                    <option value="flux-pro-1.1">FLUX Pro 1.1</option>
                                    <option value="flux-pro">FLUX Pro</option>
                                    <option value="flux-dev">FLUX Dev (快速)</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-mouse-pointer"></i> 点击可下拉选择不同AI模型
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="aspect_ratio" class="form-label">
                                    <i class="fas fa-expand-arrows-alt"></i> 宽高比
                                </label>
                                <select class="form-select aspect-select-enhanced" id="aspect_ratio" name="aspect_ratio">
                                    <option value="1:1">1:1 (正方形)</option>
                                    <option value="2:3">2:3 (竖向)</option>
                                    <option value="3:4">3:4 (竖向矩形)</option>
                                    <option value="9:16" selected>9:16 (竖屏)</option>
                                    <option value="21:9">21:9 (超宽屏)</option>
                                    <option value="9:21">9:21 (超高屏)</option>
                                    <option value="16:9">16:9 (宽屏)</option>
                                    <option value="4:3">4:3 (横向矩形)</option>
                                    <option value="3:2">3:2 (横向)</option>
                                    <option value="custom">自定义尺寸</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-mouse-pointer"></i> 点击可下拉选择图像比例
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 自定义尺寸设置 -->
                    <div class="row">
                        <div class="col-md-6"></div>
                        <div class="col-md-6">
                            <div class="mb-3" id="customSizeArea" style="display: none;">
                                <label class="form-label">
                                    <i class="fas fa-ruler"></i> 自定义尺寸 (像素)
                                </label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" id="width" name="width" 
                                               placeholder="宽度" min="256" max="2752" step="32">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" id="height" name="height" 
                                               placeholder="高度" min="256" max="2752" step="32">
                                    </div>
                                </div>
                                <div class="form-text" id="customSizeHelpText">必须是32的倍数</div>
                                <div class="alert alert-warning mt-2" id="kontextModelWarning" style="display: none;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>注意：</strong> Kontext 系列模型仅支持宽高比参数，不支持精确像素设置。请改用宽高比选择。
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级参数 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h"></i> 高级参数
                                <button type="button" class="btn btn-sm btn-outline-secondary float-end" 
                                        data-bs-toggle="collapse" data-bs-target="#advancedParams">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="collapse" id="advancedParams">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="steps" class="form-label">扩散步数 (1-50)</label>
                                            <input type="number" class="form-control" id="steps" name="steps" 
                                                   min="1" max="50" placeholder="默认: 40">
                                            <div class="form-text">更高的步数通常产生更好的质量</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="seed" class="form-label">随机种子</label>
                                            <input type="number" class="form-control" id="seed" name="seed" 
                                                   placeholder="留空为随机">
                                            <div class="form-text">相同种子产生相同结果</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="guidance" class="form-label">引导强度</label>
                                            <input type="number" class="form-control" id="guidance" name="guidance" 
                                                   step="0.1" placeholder="默认值">
                                            <div class="form-text">控制对提示词的遵循程度</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="safety_tolerance" class="form-label">安全容忍度 (0-6)</label>
                                            <select class="form-select" id="safety_tolerance" name="safety_tolerance">
                                                <option value="0">0 (最严格)</option>
                                                <option value="1">1</option>
                                                <option value="2">2</option>
                                                <option value="3">3</option>
                                                <option value="4">4</option>
                                                <option value="5">5</option>
                                                <option value="6" selected>6 (最宽松)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check mt-4" id="rawOption" style="display: none;">
                                                <input class="form-check-input" type="checkbox" id="raw" name="raw">
                                                <label class="form-check-label" for="raw">
                                                    自然图像模式
                                                </label>
                                                <div class="form-text">仅Ultra模型支持</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 测试模式和重置按钮 -->
                    <div class="mt-4">
                        <div class="row g-2">
                            <div class="col-md-6">
                                <div class="form-check form-switch h-100 d-flex align-items-center">
                                    <input class="form-check-input" type="checkbox" id="testMode" name="testMode">
                                    <label class="form-check-label" for="testMode">
                                        <i class="fas fa-bug"></i> 测试模式（显示API请求内容）
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-secondary w-100 unified-btn" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> 重置表单
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 状态和结果显示 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 生成状态
                </h5>
            </div>
            <div class="card-body">
                <div id="timelineArea" class="small text-muted mb-3" style="max-height: 200px; overflow-y: auto;"></div>
                <div id="statusArea"></div>
            </div>
        </div>
        
        <!-- 示例提示词 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i> 示例提示词
                </h6>
            </div>
            <div class="card-body">
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>风景:</strong> 日出时宁静的山间湖泊，水面升起雾气，背景是雪山峰顶，摄影写实风格
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>人物:</strong> 一位智慧的老巫师肖像，长长的白胡子，穿着蓝色长袍，奇幻艺术风格
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>动物:</strong> 一只威严的老鹰在云雾中翱翔，双翅展开，戏剧性的光影效果
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>抽象:</strong> 充满活力色彩和流动形状的抽象几何构图，现代艺术风格
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="module" src="{{ url_for('static', filename='js/pages/generate.js') }}"></script>
{% endblock %} 