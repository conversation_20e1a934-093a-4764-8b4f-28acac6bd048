#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BFL AI 图像生成器
基于 Black Forest Labs API 实现的简单图像生成工具
"""

import os
import time
import json
import requests
from typing import Optional, Dict, Any, Union
from urllib.parse import urlparse
import argparse
import base64
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import ssl


class BFLImageGenerator:
    """BFL AI 图像生成器类"""
    
    def __init__(self, api_key: str):
        """
        初始化生成器
        
        Args:
            api_key: BFL API 密钥
        """
        self.api_key = api_key  # 使用传入的API密钥
        self.base_url = "https://api.bfl.ai/v1"  # 修正API基础URL
        self.headers = {
            "accept": "application/json",
            "x-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        # 创建带重试机制的会话
        self.session = self._create_robust_session()
    
    def _create_robust_session(self):
        """
        创建具有重试机制和SSL优化的HTTP会话
        """
        session = requests.Session()
        
        # 配置重试策略
        try:
            # 尝试使用新版本的参数名
            retry_strategy = Retry(
                total=3,  # 总重试次数
                status_forcelist=[500, 502, 503, 504],  # 需要重试的HTTP状态码
                allowed_methods=["HEAD", "GET", "OPTIONS", "POST"],  # 允许重试的HTTP方法
                backoff_factor=1,  # 重试间隔倍数 (1, 2, 4秒)
                raise_on_redirect=False,
                raise_on_status=False
            )
        except TypeError:
            # 如果新参数名不支持，使用旧版本的参数名
            retry_strategy = Retry(
                total=3,  # 总重试次数
                status_forcelist=[500, 502, 503, 504],  # 需要重试的HTTP状态码
                method_whitelist=["HEAD", "GET", "OPTIONS", "POST"],  # 允许重试的HTTP方法
                backoff_factor=1,  # 重试间隔倍数 (1, 2, 4秒)
                raise_on_redirect=False,
                raise_on_status=False
            )
        
        # 配置HTTP适配器
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=20,
            pool_block=False
        )
        
        # 挂载适配器
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 设置超时
        session.timeout = (30, 120)  # 连接超时30秒，读取超时120秒
        
        return session
    
    def close(self):
        """
        关闭HTTP会话，释放资源
        """
        if hasattr(self, 'session'):
            self.session.close()
    
    def __del__(self):
        """
        析构函数，确保会话被正确关闭
        """
        self.close()
    
    def encode_image_to_base64(self, image_path: str) -> str:
        """
        将图像文件编码为base64字符串
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            base64编码的图像字符串
        """
        try:
            print(f"🔄 正在编码图像: {image_path}")
            
            if not os.path.exists(image_path):
                print(f"❌ 图像文件不存在: {image_path}")
                return None
            
            # 获取文件大小
            file_size = os.path.getsize(image_path)
            print(f"📁 图像文件大小: {file_size / 1024 / 1024:.2f} MB")
            
            # 检测图像格式
            import imghdr
            image_type = imghdr.what(image_path)
            if not image_type:
                # 如果imghdr无法检测，尝试从文件扩展名推断
                ext = os.path.splitext(image_path)[1].lower()
                if ext in ['.jpg', '.jpeg']:
                    image_type = 'jpeg'
                elif ext in ['.png']:
                    image_type = 'png'
                elif ext in ['.gif']:
                    image_type = 'gif'
                elif ext in ['.webp']:
                    image_type = 'webp'
                else:
                    print(f"❌ 无法识别图像格式: {image_path}")
                    return None
            
            print(f"🖼️  检测到图像格式: {image_type}")
            
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                
                # 根据实际格式设置MIME类型
                if image_type in ['jpg', 'jpeg']:
                    mime_type = 'image/jpeg'
                elif image_type == 'png':
                    mime_type = 'image/png'
                elif image_type == 'gif':
                    mime_type = 'image/gif'
                elif image_type == 'webp':
                    mime_type = 'image/webp'
                else:
                    # 默认使用jpeg
                    mime_type = 'image/jpeg'
                
                # 根据BFL官方文档，只返回纯base64字符串，不加data:前缀
                print(f"✅ 图像编码成功，base64长度: {len(encoded_string)} 字符")
                return encoded_string
                
        except Exception as e:
            print(f"❌ 图像编码失败: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return None
    
    def submit_generation_request(self, 
                                prompt: str, 
                                model: str = "flux-kontext-pro",
                                aspect_ratio: str = "9:16",
                                width: Optional[int] = None,
                                height: Optional[int] = None,
                                steps: Optional[int] = None,
                                guidance: Optional[float] = None,
                                seed: Optional[int] = None,
                                prompt_upsampling: bool = False,
                                safety_tolerance: int = 2,
                                raw: bool = False,
                                image: Optional[Union[str, bytes]] = None,
                                **kwargs) -> Optional[str]:
        """
        提交图像生成请求
        
        Args:
            prompt: 图像描述提示词
            model: 使用的模型 (flux-kontext-pro, flux-kontext-max, flux-pro-1.1-ultra, 
                   flux-pro-1.1, flux-pro, flux-dev)
            aspect_ratio: 图像宽高比 (当未指定width/height时使用)
            width: 图像宽度 (像素，必须是32的倍数)
            height: 图像高度 (像素，必须是32的倍数)
            steps: 扩散步数 (1-50，默认40)
            guidance: 引导强度 (仅某些模型支持)
            seed: 随机种子 (用于可重现的结果)
            prompt_upsampling: 是否对提示词进行上采样增强
            safety_tolerance: 安全容忍度 (0-6，6为最宽松)
            raw: 是否生成更自然、处理更少的图像 (仅ultra模型支持)
            image: 参考图像 (文件路径或base64字符串，用于以图生图)
            **kwargs: 其他参数
            
        Returns:
            请求ID，如果失败返回None
        """
        endpoint = f"{self.base_url}/{model}"
        
        # 构建请求载荷
        payload = {
            "prompt": prompt
        }
        
        # 添加尺寸参数 - 优先使用width/height，否则使用aspect_ratio
        if width is not None and height is not None:
            payload["width"] = width
            payload["height"] = height
        else:
            payload["aspect_ratio"] = aspect_ratio
        
        # 处理参考图像 - 根据模型类型使用正确的参数名
        if image is not None:
            print(f"🖼️  处理输入图像: {image}")
            
            # 根据官方文档确定参数名
            if "kontext" in model.lower():
                image_param_name = "input_image"  # Kontext模型用于图像编辑
            elif "pro-1.1" in model.lower() or "ultra" in model.lower():
                image_param_name = "image_prompt"  # Pro 1.1和Ultra模型用于Redux/混合
            else:
                image_param_name = "input_image"  # 默认使用input_image
            
            if isinstance(image, str):
                if os.path.exists(image):
                    # 如果是文件路径，编码为base64
                    print(f"📁 图像文件路径: {image}")
                    encoded_image = self.encode_image_to_base64(image)
                    if encoded_image:
                        payload[image_param_name] = encoded_image
                        print(f"✅ 图像已添加到请求载荷 (使用 {image_param_name} 参数)")
                    else:
                        print("❌ 图像编码失败，这将导致功能无法正常工作")
                        return None
                else:
                    # 假设是base64字符串
                    print(f"📝 假设输入为base64字符串，长度: {len(image)} 字符")
                    payload[image_param_name] = image
            else:
                print("❌ 不支持的图像格式，请提供文件路径或base64字符串")
                return None
        
        # 添加可选参数
        if steps is not None:
            payload["steps"] = steps
        if guidance is not None:
            payload["guidance"] = guidance
        if seed is not None:
            payload["seed"] = seed
        if prompt_upsampling:
            payload["prompt_upsampling"] = prompt_upsampling
        if safety_tolerance is not None:
            payload["safety_tolerance"] = safety_tolerance
        if raw and "ultra" in model:
            payload["raw"] = raw
        
        # 添加其他参数
        payload.update(kwargs)
        
        try:
            print(f"🚀 正在提交生成请求...")
            print(f"📝 提示词: {prompt}")
            print(f"🎨 模型: {model}")
            print(f"📐 宽高比: {aspect_ratio}")
            print(f"🌐 API端点: {endpoint}")
            print(f"🔑 API密钥: {self.api_key[:8]}..." if self.api_key else "❌ API密钥为空!")
            
            # 打印载荷信息（不包含base64图像数据以避免日志过长）
            debug_payload = payload.copy()
            for image_key in ["input_image", "image_prompt", "image"]:
                if image_key in debug_payload:
                    debug_payload[image_key] = f"[base64图像数据，长度: {len(payload[image_key])} 字符]"
            print(f"📦 请求载荷: {json.dumps(debug_payload, ensure_ascii=False, indent=2)}")
            print(f"📡 请求头: {self.headers}")
            
            print("🔄 开始发送HTTP请求...")
            start_time = time.time()
            
            # 使用强化版session发送请求，具备自动重试功能
            response = self.session.post(
                endpoint, 
                headers=self.headers, 
                json=payload,
                timeout=(30, 120)  # 连接超时30秒，读取超时120秒
            )
            
            elapsed_time = time.time() - start_time
            print(f"⏱️  HTTP请求耗时: {elapsed_time:.2f}秒")
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"📄 响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                request_id = result.get("id")
                print(f"✅ 请求提交成功! 请求ID: {request_id}")
                return request_id
            elif response.status_code == 429:
                print("⚠️  请求过于频繁，请稍后再试")
                print(f"📄 响应内容: {response.text}")
            elif response.status_code == 402:
                print("💳 积分不足，请前往 https://api.us1.bfl.ai 充值")
                print(f"📄 响应内容: {response.text}")
            elif response.status_code == 401:
                print("🔒 认证失败，请检查API密钥")
                print(f"📄 响应内容: {response.text}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"📄 响应内容: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时 (30秒)，可能是网络问题或API服务响应慢")
        except requests.exceptions.ConnectionError as e:
            print(f"🌐 网络连接错误: {str(e)}")
        except requests.exceptions.RequestException as e:
            print(f"📡 HTTP请求异常: {str(e)}")
        except Exception as e:
            print(f"❌ 未知异常: {str(e)}")
            import traceback
            print(f"🔍 异常详情: {traceback.format_exc()}")
            
        return None
    
    def get_result(self, request_id: str) -> Optional[Dict[str, Any]]:
        """
        获取生成结果
        
        Args:
            request_id: 请求ID
            
        Returns:
            结果字典，如果失败返回None
        """
        endpoint = f"{self.base_url}/get_result"
        params = {"id": request_id}
        
        try:
            response = self.session.get(endpoint, headers=self.headers, params=params, timeout=(10, 30))
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取结果失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 获取结果超时")
        except requests.exceptions.ConnectionError as e:
            print(f"🌐 获取结果连接错误: {str(e)}")
        except Exception as e:
            print(f"❌ 获取结果异常: {str(e)}")
            
        return None
    
    def poll_for_result(self, request_id: str, max_wait_time: int = 300) -> Optional[Dict[str, Any]]:
        """
        轮询等待生成结果
        
        Args:
            request_id: 请求ID
            max_wait_time: 最大等待时间（秒）
            
        Returns:
            结果字典，包含状态、图像URL（如果成功）和详细信息
        """
        print(f"⏳ 正在等待生成结果...")
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            result = self.get_result(request_id)
            
            if result:
                status = result.get("status")
                print(f"📊 当前状态: {status}")
                
                if status == "Ready":
                    image_url = result.get("result", {}).get("sample")
                    if image_url:
                        print(f"🎉 生成完成! 图像URL: {image_url}")
                        return {
                            'success': True,
                            'status': status,
                            'image_url': image_url,
                            'details': result.get('details')
                        }
                    else:
                        print("❌ 结果中未找到图像URL")
                        return {
                            'success': False,
                            'status': status,
                            'error': '结果中未找到图像URL',
                            'details': result.get('details')
                        }
                        
                elif status == "Content Moderated":
                    print(f"⚠️ 内容审核信息: {result}")
                    return {
                        'success': False,
                        'status': status,
                        'error': '内容审核未通过',
                        'details': result.get('details'),
                        'raw_response': result
                    }
                    
                elif status == "Task not found":
                    # 根据官方文档，这是正常的初始状态，继续等待
                    pass
                    
                elif status in ["Error", "Failed"]:
                    print(f"❌ 生成失败: {result}")
                    return {
                        'success': False,
                        'status': status,
                        'error': result.get('details') or '生成失败',
                        'raw_response': result
                    }
                    
                elif status == "Pending":
                    # 任务排队中，继续等待
                    pass
                
                elif status == "Request Moderated":
                    print(f"⚠️ 请求审核信息: {result}")
                    return {
                        'success': False,
                        'status': status,
                        'error': '请求被内容审核拦截',
                        'details': result.get('details'),
                        'raw_response': result
                    }
                    
            time.sleep(2)  # 等待2秒后重试
            
        print(f"⏰ 等待超时 ({max_wait_time}秒)")
        return {
            'success': False,
            'status': 'Timeout',
            'error': f'等待超时 ({max_wait_time}秒)',
            'last_response': result if result else None
        }
    
    def download_image(self, image_url: str, filename: Optional[str] = None) -> bool:
        """
        下载生成的图像
        
        Args:
            image_url: 图像URL
            filename: 保存文件名，如果为None则自动生成
            
        Returns:
            是否下载成功
        """
        try:
            if not filename:
                # 从URL中提取文件名或使用时间戳
                parsed_url = urlparse(image_url)
                if parsed_url.path:
                    filename = os.path.basename(parsed_url.path)
                if not filename or not filename.endswith(('.jpg', '.jpeg', '.png', '.webp')):
                    filename = f"generated_image_{int(time.time())}.jpg"
            
            print(f"📥 正在下载图像: {filename}")
            
            response = self.session.get(image_url, stream=True, timeout=(10, 60))
            response.raise_for_status()
            
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"✅ 图像已保存: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ 下载失败: {str(e)}")
            return False
    
    def generate_image(self, 
                      prompt: str, 
                      model: str = "flux-kontext-pro",
                      aspect_ratio: str = "9:16",
                      output_filename: Optional[str] = None,
                      **kwargs) -> bool:
        """
        完整的图像生成流程
        
        Args:
            prompt: 图像描述提示词
            model: 使用的模型
            aspect_ratio: 图像宽高比
            output_filename: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            是否成功生成并下载图像
        """
        # 1. 提交请求
        request_id = self.submit_generation_request(prompt, model, aspect_ratio, **kwargs)
        if not request_id:
            return False
        
        # 2. 等待结果
        result = self.poll_for_result(request_id)
        if not result['success']:
            return False
        
        # 3. 下载图像
        return self.download_image(result['image_url'], output_filename)
    
    def edit_image(self, 
                   input_image: str,
                   prompt: str,
                   model: str = "flux-kontext-pro",
                   output_filename: Optional[str] = None,
                   **kwargs) -> bool:
        """
        图像编辑功能 - 基于文本提示修改现有图像
        
        Args:
            input_image: 输入图像路径
            prompt: 编辑指令 (例如: "将猫的颜色改为红色", "添加一顶帽子")
            model: 使用的模型 (推荐使用 flux-kontext-pro 或 flux-kontext-max)
            output_filename: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            是否成功编辑并下载图像
        """
        if not os.path.exists(input_image):
            print(f"❌ 输入图像文件不存在: {input_image}")
            return False
        
        # 确保使用支持图像编辑的模型
        if "kontext" not in model:
            print("⚠️  建议使用 flux-kontext-pro 或 flux-kontext-max 模型进行图像编辑")
        
        print(f"🖼️  正在编辑图像: {input_image}")
        print(f"✏️  编辑指令: {prompt}")
        
        # 使用图像作为输入进行生成
        return self.generate_image(
            prompt=prompt,
            model=model,
            image=input_image,
            output_filename=output_filename,
            **kwargs
        )
    
    def style_transfer(self,
                      reference_image: str,
                      prompt: str,
                      model: str = "flux-kontext-pro",
                      output_filename: Optional[str] = None,
                      **kwargs) -> bool:
        """
        风格迁移功能 - 基于参考图像的风格生成新图像
        
        Args:
            reference_image: 参考图像路径 (提供风格参考)
            prompt: 内容描述 (描述要生成的内容)
            model: 使用的模型
            output_filename: 输出文件名
            **kwargs: 其他参数
            
        Returns:
            是否成功生成并下载图像
        """
        if not os.path.exists(reference_image):
            print(f"❌ 参考图像文件不存在: {reference_image}")
            return False
        
        print(f"🎨 正在进行风格迁移...")
        print(f"🖼️  参考图像: {reference_image}")
        print(f"📝 内容描述: {prompt}")
        
        # 使用参考图像进行风格迁移
        return self.generate_image(
            prompt=prompt,
            model=model,
            image=reference_image,
            output_filename=output_filename,
            **kwargs
        )
    
    def generate_batch_images(self, 
                             prompts: list, 
                             model: str = "flux-kontext-pro",
                             aspect_ratio: str = "9:16",
                             output_dir: str = "batch_output",
                             max_concurrent: int = 5,
                             **kwargs) -> Dict[str, Any]:
        """
        批量生成图像
        
        Args:
            prompts: 提示词列表
            model: 使用的模型
            aspect_ratio: 图像宽高比
            output_dir: 输出目录
            max_concurrent: 最大并发数 (注意API限制)
            **kwargs: 其他参数
            
        Returns:
            批量生成结果字典
        """
        import os
        import concurrent.futures
        from threading import Semaphore
        
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 限制并发数
        semaphore = Semaphore(min(max_concurrent, len(prompts)))
        results = {
            "total": len(prompts),
            "successful": 0,
            "failed": 0,
            "results": []
        }
        
        def generate_single(prompt_info):
            idx, prompt = prompt_info
            semaphore.acquire()
            try:
                print(f"🎨 正在生成第 {idx + 1}/{len(prompts)} 张图像...")
                
                # 生成文件名
                safe_prompt = "".join(c for c in prompt[:30] if c.isalnum() or c in (' ', '-', '_')).rstrip()
                filename = f"batch_{int(time.time())}_{idx:03d}_{safe_prompt}.jpg"
                filepath = os.path.join(output_dir, filename)
                
                # 生成图像
                success = self.generate_image(
                    prompt=prompt,
                    model=model,
                    aspect_ratio=aspect_ratio,
                    output_filename=filepath,
                    **kwargs
                )
                
                result = {
                    "index": idx,
                    "prompt": prompt,
                    "success": success,
                    "filename": filename if success else None,
                    "filepath": filepath if success else None
                }
                
                if not success:
                    result["error"] = "生成失败"
                
                return result
                
            except Exception as e:
                return {
                    "index": idx,
                    "prompt": prompt,
                    "success": False,
                    "error": str(e)
                }
            finally:
                semaphore.release()
        
        print(f"🚀 开始批量生成 {len(prompts)} 张图像...")
        print(f"📁 输出目录: {output_dir}")
        print(f"⚡ 最大并发数: {max_concurrent}")
        
        # 使用线程池执行批量生成
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            future_to_prompt = {
                executor.submit(generate_single, (idx, prompt)): (idx, prompt) 
                for idx, prompt in enumerate(prompts)
            }
            
            for future in concurrent.futures.as_completed(future_to_prompt):
                result = future.result()
                results["results"].append(result)
                
                if result["success"]:
                    results["successful"] += 1
                    print(f"✅ 第 {result['index'] + 1} 张图像生成成功: {result['filename']}")
                else:
                    results["failed"] += 1
                    print(f"❌ 第 {result['index'] + 1} 张图像生成失败: {result.get('error', '未知错误')}")
        
        # 按索引排序结果
        results["results"].sort(key=lambda x: x["index"])
        
        print(f"\n📊 批量生成完成!")
        print(f"✅ 成功: {results['successful']}")
        print(f"❌ 失败: {results['failed']}")
        print(f"📁 输出目录: {output_dir}")
        
        return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BFL AI 图像生成器")
    parser.add_argument("prompt", nargs="*", help="图像描述提示词 (支持多个提示词进行批量生成)")
    parser.add_argument("--api-key", help="BFL API 密钥 (也可通过环境变量 BFL_API_KEY 设置)")
    parser.add_argument("--model", default="flux-kontext-pro", 
                       choices=["flux-kontext-pro", "flux-kontext-max", "flux-pro-1.1-ultra", 
                               "flux-pro-1.1", "flux-pro", "flux-dev"],
                       help="使用的模型 (默认: flux-pro-1.1)")
    parser.add_argument("--aspect-ratio", default="1:1", help="图像宽高比 (默认: 1:1)")
    parser.add_argument("--width", type=int, help="图像宽度 (像素，必须是32的倍数)")
    parser.add_argument("--height", type=int, help="图像高度 (像素，必须是32的倍数)")
    parser.add_argument("--steps", type=int, help="扩散步数 (1-50)")
    parser.add_argument("--guidance", type=float, help="引导强度")
    parser.add_argument("--seed", type=int, help="随机种子 (用于可重现的结果)")
    parser.add_argument("--prompt-upsampling", action="store_true", help="启用提示词上采样增强")
    parser.add_argument("--safety-tolerance", type=int, default=6, choices=range(0, 7),
                       help="安全容忍度 (0-6，6为最宽松，默认: 6)")
    parser.add_argument("--raw", action="store_true", help="生成更自然的图像 (仅ultra模型支持)")
    parser.add_argument("--output", help="输出文件名 (单张图像) 或输出目录 (批量生成)")
    parser.add_argument("--batch", action="store_true", help="批量生成模式")
    parser.add_argument("--max-concurrent", type=int, default=5, help="批量生成时的最大并发数 (默认: 5)")
    parser.add_argument("--input-image", help="输入图像路径 (用于图像编辑或风格迁移)")
    parser.add_argument("--edit", action="store_true", help="图像编辑模式")
    parser.add_argument("--style-transfer", action="store_true", help="风格迁移模式")
    
    args = parser.parse_args()
    
    # 检查提示词
    if not args.prompt:
        parser.error("请提供至少一个图像描述提示词")
    
    # 如果只有一个提示词且不是批量模式，转换为字符串
    if len(args.prompt) == 1 and not args.batch:
        args.prompt = args.prompt[0]
    
    # 获取API密钥
    api_key = args.api_key or os.getenv("BFL_API_KEY")
    if not api_key:
        print("❌ 请设置API密钥:")
        print("   方法1: 使用 --api-key 参数")
        print("   方法2: 设置环境变量 BFL_API_KEY")
        print("   例如: export BFL_API_KEY='your_api_key_here'")
        return
    
    # 创建生成器
    generator = BFLImageGenerator(api_key)
    
    print("=" * 50)
    print("🎨 BFL AI 图像生成器")
    print("=" * 50)
    
    # 准备生成参数
    generation_kwargs = {}
    if args.width is not None:
        generation_kwargs["width"] = args.width
    if args.height is not None:
        generation_kwargs["height"] = args.height
    if args.steps is not None:
        generation_kwargs["steps"] = args.steps
    if args.guidance is not None:
        generation_kwargs["guidance"] = args.guidance
    if args.seed is not None:
        generation_kwargs["seed"] = args.seed
    if args.prompt_upsampling:
        generation_kwargs["prompt_upsampling"] = args.prompt_upsampling
    if args.safety_tolerance != 6:
        generation_kwargs["safety_tolerance"] = args.safety_tolerance
    if args.raw:
        generation_kwargs["raw"] = args.raw
    
    # 判断执行模式
    if args.edit:
        # 图像编辑模式
        if not args.input_image:
            print("❌ 图像编辑模式需要指定 --input-image 参数")
            return
        
        prompt = args.prompt[0] if isinstance(args.prompt, list) else args.prompt
        print(f"✏️  图像编辑模式")
        print(f"🖼️  输入图像: {args.input_image}")
        print(f"📝 编辑指令: {prompt}")
        print(f"🎨 模型: {args.model}")
        
        success = generator.edit_image(
            input_image=args.input_image,
            prompt=prompt,
            model=args.model,
            output_filename=args.output,
            **generation_kwargs
        )
        
        if success:
            print("\n🎉 图像编辑完成!")
        else:
            print("\n❌ 图像编辑失败!")
            
    elif args.style_transfer:
        # 风格迁移模式
        if not args.input_image:
            print("❌ 风格迁移模式需要指定 --input-image 参数")
            return
        
        prompt = args.prompt[0] if isinstance(args.prompt, list) else args.prompt
        print(f"🎨 风格迁移模式")
        print(f"🖼️  参考图像: {args.input_image}")
        print(f"📝 内容描述: {prompt}")
        print(f"🎨 模型: {args.model}")
        
        success = generator.style_transfer(
            reference_image=args.input_image,
            prompt=prompt,
            model=args.model,
            output_filename=args.output,
            **generation_kwargs
        )
        
        if success:
            print("\n🎉 风格迁移完成!")
        else:
            print("\n❌ 风格迁移失败!")
            
    elif args.batch or isinstance(args.prompt, list):
        # 批量生成
        prompts = args.prompt if isinstance(args.prompt, list) else [args.prompt]
        output_dir = args.output or "batch_output"
        
        print(f"📝 批量生成模式: {len(prompts)} 个提示词")
        print(f"🎨 模型: {args.model}")
        print(f"📁 输出目录: {output_dir}")
        
        results = generator.generate_batch_images(
            prompts=prompts,
            model=args.model,
            aspect_ratio=args.aspect_ratio,
            output_dir=output_dir,
            max_concurrent=args.max_concurrent,
            **generation_kwargs
        )
        
        if results["successful"] > 0:
            print(f"\n🎉 批量生成完成! 成功生成 {results['successful']}/{results['total']} 张图像")
        else:
            print(f"\n❌ 批量生成失败! 所有图像生成都失败了")
    else:
        # 单张生成 (可能包含参考图像)
        prompt = args.prompt[0] if isinstance(args.prompt, list) else args.prompt
        print(f"📝 提示词: {prompt}")
        print(f"🎨 模型: {args.model}")
        if args.input_image:
            print(f"🖼️  参考图像: {args.input_image}")
        if args.width and args.height:
            print(f"📐 尺寸: {args.width}x{args.height}")
        else:
            print(f"📐 宽高比: {args.aspect_ratio}")
        
        # 如果有输入图像，添加到生成参数中
        if args.input_image:
            generation_kwargs["image"] = args.input_image
        
        success = generator.generate_image(
            prompt=prompt,
            model=args.model,
            aspect_ratio=args.aspect_ratio,
            output_filename=args.output,
            **generation_kwargs
        )
        
        if success:
            print("\n🎉 图像生成完成!")
        else:
            print("\n❌ 图像生成失败!")


if __name__ == "__main__":
    main() 