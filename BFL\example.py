#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BFL AI 图像生成器使用示例
"""

import os
from bfl_image_generator import BFLImageGenerator


def main():
    """示例主函数"""
    # 设置API密钥 (请替换为你的实际API密钥)
    api_key = 'b915d133-2d74-411c-a531-2dfc56474223'
    if not api_key:
        print("❌ 请设置环境变量 BFL_API_KEY")
        print("例如: export BFL_API_KEY='your_api_key_here'")
        return
    
    # 创建生成器实例
    generator = BFLImageGenerator(api_key)
    
    # 示例1: 基本图像生成
    print("🎨 示例1: 基本图像生成")
    prompt1 = "A beautiful sunset over a mountain lake, with vibrant colors reflecting in the water"
    success1 = generator.generate_image(
        prompt=prompt1,
        model="flux-kontext-pro",
        aspect_ratio="16:9",
        output_filename="sunset_lake.jpg"
    )
    
    if success1:
        print("✅ 示例1完成!")
    else:
        print("❌ 示例1失败!")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2: 图像编辑 (如果第一张图像生成成功)
    if success1 and os.path.exists("sunset_lake.jpg"):
        print("🎨 示例2: 图像编辑")
        edit_success = generator.edit_image(
            input_image="sunset_lake.jpg",
            prompt="Add a small wooden boat floating on the lake",
            model="flux-kontext-pro",
            output_filename="sunset_lake_with_boat.jpg"
        )
        
        if edit_success:
            print("✅ 示例2完成!")
        else:
            print("❌ 示例2失败!")
    else:
        print("⏭️  跳过示例2 (需要示例1的输出图像)")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3: 风格迁移 (如果有输入图像)
    if success1 and os.path.exists("sunset_lake.jpg"):
        print("🎨 示例3: 风格迁移")
        style_success = generator.style_transfer(
            reference_image="sunset_lake.jpg",
            prompt="A cyberpunk cityscape with neon lights and futuristic buildings",
            model="flux-kontext-pro",
            output_filename="cyberpunk_style.jpg"
        )
        
        if style_success:
            print("✅ 示例3完成!")
        else:
            print("❌ 示例3失败!")
    else:
        print("⏭️  跳过示例3 (需要参考图像)")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2: 使用精确尺寸和高级参数
    # print("🎨 示例2: 使用精确尺寸和高级参数")
    # prompt2 = "A majestic dragon flying over ancient mountains, fantasy art style"
    # success2 = generator.generate_image(
    #     prompt=prompt2,
    #     model="flux-pro-1.1-ultra",
    #     width=1024,
    #     height=768,
    #     steps=30,
    #     seed=12345,
    #     prompt_upsampling=True,
    #     output_filename="dragon_mountains.jpg"
    # )
    
    # if success2:
    #     print("✅ 示例2完成!")
    # else:
    #     print("❌ 示例2失败!")
    
    # print("\n" + "="*50 + "\n")
    
    # # 示例3: 批量生成
    # print("🎨 示例3: 批量生成")
    # batch_prompts = [
    #     "A serene lake at sunset with mountains in the background",
    #     "A futuristic cityscape with flying cars and neon lights",
    #     "A magical forest with glowing plants and fairy lights",
    #     "A steampunk airship floating in cloudy skies"
    # ]
    
    # batch_results = generator.generate_batch_images(
    #     prompts=batch_prompts,
    #     model="flux-dev",  # 使用dev模型节省积分
    #     aspect_ratio="16:9",
    #     output_dir="example_batch",
    #     max_concurrent=2,  # 限制并发数
    #     steps=25
    # )
    
    # if batch_results["successful"] > 0:
    #     print("✅ 示例3完成!")
    # else:
    #     print("❌ 示例3失败!")
    
    # print("\n" + "="*50 + "\n")
    
    # # 示例4: 分步骤操作 (展示可重现性)
    # print("🎨 示例4: 可重现的图像生成")
    # prompt4 = "A cute robot playing with colorful balloons in a park"
    
    # # 使用相同种子生成两张相同的图像
    # for i in range(2):
    #     print(f"生成第 {i+1} 张图像...")
    #     request_id = generator.submit_generation_request(
    #         prompt=prompt4,
    #         model="flux-dev",
    #         width=512,
    #         height=512,
    #         seed=98765,  # 固定种子确保结果一致
    #         steps=20
    #     )
        
    #     if request_id:
    #         image_url = generator.poll_for_result(request_id)
    #         if image_url:
    #             success = generator.download_image(image_url, f"robot_balloons_{i+1}.jpg")
    #             if success:
    #                 print(f"✅ 第 {i+1} 张图像生成成功!")
    #             else:
    #                 print(f"❌ 第 {i+1} 张图像下载失败!")
    #         else:
    #             print(f"❌ 第 {i+1} 张图像生成失败!")
    #     else:
    #         print(f"❌ 第 {i+1} 张图像请求失败!")
    
    # print("✅ 示例4完成! (两张图像应该完全相同)")


if __name__ == "__main__":
    main() 