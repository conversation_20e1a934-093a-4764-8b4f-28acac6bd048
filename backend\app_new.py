#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BFL AI 图像生成器 Web 应用 - 重构版本
基于函数式编程思想的模块化架构
"""

import sys
import os

# 添加必要的路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

# 添加项目根目录到路径
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

from flask import Flask, jsonify, render_template, request, send_file
# JWT removed - using simple auth system
from backend.config.app_config import AppConfig
from backend.routes.page_routes import page_bp
from backend.routes.image_routes import image_bp
from backend.routes.translation_routes import translation_bp

def create_app():
    """
    应用工厂函数 - 创建和配置Flask应用
    这是主控函数，组合各个模块来构建完整应用
    """
    # 获取项目根目录路径
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # 创建Flask应用实例，指定前端资源路径
    app = Flask(__name__,
                static_folder=os.path.join(project_root, 'frontend', 'static'),
                template_folder=os.path.join(project_root, 'frontend', 'templates'))

    # 配置应用
    _configure_app(app)

    # 初始化数据库（如果启用）
    _initialize_database(app)

    # 初始化JWT认证
    _initialize_jwt(app)

    # 初始化必要组件
    _initialize_components()

    # 注册蓝图
    _register_blueprints(app)

    # 注册错误处理器
    _register_error_handlers(app)

    # 注册静态文件路由
    _register_file_routes(app)

    return app

def _create_flask_instance():
    """创建Flask实例"""
    return Flask(__name__)

def _configure_app(app):
    """配置Flask应用"""
    # 基础配置
    app.secret_key = AppConfig.SECRET_KEY
    app.config['SECRET_KEY'] = AppConfig.SECRET_KEY
    app.config['DEBUG'] = AppConfig.DEBUG
    
    # Session配置（新的认证系统）
    app.config['SESSION_COOKIE_SECURE'] = False  # 开发环境设为False
    app.config['SESSION_COOKIE_HTTPONLY'] = True  # 防止XSS
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # CSRF保护
    app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24小时（秒）

    # 数据库配置
    if AppConfig.DATABASE_ENABLED:
        app.config['SQLALCHEMY_DATABASE_URI'] = AppConfig.SQLALCHEMY_DATABASE_URI
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = AppConfig.SQLALCHEMY_TRACK_MODIFICATIONS
        app.config['SQLALCHEMY_ENGINE_OPTIONS'] = AppConfig.SQLALCHEMY_ENGINE_OPTIONS

    # JWT配置已移除 - 使用简化认证系统

    # 文件上传配置
    app.config['UPLOAD_FOLDER'] = AppConfig.UPLOAD_FOLDER
    app.config['MAX_CONTENT_LENGTH'] = AppConfig.MAX_CONTENT_LENGTH

    # 其他配置
    app.config['JSON_AS_ASCII'] = False  # 支持中文JSON响应

    # 初始化目录
    os.makedirs(AppConfig.UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(AppConfig.OUTPUT_FOLDER, exist_ok=True)
    os.makedirs(os.path.join(AppConfig.BASE_DIR, 'static'), exist_ok=True)
    os.makedirs(os.path.join(AppConfig.BASE_DIR, 'templates'), exist_ok=True)

    # 调用配置类的初始化方法
    AppConfig.init_directories()


def _initialize_database(app):
    """初始化数据库"""
    if AppConfig.DATABASE_ENABLED:
        try:
            from backend.models.database import db, init_db
            init_db(app)
            print("数据库初始化成功")
        except Exception as e:
            print(f"数据库初始化失败: {e}")


def _initialize_jwt(app):
    """JWT认证已移除 - 使用简化认证系统"""
    print("使用简化认证系统，无需JWT初始化")


def _initialize_components():
    """初始化应用组件"""
    # 初始化服务（导入时自动创建实例）
    from backend.services.image_service import image_service
    
    # 不再需要初始化翻译服务，因为我们已经简化了架构
    # 翻译功能直接在routes/translation_routes.py中处理
    
    # 可以在这里添加其他初始化逻辑
    pass

def _register_blueprints(app):
    """注册所有蓝图路由"""
    # 注册页面路由
    app.register_blueprint(page_bp)

    # 注册图像API路由
    app.register_blueprint(image_bp)

    # 注册翻译API路由
    app.register_blueprint(translation_bp)

    # 注册新的简化认证路由
    if AppConfig.USER_SYSTEM_ENABLED:
        try:
            from backend.routes.simple_auth_routes import auth_bp
            app.register_blueprint(auth_bp)
            print("简化认证路由注册成功")
        except Exception as e:
            print(f"简化认证路由注册失败: {e}")
            
        # 用户路由
        try:
            from backend.routes.user_routes import user_bp
            app.register_blueprint(user_bp)
            print("用户路由注册成功")
        except Exception as e:
            print(f"用户路由注册失败: {e}")

        # 注册管理员路由
        try:
            from backend.routes.admin_routes import admin_bp
            app.register_blueprint(admin_bp)
            print("管理员路由注册成功")
        except Exception as e:
            print(f"管理员路由注册失败: {e}")

    # from routes.file_routes import file_bp
    # app.register_blueprint(file_bp)

def _register_error_handlers(app):
    """注册错误处理器"""
    @app.errorhandler(404)
    def not_found_error(error):
        """处理404错误"""
        # 如果是API请求，返回JSON
        if request.path.startswith('/api/'):
            return jsonify({'error': 'Not Found', 'path': request.path}), 404
        
        # 否则返回HTML错误页面
        return render_template('base.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """处理500错误"""
        if request.path.startswith('/api/'):
            return jsonify({'error': 'Internal Server Error'}), 500
        
        return render_template('base.html'), 500

def _register_file_routes(app):
    """注册文件服务路由"""
    @app.route('/download/<path:filename>')
    def download_file(filename):
        """下载生成的图像"""
        try:
            filename = os.path.basename(filename)
            filepath = AppConfig.get_output_path(filename)
            
            if os.path.exists(filepath):
                return send_file(filepath, as_attachment=True)
            else:
                return "文件不存在", 404
        except Exception as e:
            return f"下载错误: {str(e)}", 500
    
    @app.route('/view/<path:filename>')
    def view_image(filename):
        """查看图像"""
        try:
            filename = os.path.basename(filename)
            filepath = AppConfig.get_output_path(filename)
            
            if os.path.exists(filepath):
                return send_file(filepath)
            else:
                return "文件不存在", 404
        except Exception as e:
            return f"查看错误: {str(e)}", 500
    
    @app.route('/outputs/<filename>')
    def output_file(filename):
        """直接访问outputs文件夹中的图像"""
        try:
            filepath = AppConfig.get_output_path(filename)
            if os.path.exists(filepath):
                return send_file(filepath)
            else:
                return "文件不存在", 404
        except Exception as e:
            return f"查看错误: {str(e)}", 500
    
    @app.route('/uploads/<filename>')
    def uploaded_file(filename):
        """查看上传的图像"""
        try:
            filepath = AppConfig.get_upload_path(filename)
            if os.path.exists(filepath):
                return send_file(filepath)
            else:
                return "文件不存在", 404
        except Exception as e:
            return f"查看错误: {str(e)}", 500

def run_development_server():
    """运行开发服务器"""
    app = create_app()
    print("🚀 启动 Web 应用")
    print("📱 访问地址: http://127.0.0.1:5000")
    app.run(host='127.0.0.1', port=5000, debug=True, threaded=False, use_reloader=False)

if __name__ == '__main__':
    run_development_server()