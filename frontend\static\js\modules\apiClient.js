const apiClient = (() => {

    /**
     * 提交一个任务（如生成、编辑）到后端，并返回一个任务URL。
     * @param {string} endpoint - The API endpoint (e.g., '/api/generate').
     * @param {FormData | object} body - The request body, can be FormData or a JSON object.
     * @returns {Promise<string>} A promise that resolves with the task status URL.
     */
    async function submitTask(endpoint, body) {
        const isFormData = body instanceof FormData;
        
        const fetchOptions = {
            method: 'POST',
            body: isFormData ? body : JSON.stringify(body),
            credentials: 'include', // 包含session cookie
        };

        if (!isFormData) {
            fetchOptions.headers = {
                'Content-Type': 'application/json',
            };
        }

        try {
            const response = await fetch(endpoint, fetchOptions);
            const data = await response.json();

            if (!response.ok || !data.success) {
                throw new Error(data.error || '任务提交失败');
            }

            if (!data.task_id) {
                throw new Error('未在响应中找到 task_id');
            }

            return `/api/status/${data.task_id}`;

        } catch (error) {
            console.error(`提交任务到 ${endpoint} 失败:`, error);
            throw error;
        }
    }

    /**
     * A simple, one-off text translation.
     * @param {string} text - The text to translate.
     * @returns {Promise<string>} A promise that resolves with the translated text.
     */
    async function translateText(text) {
        try {
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({ text }),
            });
            const data = await response.json();
            if (!response.ok || !data.success) {
                throw new Error(data.error || '翻译失败');
            }
            return data.translated_text;
        } catch (error) {
            console.error('翻译文本失败:', error);
            throw error;
        }
    }

    /**
     * Sends text to the polish API.
     * @param {string} text - The text to polish.
     * @returns {Promise<string>} A promise that resolves with the polished text.
     */
    async function polishPrompt(text) {
        try {
            const response = await fetch('/api/translate/polish', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({ text }),
            });
            const data = await response.json();
            if (!response.ok || !data.success) {
                throw new Error(data.error || '润色提示词失败');
            }
            return data.polished_text;
        } catch (error) {
            console.error('润色提示词失败:', error);
            throw error;
        }
    }

    // A reference to the active polling interval, to allow cancellation.
    let pollIntervalId = null; 
    
    /**
     * 轮询任务状态URL，直到任务完成或失败。
     * @param {string} taskUrl - The URL to poll for the task status.
     * @param {function(object): void} [onProgress] - Optional callback for progress updates.
     * @returns {Promise<object>} A promise that resolves with the final task result.
     */
    function pollTaskStatus(taskUrl, onProgress) {
        // Clear any existing interval before starting a new one.
        if (pollIntervalId) {
            clearInterval(pollIntervalId);
        }

        return new Promise((resolve, reject) => {
            pollIntervalId = setInterval(async () => {
                try {
                    const response = await fetch(taskUrl, {
                        credentials: 'include'
                    });
                    if (!response.ok) {
                        // Handle server errors (5xx, etc.) gracefully
                        throw new Error(`服务器错误: ${response.status} ${response.statusText}`);
                    }
                    const data = await response.json();

                    // 后端响应的顶层 success 表示API调用本身是否成功
                    if (!data.success) {
                        clearInterval(pollIntervalId);
                        reject(new Error(data.error || '获取任务状态失败'));
                        return;
                    }
                    
                    const task = data.data;
                    
                    // 调试日志：查看任务数据结构
                    console.log('[DEBUG] 轮询任务状态:', {
                        taskId: task?.task_id,
                        status: task?.status,
                        hasViewUrl: !!task?.view_url,
                        hasDownloadUrl: !!task?.download_url,
                        hasOutputFile: !!task?.output_file,
                        fullTask: task
                    });

                    // 任务本身的内部状态检查
                    if (task.status === 'failed') {
                        clearInterval(pollIntervalId);
                        pollIntervalId = null;
                        // 任务失败，拒绝Promise并传递失败信息
                        reject(new Error(task.message || '任务处理失败'));
                        return;
                    }

                    if (task.status === 'completed') {
                        clearInterval(pollIntervalId);
                        pollIntervalId = null;
                        // 任务成功，解决Promise并传递结果
                        console.log('[DEBUG] 任务完成，返回结果:', task);
                        resolve(task);
                        return;
                    }

                    // 对于所有其他状态 (queued, processing, etc.)，视为进度更新
                    if (onProgress) {
                        onProgress(task);
                    }

                } catch (error) {
                    clearInterval(pollIntervalId);
                    pollIntervalId = null;
                    console.error(`轮询 ${taskUrl} 失败:`, error);
                    reject(error);
                }
            }, 2000); // Poll every 2 seconds
        });
    }

    /**
     * Cancels any active polling.
     */
    function cancelPolling() {
        if (pollIntervalId) {
            clearInterval(pollIntervalId);
            pollIntervalId = null;
        }
    }

    /**
     * 生成图像的函数
     * @param {object} params - 生成参数
     * @returns {Promise<object>} 包含生成结果的Promise
     */
    async function generateImage(params) {
        try {
            const response = await fetch('/api/generate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(params)
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || '生成失败');
            }

            // 后端可能返回 task_id 或 id，均可接受
            const taskId = data.task_id || data.id;
            if (!taskId) {
                throw new Error('服务器未返回任务ID');
            }

            return {
                taskId: taskId,
                status: data.status || 'queued'
            };

        } catch (error) {
            console.error('生成图像失败:', error);
            throw error;
        }
    }

    async function checkTaskStatus(taskId) {
        try {
            const response = await fetch(`/api/status/${taskId}`);
            
            if (response.status === 404) {
                return { status: 'Task not found', progress: 0 };
            }
            if (!response.ok) {
                throw new Error(`服务器错误: ${response.status}`);
            }

            const respJson = await response.json();
            const taskData = respJson.data || respJson; // 兼容 {data:task} 或直接task
            const rawStatus = taskData.status;
            let mappedStatus;
            switch (rawStatus) {
                case 'queued':
                    mappedStatus = 'Pending';
                    break;
                case 'processing':
                    mappedStatus = 'Pending';
                    break;
                case 'completed':
                    mappedStatus = 'Ready';
                    break;
                case 'failed':
                    mappedStatus = 'Error';
                    break;
                default:
                    mappedStatus = rawStatus; // 可能已是官方枚举
            }
            return {
                status: mappedStatus,
                progress: taskData.progress || 0,
                result: taskData.output_file ? { sample: (() => {
                    const fileName = taskData.output_file.split(/[\\/]/).pop();
                    return `/outputs/${fileName}`;
                })() } : null,
                details: { message: taskData.message }
            };
        } catch (error) {
            console.error('检查任务状态失败:', error);
            throw error;
        }
    }

    // 导出公开的函数
    return {
        submitTask,
        pollTaskStatus,
        translateText,
        polishPrompt,
        cancelPolling,
        generateImage,
        checkTaskStatus
    };
})();

export default apiClient;