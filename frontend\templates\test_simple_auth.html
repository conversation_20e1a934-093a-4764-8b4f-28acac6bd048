<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化认证系统测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .logout-btn {
            background-color: #dc3545;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .user-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简化认证系统测试</h1>
        
        <!-- 登录区域 -->
        <div class="section">
            <h3>🔐 用户登录</h3>
            <form id="loginForm">
                <div class="form-group">
                    <label for="identifier">用户名/邮箱/手机号:</label>
                    <input type="text" id="identifier" name="identifier" value="weixun" required>
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" value="123456" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="remember_me" name="remember_me"> 记住我
                    </label>
                </div>
                <button type="submit">登录</button>
            </form>
            <div id="loginStatus"></div>
        </div>
        
        <!-- 认证状态区域 -->
        <div class="section">
            <h3>📊 认证状态</h3>
            <button onclick="checkAuthStatus()">检查认证状态</button>
            <button onclick="getUserProfile()">获取用户信息</button>
            <div id="authStatus"></div>
        </div>
        
        <!-- 登出区域 -->
        <div class="section">
            <h3>🚪 用户登出</h3>
            <button class="logout-btn" onclick="logout()">登出</button>
            <div id="logoutStatus"></div>
        </div>
        
        <!-- 系统信息 -->
        <div class="section">
            <h3>ℹ️ 系统信息</h3>
            <p><strong>测试时间:</strong> <span id="currentTime"></span></p>
            <p><strong>认证方式:</strong> Session-based (httpOnly cookie)</p>
            <p><strong>API端点:</strong></p>
            <ul>
                <li>POST /api/auth/login - 用户登录</li>
                <li>POST /api/auth/logout - 用户登出</li>
                <li>GET /api/auth/status - 认证状态检查</li>
            </ul>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 显示用户信息
        function showUserInfo(elementId, userData) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="user-info">
                    <h4>用户信息:</h4>
                    <pre>${JSON.stringify(userData, null, 2)}</pre>
                </div>
            `;
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                identifier: formData.get('identifier'),
                password: formData.get('password'),
                remember_me: formData.get('remember_me') === 'on'
            };
            
            try {
                showStatus('loginStatus', '正在登录...', 'info');
                
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('loginStatus', `登录成功: ${result.message}`, 'success');
                    if (result.data && result.data.user) {
                        showUserInfo('loginStatus', result.data.user);
                    }
                } else {
                    showStatus('loginStatus', `登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `登录异常: ${error.message}`, 'error');
            }
        });

        // 检查认证状态
        async function checkAuthStatus() {
            try {
                showStatus('authStatus', '正在检查认证状态...', 'info');
                
                const response = await fetch('/api/auth/status');
                const result = await response.json();
                
                if (result.success) {
                    showStatus('authStatus', `认证状态: ${result.message}`, 'success');
                    if (result.data && result.data.user) {
                        showUserInfo('authStatus', result.data.user);
                    }
                } else {
                    showStatus('authStatus', `认证状态: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('authStatus', `状态检查异常: ${error.message}`, 'error');
            }
        }

        // 获取用户信息
        async function getUserProfile() {
            try {
                showStatus('authStatus', '正在获取用户信息...', 'info');
                
                const response = await fetch('/api/user/profile');
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showStatus('authStatus', '用户信息获取成功', 'success');
                    showUserInfo('authStatus', result.data);
                } else {
                    showStatus('authStatus', `获取用户信息失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus('authStatus', `获取用户信息异常: ${error.message}`, 'error');
            }
        }

        // 用户登出
        async function logout() {
            try {
                showStatus('logoutStatus', '正在登出...', 'info');
                
                const response = await fetch('/api/auth/logout', {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('logoutStatus', `登出成功: ${result.message}`, 'success');
                    // 清空其他状态显示
                    document.getElementById('authStatus').innerHTML = '';
                    document.getElementById('loginStatus').innerHTML = '';
                } else {
                    showStatus('logoutStatus', `登出失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus('logoutStatus', `登出异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查认证状态
        window.addEventListener('load', function() {
            setTimeout(checkAuthStatus, 1000);
        });
    </script>
</body>
</html>