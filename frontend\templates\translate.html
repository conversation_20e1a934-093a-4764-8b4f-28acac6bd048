{% extends "base.html" %}

{% block title %}智能提示词处理{% endblock %}

{% block extra_css %}
<style>
/* 翻译页面专用样式 */
body.theme-translate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 流程步骤卡片样式 */
.process-step {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.process-step:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.step-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 16px 16px 0 0;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    margin-right: 1rem;
}

.step-1 .step-number { background: linear-gradient(135deg, #06b6d4, #0891b2); }
.step-2 .step-number { background: linear-gradient(135deg, #10b981, #059669); }
.step-3 .step-number { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: #1e293b;
}

.step-subtitle {
    font-size: 0.9rem;
    color: #64748b;
    margin: 0;
}

.step-content {
    padding: 2rem;
}

/* 文本框样式 */
.step-textarea {
    width: 100%;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.step-textarea:focus {
    outline: none;
    border-color: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
    background: white;
}

.step-textarea.polished {
    border-color: #10b981;
}

.step-textarea.translated {
    border-color: #8b5cf6;
}

/* 按钮样式 */
.step-btn {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid;
    min-width: 120px;
}

.step-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-polish {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #10b981;
    color: white;
}

.btn-polish:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.btn-translate {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border-color: #8b5cf6;
    color: white;
}

.btn-translate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.3);
}

.btn-generate {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-color: #f59e0b;
    color: white;
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

/* 状态显示 */
.service-status {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 流程指示器 */
.process-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
}

.indicator-step {
    display: flex;
    align-items: center;
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;
}

.indicator-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e2e8f0;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-weight: 600;
}

.indicator-step.active .indicator-number {
    background: #06b6d4;
    color: white;
}

.indicator-step.completed .indicator-number {
    background: #10b981;
    color: white;
}

.indicator-arrow {
    color: #cbd5e1;
    font-size: 1.2rem;
}

/* 响应式 */
@media (max-width: 768px) {
    .step-content {
        padding: 1.5rem;
    }
    
    .process-indicator {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .indicator-arrow {
        transform: rotate(90deg);
    }
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    z-index: 10;
}

.step-content {
    position: relative;
}

/* 成功状态 */
.step-success {
    border-left: 4px solid #10b981;
}

.step-error {
    border-left: 4px solid #ef4444;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    let currentStep = 1;
    
    // 检测语言函数
    function detectLanguage(text) {
        const chineseRegex = /[\u4e00-\u9fff]/;
        return chineseRegex.test(text) ? 'zh' : 'en';
    }

    // 显示加载状态
    function showLoading(stepNumber, message) {
        const stepContent = $(`.step-${stepNumber} .step-content`);
        stepContent.append(`
            <div class="loading-overlay">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-2" role="status">
                        <span class="visually-hidden">处理中...</span>
                    </div>
                    <div class="text-muted">${message}</div>
                </div>
            </div>
        `);
    }

    // 隐藏加载状态
    function hideLoading(stepNumber) {
        $(`.step-${stepNumber} .loading-overlay`).remove();
    }

    // 显示错误信息
    function showError(stepNumber, message) {
        const stepElement = $(`.step-${stepNumber}`);
        stepElement.addClass('step-error');
        setTimeout(() => stepElement.removeClass('step-error'), 5000);
        
        // 显示错误提示
        const errorAlert = `
            <div class="alert alert-danger alert-dismissible fade show mt-2" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $(`.step-${stepNumber} .step-content`).append(errorAlert);
    }

    // 显示成功状态
    function showSuccess(stepNumber) {
        const stepElement = $(`.step-${stepNumber}`);
        stepElement.addClass('step-success');
        setTimeout(() => stepElement.removeClass('step-success'), 3000);
        
        // 更新流程指示器
        updateProcessIndicator(stepNumber);
    }

    // 更新流程指示器
    function updateProcessIndicator(completedStep) {
        for (let i = 1; i <= completedStep; i++) {
            $(`.indicator-step-${i}`).addClass('completed').removeClass('active');
        }
        if (completedStep < 3) {
            $(`.indicator-step-${completedStep + 1}`).addClass('active');
        }
    }

    // 更新按钮状态
    function updateButtonStates() {
        const inputText = $('#input-text').val().trim();
        const polishText = $('#polish-text').val().trim();
        
        // 润色按钮：需要有输入文本
        $('#polish-btn').prop('disabled', !inputText);
        
        // 翻译按钮：需要有润色文本
        $('#translate-btn').prop('disabled', !polishText);
        
        // 绘图按钮：需要有翻译文本或润色文本
        const translateText = $('#translate-text').val().trim();
        $('#generate-btn').prop('disabled', !translateText && !polishText);
    }

    // 输入文本变化监听
    $('#input-text').on('input', function() {
        updateButtonStates();
        // 清空后续步骤
        if (!$(this).val().trim()) {
            $('#polish-text').val('');
            $('#translate-text').val('');
        }
    });

    $('#polish-text').on('input', updateButtonStates);
    $('#translate-text').on('input', updateButtonStates);

    // 润色按钮点击
    $('#polish-btn').click(function() {
        const inputText = $('#input-text').val().trim();
        if (!inputText) {
            alert('请先输入要润色的文字！');
            return;
        }

        showLoading(2, '正在为您的描述添加艺术细节...');
        $(this).prop('disabled', true);

        $.ajax({
            url: '/api/translate/polish',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ text: inputText }),
            success: function(response) {
                hideLoading(2);
                if (response.success) {
                    $('#polish-text').val(response.polished_text);
                    showSuccess(2);
                    updateButtonStates();
                } else {
                    showError(2, '润色失败：' + (response.error || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                hideLoading(2);
                showError(2, '润色失败：网络错误或服务不可用');
            },
            complete: function() {
                $('#polish-btn').prop('disabled', false);
                updateButtonStates();
            }
        });
    });

    // 翻译按钮点击
    $('#translate-btn').click(function() {
        const polishText = $('#polish-text').val().trim();
        if (!polishText) {
            alert('请先完成润色步骤！');
            return;
        }

        const lang = detectLanguage(polishText);
        if (lang === 'en') {
            $('#translate-text').val(polishText);
            showSuccess(3);
            updateButtonStates();
            return;
        }

        showLoading(3, '正在翻译为英文...');
        $(this).prop('disabled', true);

        $.ajax({
            url: '/api/translate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ text: polishText }),
            success: function(response) {
                hideLoading(3);
                if (response.success) {
                    $('#translate-text').val(response.translated_text);
                    showSuccess(3);
                    updateButtonStates();
                } else {
                    showError(3, '翻译失败：' + (response.error || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                hideLoading(3);
                showError(3, '翻译失败：网络错误或服务不可用');
            },
            complete: function() {
                $('#translate-btn').prop('disabled', false);
                updateButtonStates();
            }
        });
    });

    // 绘图按钮点击
    $('#generate-btn').click(function() {
        const translateText = $('#translate-text').val().trim();
        const polishText = $('#polish-text').val().trim();
        
        const finalText = translateText || polishText;
        if (!finalText) {
            alert('请先完成前面的步骤！');
            return;
        }

        const generateUrl = '/generate?prompt=' + encodeURIComponent(finalText);
        window.location.href = generateUrl;
    });

    // 检查服务状态
    function checkServiceStatus() {
        $.ajax({
            url: '/api/translate/status',
            method: 'GET',
            timeout: 10000,
            success: function(response) {
                const statusElement = $('#service-status');
                
                if (response.success && response.manager_status === 'running') {
                    if (response.services && response.services.length > 0) {
                        // 显示主服务状态
                        const primaryService = response.primary_service;
                        const primaryServiceInfo = response.services.find(s => s.is_primary);
                        
                        const serviceName = primaryService === 'ollama' ? 'Ollama' : 
                                          primaryService === 'deepseek' ? 'DeepSeek' : '未知';
                        
                        if (primaryServiceInfo && primaryServiceInfo.status === 'available') {
                            // 主服务可用
                            statusElement.removeClass('bg-secondary bg-danger bg-warning')
                                       .addClass('bg-success')
                                       .html(`<i class="fas fa-check-circle me-2"></i>服务正常 - ${serviceName} (主服务)`);
                        } else {
                            // 主服务不可用，检查备用服务
                            const backupService = response.backup_service;
                            const backupServiceInfo = response.services.find(s => !s.is_primary);
                            const backupServiceName = backupService === 'ollama' ? 'Ollama' : 
                                                    backupService === 'deepseek' ? 'DeepSeek' : '未知';
                            
                            if (backupServiceInfo && backupServiceInfo.status === 'available') {
                                statusElement.removeClass('bg-secondary bg-danger bg-success')
                                           .addClass('bg-warning')
                                           .html(`<i class="fas fa-exclamation-triangle me-2"></i>使用备用服务 - ${backupServiceName}`);
                            } else {
                                statusElement.removeClass('bg-secondary bg-success bg-warning')
                                           .addClass('bg-danger')
                                           .html('<i class="fas fa-times-circle me-2"></i>所有服务不可用');
                            }
                        }
                    } else {
                        // 服务管理器运行中，但没有可用服务
                        statusElement.removeClass('bg-secondary bg-success bg-danger')
                                   .addClass('bg-warning')
                                   .html('<i class="fas fa-exclamation-triangle me-2"></i>服务初始化中...');
                    }
                } else {
                    // 服务管理器未运行
                    statusElement.removeClass('bg-secondary bg-success bg-warning')
                               .addClass('bg-danger')
                               .html('<i class="fas fa-times-circle me-2"></i>服务未启动');
                }
            },
            error: function(xhr, status, error) {
                const statusElement = $('#service-status');
                if (status === 'timeout') {
                    statusElement.removeClass('bg-secondary bg-success')
                               .addClass('bg-warning')
                               .html('<i class="fas fa-clock me-2"></i>响应超时');
                } else {
                    statusElement.removeClass('bg-secondary bg-success')
                               .addClass('bg-danger')
                               .html('<i class="fas fa-times-circle me-2"></i>连接失败');
                }
            }
        });
    }

    // 初始化
    updateButtonStates();
    checkServiceStatus();
    
    // 定期检查状态
    setInterval(checkServiceStatus, 60000);
});
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 页面标题 -->
    <div class="text-center mb-4">
        <h1 class="text-white mb-2">
            <i class="fas fa-magic me-2"></i>智能提示词处理工具
        </h1>
        <p class="text-white-50">三步流程：输入描述 → AI润色 → 智能翻译</p>
    </div>

    <!-- 服务状态 -->
    <div class="service-status text-center">
        <span id="service-status" class="badge bg-secondary fs-6">
            <i class="fas fa-spinner fa-spin me-2"></i>检查服务状态...
        </span>
    </div>

    <!-- 流程指示器 -->
    <div class="process-indicator">
        <div class="indicator-step indicator-step-1 active">
            <div class="indicator-number">1</div>
            <span>输入描述</span>
        </div>
        <i class="fas fa-arrow-right indicator-arrow"></i>
        <div class="indicator-step indicator-step-2">
            <div class="indicator-number">2</div>
            <span>AI润色</span>
        </div>
        <i class="fas fa-arrow-right indicator-arrow"></i>
        <div class="indicator-step indicator-step-3">
            <div class="indicator-number">3</div>
            <span>智能翻译</span>
        </div>
    </div>

    <!-- 第一步：输入描述 -->
    <div class="process-step step-1">
        <div class="step-header">
            <div class="d-flex align-items-center">
                <div class="step-number">1</div>
                <div>
                    <h3 class="step-title">输入原始描述</h3>
                    <p class="step-subtitle">请输入您想要描述的画面内容</p>
                </div>
            </div>
        </div>
        <div class="step-content">
            <textarea 
                id="input-text" 
                class="step-textarea" 
                rows="6" 
                placeholder="请输入您想要生成图像的描述...&#10;&#10;例如：&#10;• 一只可爱的小猫坐在窗台上&#10;• 夕阳下的古老城堡&#10;• 科幻风格的未来城市"></textarea>
            <div class="text-end mt-3">
                <small class="text-muted">
                    <i class="fas fa-lightbulb me-1"></i>
                    支持中文或英文输入，简单描述即可
                </small>
            </div>
        </div>
    </div>

    <!-- 第二步：AI润色 -->
    <div class="process-step step-2">
        <div class="step-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="step-number">2</div>
                    <div>
                        <h3 class="step-title">AI智能润色</h3>
                        <p class="step-subtitle">为您的描述添加专业的艺术细节</p>
                    </div>
                </div>
                <button id="polish-btn" class="btn step-btn btn-polish" disabled>
                    <i class="fas fa-sparkles me-2"></i>开始润色
                </button>
            </div>
        </div>
        <div class="step-content">
            <textarea 
                id="polish-text" 
                class="step-textarea polished" 
                rows="8" 
                placeholder="润色后的内容将显示在这里...&#10;&#10;AI会为您添加：&#10;• 光线和色彩描述&#10;• 构图和视角细节&#10;• 艺术风格和技法&#10;• 材质和纹理描述"></textarea>
            <div class="text-end mt-3">
                <small class="text-muted">
                    <i class="fas fa-edit me-1"></i>
                    润色结果可以手动编辑调整
                </small>
            </div>
        </div>
    </div>

    <!-- 第三步：智能翻译 -->
    <div class="process-step step-3">
        <div class="step-header">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="step-number">3</div>
                    <div>
                        <h3 class="step-title">智能翻译</h3>
                        <p class="step-subtitle">将中文内容翻译为专业英文提示词</p>
                    </div>
                </div>
                <div>
                    <button id="translate-btn" class="btn step-btn btn-translate me-2" disabled>
                        <i class="fas fa-language me-2"></i>翻译
                    </button>
                    <button id="generate-btn" class="btn step-btn btn-generate" disabled>
                        <i class="fas fa-image me-2"></i>生成图像
                    </button>
                </div>
            </div>
        </div>
        <div class="step-content">
            <textarea 
                id="translate-text" 
                class="step-textarea translated" 
                rows="8" 
                placeholder="翻译后的英文提示词将显示在这里...&#10;&#10;特点：&#10;• 保持原意准确性&#10;• 使用专业艺术术语&#10;• 适合AI图像生成&#10;• 符合国际标准格式"></textarea>
            <div class="text-end mt-3">
                <small class="text-muted">
                    <i class="fas fa-check me-1"></i>
                    最终结果，可直接用于图像生成
                </small>
            </div>
        </div>
    </div>

    <!-- 使用提示 -->
    <div class="text-center mt-4 mb-4">
        <div class="bg-white bg-opacity-10 rounded p-3 text-white">
            <h6><i class="fas fa-info-circle me-2"></i>使用提示</h6>
            <div class="row">
                <div class="col-md-4">
                    <small>
                        <strong>第一步：</strong>输入您的创意描述，可以是简单的中文或英文
                    </small>
                </div>
                <div class="col-md-4">
                    <small>
                        <strong>第二步：</strong>AI会为您添加专业的艺术细节，提升绘图效果
                    </small>
                </div>
                <div class="col-md-4">
                    <small>
                        <strong>第三步：</strong>智能翻译为英文，然后可以直接生成图像
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
