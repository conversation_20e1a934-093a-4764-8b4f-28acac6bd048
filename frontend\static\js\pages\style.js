import FileUploader from '../modules/fileUploader.js';
import TaskProcessor from '../modules/taskProcessor.js';
import apiClient from '../modules/apiClient.js';
import uiManager from '../modules/uiManager.js';

document.addEventListener('DOMContentLoaded', () => {
    // 1. 获取关键DOM元素
    const styleForm = document.getElementById('styleForm');
    const resetBtn = document.getElementById('resetBtn');
    const translateBtn = document.getElementById('translateBtn');
    const useTranslatedBtn = document.getElementById('useTranslatedBtn');
    const hideTranslatedBtn = document.getElementById('hideTranslatedBtn');
    const promptEl = document.getElementById('prompt');
    const translatedPromptEl = document.getElementById('translatedPrompt');
    const translatedPromptArea = document.getElementById('translatedPromptArea');
    const translateStatus = document.getElementById('translateStatus');

    // 2. 初始化文件上传器
    const uploader = new FileUploader('#uploadArea', 'image');

    // 3. 初始化任务处理器
    const taskProcessor = new TaskProcessor({
        form: styleForm,
        uploader: uploader,
        submitBtnSelector: '#styleBtn',
        statusSelector: '#statusArea',
        resultSelector: '#resultArea'
    });

    // 4. 绑定其他UI事件
    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            taskProcessor.reset();
            translatedPromptArea.style.display = 'none';
        });
    }

    // 翻译功能
    if (translateBtn) {
        translateBtn.addEventListener('click', translatePrompt);
    }
    
    if (useTranslatedBtn) {
        useTranslatedBtn.addEventListener('click', () => {
            if (translatedPromptEl.value.trim()) {
                promptEl.value = translatedPromptEl.value.trim();
                translatedPromptArea.style.display = 'none';
            }
        });
    }

    if (hideTranslatedBtn) {
        hideTranslatedBtn.addEventListener('click', () => {
            translatedPromptArea.style.display = 'none';
        });
    }

    async function translatePrompt() {
        const textToTranslate = promptEl.value.trim();
        if (!textToTranslate) {
            alert('请输入需要翻译的描述。');
            return;
        }

        uiManager.showLoadingState(translateBtn, "翻译中...");
        translateStatus.style.display = 'inline';
        translateStatus.textContent = '正在翻译...';
        translateStatus.className = 'ms-2 text-muted';

        try {
            const translatedText = await apiClient.translateText(textToTranslate);
            translatedPromptEl.value = translatedText;
            translatedPromptArea.style.display = 'block';
            translateStatus.textContent = '翻译完成';
            translateStatus.className = 'ms-2 text-success';
            setTimeout(() => {
                translateStatus.style.display = 'none';
            }, 3000);
        } catch (error) {
            uiManager.showError(translateStatus, `翻译失败: ${error.message}`);
            translateStatus.className = 'ms-2 text-danger';
        } finally {
            uiManager.hideLoadingState(translateBtn);
        }
    }

    // 风格迁移主函数
    function styleTransfer() {
        const imageFile = uploader.getFile();
        
        if (!imageFile) {
            alert('请选择参考图像');
            return;
        }
        
        if (!promptEl.value.trim()) {
            alert('请输入内容描述');
            return;
        }
        
        // 获取当前提示词
        let currentPrompt = promptEl.value.trim();
        const translatedPrompt = translatedPromptEl.value;
        
        // 如果有手动翻译结果且正在显示，优先使用翻译结果
        if (translatedPrompt && translatedPrompt.trim() && translatedPromptArea.style.display === 'block') {
            currentPrompt = translatedPrompt;
        }
        
        // 自动检测语言并处理
        const isEnglish = detectLanguage(currentPrompt) === 'english';
        
        if (isEnglish) {
            // 英文直接风格迁移
            console.log('检测到英文描述，直接开始风格迁移...');
            processStyleRequest(currentPrompt, imageFile);
        } else {
            // 中文需要先翻译
            console.log('检测到中文描述，正在自动翻译...');
            showStyleStatus('检测到中文描述，正在自动翻译...', 'info');
            $('#styleBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 翻译中...');
            
            translateAndStyle(currentPrompt, imageFile);
        }
    }

    // 语言检测函数
    function detectLanguage(text) {
        const chineseRegex = /[\u4e00-\u9fff]/;
        if (chineseRegex.test(text)) {
            return 'chinese';
        }
        return 'english';
    }

    // 翻译并风格迁移
    function translateAndStyle(textToTranslate, imageFile) {
        $.ajax({
            url: '/api/translate',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                text: textToTranslate
            }),
            success: function(response) {
                if (response.success) {
                    const translatedText = response.translated_text;
                    
                    // 显示翻译状态
                    showStyleStatus(`🌐 已翻译为英文: "${translatedText.substring(0, 50)}..."`, 'success');
                    
                    // 开始风格迁移
                    setTimeout(() => {
                        processStyleRequest(translatedText, imageFile);
                    }, 800);
                    
                } else {
                    // 翻译失败，使用原始文本
                    showStyleStatus('⚠️ 自动翻译失败，使用原始描述: ' + (response.error || '未知错误'), 'warning');
                    setTimeout(() => {
                        processStyleRequest(textToTranslate, imageFile);
                    }, 1000);
                }
            },
            error: function(xhr) {
                // 翻译失败，使用原始文本
                const error = xhr.responseJSON ? xhr.responseJSON.error : '自动翻译失败';
                showStyleStatus('⚠️ 自动翻译失败，使用原始描述: ' + error, 'warning');
                setTimeout(() => {
                    processStyleRequest(textToTranslate, imageFile);
                }, 1000);
            }
        });
    }

    // 处理风格迁移请求
    function processStyleRequest(promptToUse, imageFile) {
        // 构建表单数据对象（用于测试模式显示）
        const formDataObject = {
            prompt: promptToUse,
            model: $('#model').val(),
            steps: $('#steps').val() || null,
            seed: $('#seed').val() || null,
            guidance: $('#guidance').val() || null,
            image_name: imageFile.name,
            image_size: formatFileSize(imageFile.size),
            image_type: imageFile.type
        };
        
        // 检查是否为测试模式
        if ($('#testMode').is(':checked')) {
            // 测试模式：显示API请求内容而不实际发送
            showStyleTestModeResult(formDataObject, promptToUse, imageFile);
            return;
        }
        
        // 实际发送请求
        const formData = new FormData();
        formData.append('image', imageFile, imageFile.name);
        formData.append('prompt', promptToUse);
        formData.append('model', $('#model').val());
        
        if ($('#steps').val()) {
            formData.append('steps', $('#steps').val());
        }
        if ($('#seed').val()) {
            formData.append('seed', $('#seed').val());
        }
        if ($('#guidance').val()) {
            formData.append('guidance', $('#guidance').val());
        }
        
        // 禁用提交按钮
        $('#styleBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 风格迁移中...');
        showStyleStatus('正在上传图像并处理...', 'info');
        
        // 发送请求
        $.ajax({
            url: '/api/style',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                currentTaskId = response.task_id;
                showStyleStatus('任务已提交，正在风格迁移...', 'info');
                startStyleStatusCheck();
            },
            error: function(xhr) {
                const error = xhr.responseJSON ? xhr.responseJSON.error : '请求失败';
                showStyleStatus('风格迁移失败: ' + error, 'danger');
                $('#styleBtn').prop('disabled', false).html('<i class="fas fa-palette"></i> 开始风格迁移');
            }
        });
    }

    // 开始状态检查
    function startStyleStatusCheck() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
        
        statusCheckInterval = setInterval(function() {
            if (currentTaskId) {
                checkTaskStatus(currentTaskId);
            }
        }, 2000);
    }

    // 检查任务状态
    function checkTaskStatus(taskId) {
        $.get('/api/status/' + taskId)
            .done(function(task) {
                showStatus(task.status, task.message, task);
                
                if (task.status === 'completed' || task.status === 'failed') {
                    clearInterval(statusCheckInterval);
                    $('#styleBtn').prop('disabled', false).html('<i class="fas fa-palette"></i> 开始风格迁移');
                    currentTaskId = null;
                }
            })
            .fail(function() {
                showStatus('failed', '状态检查失败');
                clearInterval(statusCheckInterval);
                $('#styleBtn').prop('disabled', false).html('<i class="fas fa-palette"></i> 开始风格迁移');
            });
    }

    // 显示状态
    function showStatus(status, message, task = null) {
        let statusClass = 'status-' + status;
        let statusIcon = getStatusIcon(status);
        let statusText = getStatusText(status);
        
        let html = `
            <div class="status-card card border-0 mb-3">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${statusIcon} me-2"></i>
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </div>
                    <p class="mb-0">${message}</p>
                </div>
            </div>
        `;
        
        if (task && task.status === 'completed' && task.view_url) {
            html += `
                <div class="text-center mb-3">
                    <img src="${task.view_url}" class="result-image" alt="风格迁移后的图像">
                </div>
                <div class="d-grid gap-2">
                    <a href="${task.view_url}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye"></i> 查看大图
                    </a>
                    <a href="${task.download_url}" class="btn btn-primary">
                        <i class="fas fa-download"></i> 下载图像
                    </a>
                </div>
            `;
        } else if (status === 'processing') {
            html += `
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 100%"></div>
                </div>
            `;
        }
        
        $('#statusArea').html(html);
    }

    // 显示风格迁移状态
    function showStyleStatus(message, type = 'info') {
        let iconClass = type === 'info' ? 'fas fa-info-circle' : 
                       type === 'success' ? 'fas fa-check-circle' :
                       type === 'warning' ? 'fas fa-exclamation-triangle' :
                       'fas fa-times-circle';
        
        const html = `
            <div class="alert alert-${type} d-flex align-items-center" role="alert">
                <i class="${iconClass} me-2"></i>
                <span>${message}</span>
            </div>
        `;
        
        $('#statusArea').html(html);
    }

    // 获取状态图标
    function getStatusIcon(status) {
        switch(status) {
            case 'queued': return 'fas fa-clock text-warning';
            case 'processing': return 'fas fa-spinner fa-spin text-info';
            case 'completed': return 'fas fa-check-circle text-success';
            case 'failed': return 'fas fa-times-circle text-danger';
            default: return 'fas fa-question-circle';
        }
    }

    // 获取状态文本
    function getStatusText(status) {
        switch(status) {
            case 'queued': return '排队中';
            case 'processing': return '处理中';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            default: return status;
        }
    }

    // 使用示例提示词
    window.useExample = function(element) {
        const text = element.textContent;
        const prompt = text.split(': ')[1];
        promptEl.value = prompt;
        
        // 添加视觉反馈
        $(element).addClass('bg-warning text-dark');
        setTimeout(function() {
            $(element).removeClass('bg-warning text-dark');
        }, 500);
    };

    // 重置表单
    window.resetForm = function() {
        styleForm.reset();
        uploader.reset();
        $('#statusArea').html(`
            <div class="text-center text-muted">
                <i class="fas fa-palette fa-3x mb-3"></i>
                <p>上传参考图像并描述内容来开始风格迁移</p>
            </div>
        `);
        
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
        currentTaskId = null;
        $('#styleBtn').prop('disabled', false).html('<i class="fas fa-palette"></i> 开始风格迁移');
    };

    // 文件大小格式化
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 测试模式相关函数
    function showStyleTestModeResult(formDataObject, finalPrompt, imageFile) {
        const originalPrompt = promptEl.value;
        const translatedPrompt = translatedPromptEl.value;
        const hasTranslation = translatedPrompt && translatedPrompt.trim() && translatedPromptArea.style.display === 'block';
        
        // 构建翻译信息
        const translationInfo = hasTranslation ? 
            `原始提示词 (中文): ${originalPrompt}<br/>翻译后提示词 (英文): ${translatedPrompt}<br/>使用: 翻译后的英文提示词` : 
            `提示词: ${originalPrompt}<br/>语言检测: 英文或无需翻译<br/>使用: 原始提示词`;
        
        // 构建显示内容
        const testResultHtml = `
            <div class="status-card card border-0 mb-3">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-bug me-2 text-warning"></i>
                        <span class="badge bg-warning text-dark">测试模式</span>
                    </div>
                    
                    <h6 class="text-primary mb-2"><i class="fas fa-edit me-1"></i> 原始输入内容：</h6>
                    <div class="bg-light p-3 rounded mb-3" style="font-family: monospace;">
                        ${escapeHtml(originalPrompt)}
                    </div>
                    
                    <h6 class="text-info mb-2"><i class="fas fa-language me-1"></i> 翻译处理信息：</h6>
                    <div class="bg-light p-2 rounded mb-3">
                        ${translationInfo}
                    </div>
                    
                    <h6 class="text-success mb-2"><i class="fas fa-paper-plane me-1"></i> 最终发送给API的提示词：</h6>
                    <div class="bg-light p-3 rounded mb-3" style="font-family: monospace; white-space: pre-wrap;">
                        ${escapeHtml(finalPrompt)}
                    </div>
                    
                    <h6 class="text-warning mb-2"><i class="fas fa-image me-1"></i> 参考图像文件信息：</h6>
                    <div class="bg-light p-2 rounded mb-3">
                        文件名: ${formDataObject.image_name}<br/>
                        文件大小: ${formDataObject.image_size}<br/>
                        文件类型: ${formDataObject.image_type}
                    </div>
                    
                    <h6 class="text-secondary mb-2"><i class="fas fa-cogs me-1"></i> 完整API请求参数：</h6>
                    <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; font-size: 0.85em;">
                        <pre>${JSON.stringify(formDataObject, null, 2)}</pre>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <button class="btn btn-primary" onclick="exitStyleTestMode()">
                    <i class="fas fa-play"></i> 退出测试模式并实际风格迁移
                </button>
                <button class="btn btn-outline-secondary" onclick="resetForm()">
                    <i class="fas fa-undo"></i> 重置表单
                </button>
            </div>
        `;
        
        $('#statusArea').html(testResultHtml);
    }

    // 退出测试模式并实际风格迁移
    window.exitStyleTestMode = function() {
        $('#testMode').prop('checked', false);
        styleTransfer();
    };

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}); 