{% extends "base.html" %}

{% block title %}前端功能测试 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header text-center bg-info text-white">
                    <h4><i class="fas fa-vial"></i> 前端功能测试</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> 测试说明</h6>
                        <p>此页面用于测试前端JavaScript功能，包括用户注册、登录、API调用等。</p>
                        <p>请打开浏览器开发者工具的控制台查看测试结果。</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>手动测试按钮</h6>
                            <button class="btn btn-primary mb-2" onclick="testFunctions.testRegistrationAPI()">
                                <i class="fas fa-user-plus"></i> 测试注册API
                            </button><br>
                            <button class="btn btn-success mb-2" onclick="testManualLogin()">
                                <i class="fas fa-sign-in-alt"></i> 测试登录API
                            </button><br>
                            <button class="btn btn-info mb-2" onclick="testFunctions.testGetProfile()">
                                <i class="fas fa-user"></i> 测试获取资料
                            </button><br>
                            <button class="btn btn-warning mb-2" onclick="clearStorage()">
                                <i class="fas fa-trash"></i> 清除存储
                            </button>
                        </div>
                        <div class="col-md-6">
                            <h6>管理员登录测试</h6>
                            <div class="mb-3">
                                <label class="form-label">管理员邮箱</label>
                                <input type="email" class="form-control" id="adminEmail" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">管理员密码</label>
                                <input type="password" class="form-control" id="adminPassword" value="admin123456">
                            </div>
                            <button class="btn btn-danger mb-2" onclick="testAdminLogin()">
                                <i class="fas fa-crown"></i> 测试管理员登录
                            </button><br>

                            <h6>快速注册测试</h6>
                            <div class="mb-2">
                                <input type="text" class="form-control form-control-sm" id="testUsername" placeholder="用户名" value="testuser123">
                            </div>
                            <div class="mb-2">
                                <input type="email" class="form-control form-control-sm" id="testEmail" placeholder="邮箱" value="<EMAIL>">
                            </div>
                            <div class="mb-2">
                                <input type="password" class="form-control form-control-sm" id="testPassword" placeholder="密码" value="test123456">
                            </div>
                            <button class="btn btn-success btn-sm" onclick="testQuickRegister()">
                                <i class="fas fa-user-plus"></i> 快速注册测试
                            </button>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6>测试结果</h6>
                            <div id="testResults" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                                <p class="text-muted">测试结果将显示在这里...</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <strong>提示：</strong> 
                            <a href="/register" target="_blank">打开注册页面</a> | 
                            <a href="/login" target="_blank">打开登录页面</a> | 
                            <a href="/dashboard" target="_blank">打开仪表板</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/test-registration.js') }}"></script>
<script>
// 重写console.log来显示在页面上
const originalLog = console.log;
const originalError = console.error;

function addToResults(message, type = 'info') {
    const results = document.getElementById('testResults');
    const timestamp = new Date().toLocaleTimeString();
    const colorClass = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
    
    results.innerHTML += `<div class="${colorClass}"><small>[${timestamp}]</small> ${message}</div>`;
    results.scrollTop = results.scrollHeight;
}

console.log = function(...args) {
    originalLog.apply(console, args);
    addToResults(args.join(' '), 'info');
};

console.error = function(...args) {
    originalError.apply(console, args);
    addToResults(args.join(' '), 'error');
};

// 手动测试函数
function testManualLogin() {
    const email = prompt('请输入邮箱:', '<EMAIL>');
    const password = prompt('请输入密码:', 'test123456');
    
    if (email && password) {
        testFunctions.testLoginAPI(email, password);
    }
}

function testAdminLogin() {
    const email = document.getElementById('adminEmail').value;
    const password = document.getElementById('adminPassword').value;

    testFunctions.testLoginAPI(email, password);
}

function testQuickRegister() {
    const username = document.getElementById('testUsername').value;
    const email = document.getElementById('testEmail').value;
    const password = document.getElementById('testPassword').value;

    console.log('开始快速注册测试...');
    addToResults('开始快速注册测试...', 'info');

    $.ajax({
        url: '/api/user/register',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            username: username,
            email: email,
            password: password
        }),
        success: function(response) {
            console.log('✅ 快速注册成功:', response);
            addToResults('✅ 快速注册成功: ' + JSON.stringify(response), 'success');

            // 自动测试登录
            setTimeout(function() {
                testFunctions.testLoginAPI(email, password);
            }, 1000);
        },
        error: function(xhr) {
            console.error('❌ 快速注册失败:', xhr.responseText);
            addToResults('❌ 快速注册失败: ' + xhr.responseText, 'error');
        }
    });
}

function clearStorage() {
    localStorage.clear();
    sessionStorage.clear();
    console.log('✅ 本地存储已清除');
}

// 页面加载完成后的初始化
$(document).ready(function() {
    addToResults('页面加载完成，测试环境准备就绪', 'success');
    addToResults('jQuery版本: ' + $.fn.jquery, 'info');
    
    // 检查是否有保存的token
    const token = localStorage.getItem('access_token');
    if (token) {
        addToResults('发现已保存的访问令牌', 'info');
    }
});
</script>
{% endblock %}
