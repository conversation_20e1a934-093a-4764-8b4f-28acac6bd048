"""
迁移现有数据到新的用户系统
"""
import os
import sys
import uuid
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from backend.models.database import db
from backend.models.user import User
from backend.models.task import Task, TaskType, TaskStatus
from backend.services.task_service import task_service


def migrate_memory_tasks_to_database():
    """将内存中的任务数据迁移到数据库"""
    print("开始迁移内存中的任务数据...")
    
    # 获取匿名用户
    anonymous_user = User.query.filter_by(username='anonymous').first()
    if not anonymous_user:
        print("错误：未找到匿名用户，请先运行数据库初始化")
        return False
    
    migrated_count = 0
    
    # 迁移内存中的任务
    for task_id, task_data in task_service.tasks.items():
        # 检查任务是否已存在于数据库中
        existing_task = Task.query.get(task_id)
        if existing_task:
            print(f"任务 {task_id} 已存在，跳过")
            continue
        
        try:
            # 转换任务类型
            task_type = TaskType(task_data.get('type', 'generate'))
            
            # 转换任务状态
            status_mapping = {
                'queued': TaskStatus.queued,
                'processing': TaskStatus.processing,
                'completed': TaskStatus.completed,
                'failed': TaskStatus.failed
            }
            task_status = status_mapping.get(task_data.get('status', 'queued'), TaskStatus.queued)
            
            # 创建数据库任务记录
            db_task = Task(
                id=task_id,
                user_id=anonymous_user.id,
                type=task_type,
                status=task_status,
                message=task_data.get('message', ''),
                prompt=task_data.get('prompt', ''),
                model=task_data.get('model', ''),
                parameters=task_data.get('parameters', {}),
                input_image_path=task_data.get('input_image', ''),
                output_image_path=task_data.get('output_file', ''),
                credits_cost=task_data.get('credits_cost', 1),
                is_public=False
            )
            
            # 设置时间戳
            if 'created_at' in task_data:
                try:
                    if isinstance(task_data['created_at'], str):
                        db_task.created_at = datetime.fromisoformat(task_data['created_at'].replace('Z', '+00:00'))
                    else:
                        db_task.created_at = task_data['created_at']
                except:
                    db_task.created_at = datetime.utcnow()
            
            if 'completed_at' in task_data:
                try:
                    if isinstance(task_data['completed_at'], str):
                        db_task.completed_at = datetime.fromisoformat(task_data['completed_at'].replace('Z', '+00:00'))
                    else:
                        db_task.completed_at = task_data['completed_at']
                except:
                    pass
            
            db.session.add(db_task)
            migrated_count += 1
            
        except Exception as e:
            print(f"迁移任务 {task_id} 失败: {e}")
            continue
    
    try:
        db.session.commit()
        print(f"成功迁移 {migrated_count} 个任务到数据库")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"迁移任务失败: {e}")
        return False


def migrate_output_images_to_gallery():
    """将输出目录中的图像迁移到画廊系统"""
    print("开始迁移输出图像到画廊系统...")
    
    from backend.config.app_config import AppConfig
    
    # 获取匿名用户
    anonymous_user = User.query.filter_by(username='anonymous').first()
    if not anonymous_user:
        print("错误：未找到匿名用户")
        return False
    
    output_dir = AppConfig.OUTPUT_FOLDER
    if not os.path.exists(output_dir):
        print(f"输出目录不存在: {output_dir}")
        return False
    
    migrated_count = 0
    
    # 遍历输出目录中的图像文件
    for filename in os.listdir(output_dir):
        if not filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp')):
            continue
        
        file_path = os.path.join(output_dir, filename)
        
        # 尝试从文件名解析任务信息
        task_info = parse_filename(filename)
        
        # 查找对应的任务
        task = None
        if task_info['task_id']:
            task = Task.query.filter(Task.id.like(f"%{task_info['task_id']}%")).first()
        
        if not task:
            # 创建一个新的任务记录
            task_id = str(uuid.uuid4())
            task = Task(
                id=task_id,
                user_id=anonymous_user.id,
                type=TaskType(task_info['type']),
                status=TaskStatus.completed,
                message='从历史文件迁移',
                prompt=f"历史生成的{task_info['type']}图像",
                model='unknown',
                output_image_path=file_path,
                credits_cost=1,
                is_public=False
            )
            
            # 设置创建时间（从文件修改时间推断）
            try:
                file_mtime = os.path.getmtime(file_path)
                task.created_at = datetime.fromtimestamp(file_mtime)
                task.completed_at = datetime.fromtimestamp(file_mtime)
            except:
                task.created_at = datetime.utcnow()
                task.completed_at = datetime.utcnow()
            
            db.session.add(task)
            migrated_count += 1
        else:
            # 更新现有任务的输出路径
            if not task.output_image_path:
                task.output_image_path = file_path
                migrated_count += 1
    
    try:
        db.session.commit()
        print(f"成功迁移 {migrated_count} 个图像文件")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"迁移图像文件失败: {e}")
        return False


def parse_filename(filename):
    """解析文件名，提取任务信息"""
    # 文件名格式: {type}_{timestamp}_{task_id}.jpg
    parts = filename.split('_')
    
    task_info = {
        'type': 'generate',
        'timestamp': None,
        'task_id': None
    }
    
    if len(parts) >= 3:
        # 提取类型
        if parts[0] in ['generated', 'edited', 'styled']:
            type_mapping = {
                'generated': 'generate',
                'edited': 'edit',
                'styled': 'style'
            }
            task_info['type'] = type_mapping[parts[0]]
        
        # 提取时间戳
        try:
            task_info['timestamp'] = int(parts[1])
        except:
            pass
        
        # 提取任务ID（去掉文件扩展名）
        if len(parts) >= 3:
            task_id_part = parts[2].split('.')[0]  # 去掉扩展名
            task_info['task_id'] = task_id_part
    
    return task_info


def verify_migration():
    """验证迁移结果"""
    print("验证迁移结果...")
    
    # 统计数据库中的任务
    total_tasks = Task.query.count()
    completed_tasks = Task.query.filter_by(status=TaskStatus.completed).count()
    tasks_with_output = Task.query.filter(Task.output_image_path.isnot(None)).count()
    
    print(f"数据库中总任务数: {total_tasks}")
    print(f"已完成任务数: {completed_tasks}")
    print(f"有输出文件的任务数: {tasks_with_output}")
    
    # 统计内存中的任务
    memory_tasks = len(task_service.tasks)
    print(f"内存中任务数: {memory_tasks}")
    
    # 检查文件完整性
    from backend.config.app_config import AppConfig
    output_dir = AppConfig.OUTPUT_FOLDER
    
    if os.path.exists(output_dir):
        image_files = [f for f in os.listdir(output_dir) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp'))]
        print(f"输出目录中图像文件数: {len(image_files)}")
    
    return True


def main():
    """主迁移函数"""
    print("开始数据迁移...")
    
    # 导入Flask应用
    from backend.app_new import create_app
    
    # 创建应用实例
    app = create_app()
    
    with app.app_context():
        # 迁移内存任务到数据库
        if migrate_memory_tasks_to_database():
            print("✓ 任务数据迁移成功")
        else:
            print("✗ 任务数据迁移失败")
            return False
        
        # 迁移输出图像到画廊
        if migrate_output_images_to_gallery():
            print("✓ 图像文件迁移成功")
        else:
            print("✗ 图像文件迁移失败")
            return False
        
        # 验证迁移结果
        verify_migration()
        
        print("数据迁移完成！")
        return True


if __name__ == '__main__':
    main()
