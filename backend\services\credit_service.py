"""
积分系统服务
"""
from datetime import date, datetime
from typing import Dict, Any, Optional

from backend.models.database import db
from backend.models.user import User
from backend.models.credit import CreditsTransaction, TransactionType
from backend.models.system_settings import SystemSetting
from backend.config.app_config import AppConfig


class CreditService:
    """积分管理服务"""
    
    @staticmethod
    def consume_credits(user_id: int, credits_amount: int, description: str, task_id: Optional[str] = None) -> Dict[str, Any]:
        """消费积分"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 检查积分是否足够
            available_credits = user.available_credits
            if available_credits < credits_amount:
                return {
                    'success': False, 
                    'message': '积分不足',
                    'available_credits': available_credits,
                    'needed_credits': credits_amount
                }
            
            # 检查每日限制
            if user.daily_limit != -1 and user.daily_used >= user.daily_limit:
                return {
                    'success': False,
                    'message': '今日使用次数已达上限',
                    'daily_limit': user.daily_limit,
                    'daily_used': user.daily_used
                }
            
            # 消费积分
            user.used_credits += credits_amount
            user.daily_used += 1
            
            # 记录积分交易
            transaction = CreditsTransaction(
                user_id=user_id,
                transaction_type=TransactionType.usage,
                credits_amount=-credits_amount,
                balance_after=user.available_credits,
                description=description,
                related_task_id=task_id
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            return {
                'success': True,
                'remaining_credits': user.available_credits,
                'daily_remaining': user.daily_remaining,
                'transaction_id': transaction.id
            }
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'积分消费失败: {str(e)}'}
    
    @staticmethod
    def add_credits(user_id: int, credits_amount: int, description: str, 
                   transaction_type: str = 'purchase', payment_id: Optional[int] = None) -> Dict[str, Any]:
        """增加积分"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 增加积分
            user.total_credits += credits_amount
            
            # 记录积分交易
            transaction = CreditsTransaction(
                user_id=user_id,
                transaction_type=TransactionType(transaction_type),
                credits_amount=credits_amount,
                balance_after=user.available_credits,
                description=description,
                payment_id=payment_id
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            return {
                'success': True,
                'new_balance': user.available_credits,
                'transaction_id': transaction.id
            }
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'积分增加失败: {str(e)}'}
    
    @staticmethod
    def reset_daily_usage() -> int:
        """重置每日使用次数（定时任务）"""
        try:
            today = date.today()
            
            # 查找需要重置的用户
            users_to_reset = User.query.filter(User.daily_reset_date < today).all()
            
            reset_count = 0
            for user in users_to_reset:
                user.daily_used = 0
                user.daily_reset_date = today
                reset_count += 1
            
            db.session.commit()
            
            return reset_count
            
        except Exception as e:
            db.session.rollback()
            print(f"重置每日使用次数失败: {e}")
            return 0
    
    @staticmethod
    def calculate_task_cost(task_type: str, model: str, parameters: Optional[Dict] = None) -> int:
        """计算任务积分消费"""
        # 基础消费
        base_costs = AppConfig.CREDIT_COSTS
        base_cost = base_costs.get(task_type, 1)
        
        # 模型倍数
        model_multipliers = AppConfig.MODEL_MULTIPLIERS
        multiplier = model_multipliers.get(model, 1.0)
        
        cost = int(base_cost * multiplier)
        
        # 参数调整
        if parameters:
            # 高分辨率额外消费
            if parameters.get('high_resolution'):
                cost += base_costs.get('high_res_bonus', 1)
            
            # 商业授权倍数
            if parameters.get('commercial_license'):
                cost *= base_costs.get('commercial_multiplier', 3)
        
        return max(cost, 1)  # 最少消费1积分
    
    @staticmethod
    def check_user_limits(user_id: int, credits_needed: int = 1) -> Dict[str, Any]:
        """检查用户限制"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 重置每日使用次数（如果需要）
            user.reset_daily_usage()
            
            # 检查积分
            if user.available_credits < credits_needed:
                return {
                    'success': False,
                    'message': '积分不足',
                    'available_credits': user.available_credits,
                    'needed_credits': credits_needed,
                    'limit_type': 'credits'
                }
            
            # 检查每日限制
            if user.daily_limit != -1 and user.daily_used >= user.daily_limit:
                return {
                    'success': False,
                    'message': '今日使用次数已达上限',
                    'daily_limit': user.daily_limit,
                    'daily_used': user.daily_used,
                    'limit_type': 'daily'
                }
            
            return {
                'success': True,
                'available_credits': user.available_credits,
                'daily_remaining': user.daily_remaining
            }
            
        except Exception as e:
            return {'success': False, 'message': f'检查限制失败: {str(e)}'}
    
    @staticmethod
    def get_user_credit_summary(user_id: int) -> Dict[str, Any]:
        """获取用户积分摘要"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 重置每日使用次数（如果需要）
            user.reset_daily_usage()
            
            # 获取最近的交易记录
            recent_transactions = CreditsTransaction.get_user_transactions(user_id, limit=10)
            
            # 获取使用统计
            usage_stats = CreditsTransaction.get_usage_stats(user_id, days=30)
            
            return {
                'success': True,
                'credits': {
                    'total': user.total_credits,
                    'used': user.used_credits,
                    'available': user.available_credits
                },
                'daily': {
                    'limit': user.daily_limit,
                    'used': user.daily_used,
                    'remaining': user.daily_remaining
                },
                'recent_transactions': [t.to_dict() for t in recent_transactions],
                'usage_stats': usage_stats
            }
            
        except Exception as e:
            return {'success': False, 'message': f'获取积分摘要失败: {str(e)}'}
    
    @staticmethod
    def refund_credits(user_id: int, credits_amount: int, description: str, 
                      related_task_id: Optional[str] = None) -> Dict[str, Any]:
        """退还积分"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 退还积分（减少已使用积分）
            user.used_credits = max(0, user.used_credits - credits_amount)
            
            # 记录退款交易
            transaction = CreditsTransaction(
                user_id=user_id,
                transaction_type=TransactionType.refund,
                credits_amount=credits_amount,
                balance_after=user.available_credits,
                description=description,
                related_task_id=related_task_id
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            return {
                'success': True,
                'refunded_credits': credits_amount,
                'new_balance': user.available_credits,
                'transaction_id': transaction.id
            }
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'积分退还失败: {str(e)}'}
    
    @staticmethod
    def admin_adjust_credits(user_id: int, credits_amount: int, description: str) -> Dict[str, Any]:
        """管理员调整积分"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'message': '用户不存在'}
            
            # 调整积分
            if credits_amount > 0:
                user.total_credits += credits_amount
            else:
                # 负数表示减少积分
                user.used_credits += abs(credits_amount)
            
            # 记录管理员调整
            transaction = CreditsTransaction(
                user_id=user_id,
                transaction_type=TransactionType.admin,
                credits_amount=credits_amount,
                balance_after=user.available_credits,
                description=f"管理员调整: {description}"
            )
            
            db.session.add(transaction)
            db.session.commit()
            
            return {
                'success': True,
                'adjustment': credits_amount,
                'new_balance': user.available_credits,
                'transaction_id': transaction.id
            }
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'message': f'积分调整失败: {str(e)}'}
    
    @staticmethod
    def get_system_credit_stats() -> Dict[str, Any]:
        """获取系统积分统计"""
        try:
            from sqlalchemy import func
            
            # 总用户数和积分统计
            user_stats = db.session.query(
                func.count(User.id).label('total_users'),
                func.sum(User.total_credits).label('total_credits_issued'),
                func.sum(User.used_credits).label('total_credits_used'),
                func.avg(User.total_credits).label('avg_credits_per_user')
            ).first()
            
            # 交易统计
            transaction_stats = db.session.query(
                CreditsTransaction.transaction_type,
                func.count(CreditsTransaction.id).label('count'),
                func.sum(CreditsTransaction.credits_amount).label('total_amount')
            ).group_by(CreditsTransaction.transaction_type).all()
            
            # 格式化交易统计
            transaction_summary = {}
            for stat in transaction_stats:
                transaction_summary[stat.transaction_type.value] = {
                    'count': stat.count,
                    'total_amount': stat.total_amount
                }
            
            return {
                'success': True,
                'user_stats': {
                    'total_users': user_stats.total_users or 0,
                    'total_credits_issued': user_stats.total_credits_issued or 0,
                    'total_credits_used': user_stats.total_credits_used or 0,
                    'avg_credits_per_user': float(user_stats.avg_credits_per_user or 0)
                },
                'transaction_stats': transaction_summary
            }
            
        except Exception as e:
            return {'success': False, 'message': f'获取系统统计失败: {str(e)}'}


# 全局积分服务实例
credit_service = CreditService()
