{% extends "base.html" %}

{% block title %}图像画廊 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-gallery{% endblock %}

{% block extra_head %}
<style>
    /* 画廊页面专属样式 - 深色展示主题 */
    .theme-gallery .card {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(55, 65, 81, 0.1);
        box-shadow: 0 8px 32px rgba(55, 65, 81, 0.12);
    }
    
    .theme-gallery .card-header {
        background: var(--primary-gradient);
        color: white;
        border: none;
    }
    
    .theme-gallery .btn-primary {
        background: var(--primary-gradient);
        border: none;
        box-shadow: 0 4px 15px rgba(55, 65, 81, 0.3);
    }
    
    .theme-gallery .btn-primary:hover {
        background: linear-gradient(135deg, #1f2937, #111827);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(55, 65, 81, 0.4);
    }
    
    /* 增强图片展示效果 */
    .theme-gallery .gallery-item {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(55, 65, 81, 0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .theme-gallery .gallery-item:hover {
        transform: translateY(-12px) scale(1.03);
        box-shadow: 
            0 25px 50px rgba(0, 0, 0, 0.15),
            0 0 0 1px rgba(55, 65, 81, 0.1),
            0 0 40px rgba(55, 65, 81, 0.1);
        border-color: rgba(55, 65, 81, 0.2);
    }
    
    .theme-gallery .gallery-image {
        filter: contrast(1.05) saturate(1.1);
        transition: all 0.4s ease;
    }
    
    .theme-gallery .gallery-image:hover {
        filter: contrast(1.1) saturate(1.2) brightness(1.05);
        transform: scale(1.08);
    }
    
    /* 优化模态框 */
    .theme-gallery .modal-content {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    
    .theme-gallery #modalImage {
        border-radius: 12px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        max-height: 75vh;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-images"></i> 图像画廊
                </h5>
                <div>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshGallery()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#taskModal">
                        <i class="fas fa-list"></i> 任务列表
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if images %}
                    <div class="row g-3" id="imageGallery">
                        {% for image in images %}
                        <div class="col-lg-3 col-md-4 col-sm-6">
                            <div class="card gallery-item">
                                <img src="{{ url_for('view_image', filename=image) }}" 
                                     class="card-img-top gallery-image" 
                                     alt="{{ image }}"
                                     onclick="showImageModal('{{ url_for('view_image', filename=image) }}', '{{ image }}')">
                                <div class="card-body p-2">
                                    <h6 class="card-title small mb-1">{{ image }}</h6>
                                    <div class="d-flex justify-content-between">
                                        <a href="{{ url_for('view_image', filename=image) }}" 
                                           class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('download_file', filename=image) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-image fa-3x mb-3"></i>
                        <h5>暂无图像</h5>
                        <p>开始生成您的第一张图像吧！</p>
                        <a href="{{ url_for('pages.generate_page') }}" class="btn btn-primary">
                            <i class="fas fa-paint-brush"></i> 开始生成
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 图像查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">图像预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" class="img-fluid" style="max-height: 70vh;">
            </div>
            <div class="modal-footer">
                <a id="modalDownload" href="#" class="btn btn-primary" target="_blank">
                    <i class="fas fa-download"></i> 下载
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表模态框 -->
<div class="modal fade" id="taskModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list"></i> 任务列表
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskList">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载...
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="loadTasks()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    关闭
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.gallery-item {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    border: 1px solid rgba(99, 102, 241, 0.08);
    overflow: hidden;
}

.gallery-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(99, 102, 241, 0.2);
    border-color: rgba(99, 102, 241, 0.2);
}

.gallery-image {
    height: 240px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.4s ease;
    border-radius: 0;
}

.gallery-image:hover {
    transform: scale(1.05);
    filter: brightness(1.1) contrast(1.05);
}

.task-item {
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    margin-bottom: 1rem;
    padding: 1rem;
}

.task-item:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.03) 100%);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.task-preview-image {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.gallery-item .card-body {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(10px);
}

.gallery-item .card-title {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

.gallery-item .btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.gallery-item .btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.gallery-item .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow-primary);
}

/* 改善模态框中的图片显示 */
#modalImage {
    border-radius: var(--border-radius-sm);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 优化按钮组样式 */
.d-flex.justify-content-between .btn {
    min-width: 40px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 当任务模态框显示时加载任务列表
    $('#taskModal').on('show.bs.modal', function() {
        loadTasks();
    });
    
    // 每30秒自动刷新任务状态
    setInterval(function() {
        if ($('#taskModal').hasClass('show')) {
            loadTasks();
        }
    }, 30000);
});

function showImageModal(imageSrc, imageName) {
    $('#modalImage').attr('src', imageSrc);
    $('#imageModalTitle').text(imageName);
    $('#modalDownload').attr('href', imageSrc.replace('/view/', '/download/'));
    $('#imageModal').modal('show');
}

function refreshGallery() {
    location.reload();
}

function loadTasks() {
    $.get('/api/tasks')
        .done(function(response) {
            const tasks = response.data?.tasks || response.tasks || [];
            let html = '';
            
            if (tasks.length === 0) {
                html = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <h5>暂无任务记录</h5>
                        <p>开始创建您的第一个任务吧！</p>
                    </div>
                `;
            } else {
                tasks.forEach(function(task) {
                    const statusClass = 'status-' + task.status;
                    const typeIcon = getTypeIcon(task.type);
                    const typeName = getTypeName(task.type);
                    const statusText = getStatusText(task.status);
                    const createdAt = new Date(task.created_at).toLocaleString('zh-CN');
                    
                    html += `
                        <div class="task-item card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1 text-center">
                                        <i class="${typeIcon} fa-2x"></i>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="mb-1">${typeName}</h6>
                                        <p class="mb-1 text-muted small">${task.prompt.substring(0, 100)}${task.prompt.length > 100 ? '...' : ''}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> ${createdAt} | 
                                            <i class="fas fa-cog"></i> ${task.model}
                                        </small>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <span class="status-badge ${statusClass}">${statusText}</span>
                                        ${task.status === 'processing' ? '<div class="progress mt-2"><div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div></div>' : ''}
                                    </div>
                                    <div class="col-md-2 text-center">
                                        ${task.status === 'completed' && task.view_url ? 
                                            `<img src="${task.view_url}" class="task-preview-image mb-2" onclick="showImageModal('${task.view_url}', '${task.task_id}')">` : 
                                            '<div class="task-preview-image mb-2 bg-light d-flex align-items-center justify-content-center"><i class="fas fa-image text-muted"></i></div>'
                                        }
                                    </div>
                                    <div class="col-md-1 text-center">
                                        ${task.status === 'completed' && task.download_url ? 
                                            `<a href="${task.download_url}" class="btn btn-sm btn-primary mb-1" title="下载">
                                                <i class="fas fa-download"></i>
                                            </a>` : ''
                                        }
                                        ${task.status === 'completed' && task.view_url ? 
                                            `<a href="${task.view_url}" class="btn btn-sm btn-outline-primary" target="_blank" title="查看">
                                                <i class="fas fa-eye"></i>
                                            </a>` : ''
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
            }
            
            $('#taskList').html(html);
        })
        .fail(function() {
            $('#taskList').html(`
                <div class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h5>加载失败</h5>
                    <p>无法获取任务列表，请稍后重试</p>
                </div>
            `);
        });
}

function getTypeIcon(type) {
    switch(type) {
        case 'generate': return 'fas fa-paint-brush text-primary';
        case 'edit': return 'fas fa-edit text-success';
        case 'style': return 'fas fa-palette text-warning';
        default: return 'fas fa-image';
    }
}

function getTypeName(type) {
    switch(type) {
        case 'generate': return '图像生成';
        case 'edit': return '图像编辑';
        case 'style': return '风格迁移';
        default: return '未知类型';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'queued': return '排队中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return status;
    }
}
</script>
{% endblock %}