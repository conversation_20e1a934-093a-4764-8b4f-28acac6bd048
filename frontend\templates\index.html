{% extends "base.html" %}

{% block title %}首页 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-gallery{% endblock %}

{% block extra_head %}
<style>
    /* 首页专用样式 - 深色展示主题 - Version 2.0 */
    /* 强制刷新缓存标识符：cache-bust-index-v2 */
    
    body.theme-gallery .task-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 20px rgba(55, 65, 81, 0.1);
        background: var(--card-bg);
        backdrop-filter: blur(10px);
        border-radius: 16px;
    }
    
    body.theme-gallery .task-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(55, 65, 81, 0.2);
    }
    
    body.theme-gallery .task-card .card-header {
        background: linear-gradient(135deg, #374151, #1f2937) !important;
        color: white !important;
        border: none !important;
        border-top-left-radius: 16px !important;
        border-top-right-radius: 16px !important;
        padding: 1.2rem;
    }
    
    /* 通用按钮样式 */
    body.theme-gallery .btn-primary {
        background: linear-gradient(135deg, #374151, #1f2937) !important;
        border: none !important;
        border-radius: 12px;
        padding: 0.8rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(55, 65, 81, 0.3);
    }
    
    /* 图像生成按钮 - 悬停时显示蓝紫色 */
    body.theme-gallery .generate-btn:hover {
        background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4) !important;
    }
    
    /* 图像编辑按钮 - 悬停时显示绿色 */
    body.theme-gallery .edit-btn:hover {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4) !important;
    }
    
    /* 风格迁移按钮 - 悬停时显示橙色 */
    body.theme-gallery .style-btn:hover {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4) !important;
    }
    
    /* 翻译按钮 - 悬停时显示青色 */
    body.theme-gallery .translate-btn:hover {
        background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(6, 182, 212, 0.4) !important;
    }
    
    .feature-icon {
        display: inline-block;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #374151, #1f2937);
        color: white;
        line-height: 60px;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(55, 65, 81, 0.3);
    }
    
    .step-badge {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #374151, #1f2937);
        color: white;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(55, 65, 81, 0.3);
    }
    
    .recent-task-item {
        padding: 1rem;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .recent-task-item:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateX(5px);
    }
    
    /* 状态徽章样式 */
    .status-badge {
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-completed {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }
    
    .status-processing {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }
    
    .status-queued {
        background: linear-gradient(135deg, #374151, #1f2937);
        color: white;
    }
    
    .status-failed {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="text-center mb-5" style="color: #1e293b;">
            <h1 class="display-4 fw-bold mb-3" style="background: linear-gradient(135deg, #374151, #1f2937); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                <i class="fas fa-magic"></i> BFL AI 图像生成器
            </h1>
            <p class="lead" style="color: #475569;">基于 Black Forest Labs 最新 FLUX 模型的强大图像生成工具</p>
        </div>
    </div>
</div>

<div class="row g-4">
    <!-- 图像生成 -->
    <div class="col-md-3">
        <div class="card h-100 task-card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-paint-brush"></i> 图像生成
                </h5>
            </div>
            <div class="card-body d-flex flex-column">
                <p class="card-text text-center">
                    使用文本描述生成高质量图像，支持多种 FLUX 模型和精确参数控制。
                </p>
                <ul class="list-unstyled text-start flex-grow-1">
                    <li><i class="fas fa-check text-success"></i> 支持多种 FLUX 模型</li>
                    <li><i class="fas fa-check text-success"></i> 精确尺寸控制</li>
                    <li><i class="fas fa-check text-success"></i> 高级参数调节</li>
                    <li><i class="fas fa-check text-success"></i> 可重现结果</li>
                </ul>
                <div class="text-center mt-auto">
                    <a href="{{ url_for('pages.generate_page') }}" class="btn btn-primary btn-lg w-100 generate-btn">
                        <i class="fas fa-arrow-right me-2"></i> 开始生成
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图像编辑 -->
    <div class="col-md-3">
        <div class="card h-100 task-card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i> 图像编辑
                </h5>
            </div>
            <div class="card-body d-flex flex-column">
                <p class="card-text text-center">
                    基于文本指令编辑现有图像，实现局部修改和智能编辑。
                </p>
                <ul class="list-unstyled text-start flex-grow-1">
                    <li><i class="fas fa-check text-success"></i> 局部精确编辑</li>
                    <li><i class="fas fa-check text-success"></i> 角色一致性保持</li>
                    <li><i class="fas fa-check text-success"></i> 文本内容修改</li>
                    <li><i class="fas fa-check text-success"></i> 迭代式编辑</li>
                </ul>
                <div class="text-center mt-auto">
                    <a href="{{ url_for('pages.edit_page') }}" class="btn btn-primary btn-lg w-100 edit-btn">
                        <i class="fas fa-arrow-right me-2"></i> 开始编辑
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 风格迁移 -->
    <div class="col-md-3">
        <div class="card h-100 task-card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-palette"></i> 风格迁移
                </h5>
            </div>
            <div class="card-body d-flex flex-column">
                <p class="card-text text-center">
                    基于参考图像的风格生成新内容，实现艺术风格的智能迁移。
                </p>
                <ul class="list-unstyled text-start flex-grow-1">
                    <li><i class="fas fa-check text-success"></i> 艺术风格迁移</li>
                    <li><i class="fas fa-check text-success"></i> 风格保持一致</li>
                    <li><i class="fas fa-check text-success"></i> 创意内容生成</li>
                    <li><i class="fas fa-check text-success"></i> 快速处理</li>
                </ul>
                <div class="text-center mt-auto">
                    <a href="{{ url_for('pages.style_page') }}" class="btn btn-primary btn-lg w-100 style-btn">
                        <i class="fas fa-arrow-right me-2"></i> 开始迁移
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 提示词翻译 -->
    <div class="col-md-3">
        <div class="card h-100 task-card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-language"></i> 提示词翻译
                </h5>
            </div>
            <div class="card-body d-flex flex-column">
                <p class="card-text text-center">
                    将中文提示词翻译成英文，提升AI图像生成效果和准确性。
                </p>
                <ul class="list-unstyled text-start flex-grow-1">
                    <li><i class="fas fa-check text-success"></i> 专业术语翻译</li>
                    <li><i class="fas fa-check text-success"></i> 批量处理支持</li>
                    <li><i class="fas fa-check text-success"></i> 本地大模型</li>
                    <li><i class="fas fa-check text-success"></i> 一键复制导出</li>
                </ul>
                <div class="text-center mt-auto">
                    <a href="{{ url_for('pages.translate_page') }}" class="btn btn-primary btn-lg w-100 translate-btn">
                        <i class="fas fa-arrow-right me-2"></i> 开始翻译
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 功能特性 -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-star"></i> 核心特性
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-4">
                    <div class="col-md-3 text-center">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h6 class="fw-bold" style="color: #1e293b;">高速生成</h6>
                        <p class="text-muted small">
                            基于最新 FLUX 模型，生成速度比传统模型快 8 倍
                        </p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="feature-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h6 class="fw-bold" style="color: #1e293b;">高质量输出</h6>
                        <p class="text-muted small">
                            支持高达 2752x1536 分辨率，细节丰富，质量卓越
                        </p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h6 class="fw-bold" style="color: #1e293b;">精确控制</h6>
                        <p class="text-muted small">
                            支持步数、种子、引导强度等多种参数精确控制
                        </p>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h6 class="fw-bold" style="color: #1e293b;">安全可靠</h6>
                        <p class="text-muted small">
                            内置安全过滤，支持多级安全容忍度设置
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近任务 -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> 最近任务
                </h5>
                <a href="{{ url_for('pages.gallery') }}" class="btn btn-sm btn-outline-light">
                    <i class="fas fa-images"></i> 查看全部
                </a>
            </div>
            <div class="card-body">
                <div id="recent-tasks">
                    <div class="text-center text-muted">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header text-center">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> 如何使用
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <div class="step-badge">1</div>
                        <h6 class="fw-bold" style="color: #1e293b;">选择功能</h6>
                        <p class="text-muted small">
                            根据需求选择图像生成、编辑或风格迁移功能
                        </p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <div class="step-badge">2</div>
                        <h6 class="fw-bold" style="color: #1e293b;">输入内容</h6>
                        <p class="text-muted small">
                            输入文本描述或上传图像，设置相关参数
                        </p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <div class="step-badge">3</div>
                        <h6 class="fw-bold" style="color: #1e293b;">获取结果</h6>
                        <p class="text-muted small">
                            等待处理完成，下载或查看生成的高质量图像
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 检查是否是登录成功后的跳转
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('login_success') === '1') {
        // 显示登录成功消息
        showToast('success', '登录成功！欢迎回来！');

        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);

        // 检查用户是否是管理员，如果是则提供跳转到仪表板的选项
        checkUserTypeAndShowDashboard();
    }

    // 检查登录状态并显示相应提示
    checkLoginStatus();

    // 加载最近任务
    loadRecentTasks();

    // 每30秒刷新一次任务状态
    setInterval(loadRecentTasks, 30000);
});

function checkUserTypeAndShowDashboard() {
    const token = localStorage.getItem('access_token');
    if (!token) return;

    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success && response.user) {
                const user = response.user;

                // 如果是管理员或高级用户，显示仪表板链接
                if (user.user_type === 'enterprise' || user.user_type === 'pro') {
                    showDashboardNotification(user);
                }
            }
        },
        error: function() {
            console.log('获取用户信息失败');
        }
    });
}

function showDashboardNotification(user) {
    const message = `欢迎回来，${user.username}！您可以访问专属仪表板查看详细信息。`;
    const toastHtml = `
        <div class="toast align-items-center text-white bg-info border-0" role="alert" style="min-width: 350px;">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                    <div class="mt-2">
                        <button type="button" class="btn btn-sm btn-light me-2" onclick="goToDashboard()">
                            <i class="fas fa-tachometer-alt"></i> 访问仪表板
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-light" data-bs-dismiss="toast">
                            稍后再说
                        </button>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // 创建toast容器（如果不存在）
    if (!$('#toastContainer').length) {
        $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    const $toast = $(toastHtml);
    $('#toastContainer').append($toast);

    const toast = new bootstrap.Toast($toast[0], { delay: 10000 }); // 10秒后自动消失
    toast.show();
}

function checkLoginStatus() {
    const token = localStorage.getItem('access_token');

    if (!token) {
        // 未登录，显示访客提示
        showGuestWelcome();
        // 为功能按钮添加登录提示
        addLoginPromptToButtons();
    } else {
        // 已登录，验证token有效性
        $.ajax({
            url: '/api/user/profile',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            success: function(response) {
                if (response.success) {
                    showUserWelcome(response.user);
                } else {
                    // Token无效，清除并显示访客提示
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');
                    showGuestWelcome();
                    addLoginPromptToButtons();
                }
            },
            error: function() {
                // Token无效，清除并显示访客提示
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                showGuestWelcome();
                addLoginPromptToButtons();
            }
        });
    }
}

function showGuestWelcome() {
    const welcomeHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            <strong>欢迎访问 BFL AI 图像生成器！</strong>
            您正在以访客身份浏览。<a href="/login" class="alert-link">登录</a>或<a href="/register" class="alert-link">注册</a>即可开始创作图像。
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').first().prepend(welcomeHtml);
}

function showUserWelcome(user) {
    const welcomeHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-user-circle me-2"></i>
            <strong>欢迎回来，${user.username || user.phone}！</strong>
            您有 ${user.available_credits} 积分可用，今日还可生成 ${user.daily_remaining === -1 ? '无限' : user.daily_remaining} 张图像。
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').first().prepend(welcomeHtml);
}

function addLoginPromptToButtons() {
    // 为功能按钮添加点击事件，提示需要登录
    $('.generate-btn, .edit-btn, .style-btn').off('click').on('click', function(e) {
        e.preventDefault();
        showLoginPrompt();
    });
}

function showLoginPrompt() {
    const modalHtml = `
        <div class="modal fade" id="loginPromptModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-lock me-2"></i>需要登录
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                            <h6>开始您的AI创作之旅</h6>
                            <p class="text-muted">登录或注册账户即可使用所有AI图像生成功能</p>

                            <div class="row mt-4">
                                <div class="col-6">
                                    <h6><i class="fas fa-gift text-success"></i> 注册福利</h6>
                                    <ul class="list-unstyled text-start small">
                                        <li>• 免费获得10积分</li>
                                        <li>• 每日可生成5张图像</li>
                                        <li>• 保存和管理作品</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <h6><i class="fas fa-star text-warning"></i> 高级功能</h6>
                                    <ul class="list-unstyled text-start small">
                                        <li>• 图像编辑和修复</li>
                                        <li>• 风格迁移</li>
                                        <li>• 历史记录管理</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <a href="/register" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>立即注册
                        </a>
                        <a href="/login" class="btn btn-outline-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>已有账户
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    $('#loginPromptModal').remove();
    $('body').append(modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('loginPromptModal'));
    modal.show();
}

function goToDashboard() {
    // 直接跳转到仪表板，现在应该可以工作了
    window.location.href = '/dashboard';
}

function loadRecentTasks() {
    $.get('/api/tasks')
        .done(function(response) {
            const tasks = response.data?.tasks || response.tasks || [];
            const recentTasks = tasks.slice(0, 5); // 只显示最近5个任务
            let html = '';
            
            if (recentTasks.length === 0) {
                html = '<div class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无任务记录</div>';
            } else {
                recentTasks.forEach(function(task) {
                    const statusClass = 'status-' + task.status;
                    const typeIcon = getTypeIcon(task.type);
                    const createdAt = new Date(task.created_at).toLocaleString('zh-CN');
                    
                    html += `
                        <div class="recent-task-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="${typeIcon} me-2"></i>
                                <div>
                                    <div class="fw-bold" style="color: #1e293b;">${task.prompt.substring(0, 50)}${task.prompt.length > 50 ? '...' : ''}</div>
                                    <small class="text-muted">${createdAt}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="status-badge ${statusClass}">${getStatusText(task.status)}</span>
                                ${task.status === 'completed' && task.view_url ? 
                                    `<a href="${task.view_url}" class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>` : ''}
                            </div>
                        </div>
                    `;
                });
            }
            
            $('#recent-tasks').html(html);
        })
        .fail(function() {
            $('#recent-tasks').html('<div class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> 加载失败</div>');
        });
}

function getTypeIcon(type) {
    switch(type) {
        case 'generate': return 'fas fa-paint-brush' + ' style="color: #374151;"';
        case 'edit': return 'fas fa-edit text-success';
        case 'style': return 'fas fa-palette text-warning';
        default: return 'fas fa-image' + ' style="color: #374151;"';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'queued': return '排队中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return status;
    }
}

function showToast(type, message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // 创建toast容器（如果不存在）
    if (!$('#toastContainer').length) {
        $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    const $toast = $(toastHtml);
    $('#toastContainer').append($toast);

    const toast = new bootstrap.Toast($toast[0]);
    toast.show();

    // 3秒后自动移除
    setTimeout(() => $toast.remove(), 3000);
}

function showToast(type, message) {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    // 创建toast容器（如果不存在）
    if (!$('#toastContainer').length) {
        $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
    }

    const $toast = $(toastHtml);
    $('#toastContainer').append($toast);

    const toast = new bootstrap.Toast($toast[0]);
    toast.show();

    // 3秒后自动移除
    setTimeout(() => $toast.remove(), 3000);
}
</script>
{% endblock %}