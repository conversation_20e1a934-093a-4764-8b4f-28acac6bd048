#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语言检测和翻译功能测试脚本
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

def test_language_detection():
    """测试语言检测功能"""
    try:
        from utils.common_utils import detect_prompt_language, is_chinese_text
        
        # 测试用例
        test_cases = [
            ("Hello world", "english"),
            ("你好世界", "chinese"),
            ("A beautiful sunset", "english"),
            ("一只可爱的猫咪", "chinese"),
            ("Mix 中英文 text", "chinese"),
            ("", "english"),
            ("123456", "english"),
            ("将猫的颜色改为橙色", "chinese"),
            ("Change the cat's color to orange", "english"),
        ]
        
        print("🔍 测试语言检测功能:")
        for text, expected in test_cases:
            result = detect_prompt_language(text)
            status = "✅" if result == expected else "❌"
            print(f"{status} '{text}' -> {result} (期望: {expected})")
        
        print("\n🔍 测试中文检测功能:")
        for text, expected in test_cases:
            result = is_chinese_text(text)
            expected_bool = expected == "chinese"
            status = "✅" if result == expected_bool else "❌"
            print(f"{status} '{text}' -> {result} (期望: {expected_bool})")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    return True


def test_translation_mock():
    """测试翻译功能的模拟"""
    print("\n🌐 翻译功能测试 (需要翻译服务运行):")
    
    chinese_prompts = [
        "一只可爱的猫咪",
        "将猫的颜色改为橙色", 
        "给人物戴上一顶红色帽子",
        "一座现代摩天大楼矗立在城市中心"
    ]
    
    english_prompts = [
        "A cute cat",
        "Change the cat's color to orange",
        "Add a red hat to the person",
        "A modern skyscraper standing in the city center"
    ]
    
    print("中文提示词示例:")
    for prompt in chinese_prompts:
        print(f"  📝 {prompt}")
    
    print("\n英文提示词示例:")
    for prompt in english_prompts:
        print(f"  📝 {prompt}")
    
    print("\n💡 实际翻译需要启动后端服务进行测试")


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 语言检测和翻译功能测试")
    print("=" * 60)
    
    # 测试语言检测
    if test_language_detection():
        print("\n✅ 语言检测功能测试通过")
    else:
        print("\n❌ 语言检测功能测试失败")
        return
    
    # 测试翻译功能模拟
    test_translation_mock()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("💡 要测试完整功能，请启动Web应用并尝试图像编辑/风格迁移")
    print("=" * 60)


if __name__ == "__main__":
    main() 