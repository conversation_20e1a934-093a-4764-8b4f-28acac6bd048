<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - BFL AI 图像生成器</title>
    
    <!-- Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- FontAwesome -->
    <link href="{{ url_for('static', filename='css/fontawesome.min.css') }}" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/app.css') }}" rel="stylesheet">
    
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .login-header p {
            color: #666;
            margin: 0;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-login:disabled {
            opacity: 0.6;
            transform: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading .loading-spinner {
            display: inline-block;
        }
        
        .loading .btn-text {
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h2><i class="fas fa-magic"></i> BFL AI</h2>
                <p>登录您的账户</p>
            </div>
            
            <!-- 错误提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-circle"></i>
                <span id="errorMessage"></span>
            </div>
            
            <!-- 成功提示 -->
            <div id="successAlert" class="alert alert-success" style="display: none;">
                <i class="fas fa-check-circle"></i>
                <span id="successMessage"></span>
            </div>
            
            <!-- 登录表单 -->
            <form id="loginForm">
                <div class="mb-3">
                    <label for="identifier" class="form-label">
                        <i class="fas fa-user"></i> 用户名/邮箱/手机号
                    </label>
                    <input type="text" class="form-control" id="identifier" name="identifier" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock"></i> 密码
                    </label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe" name="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        记住我
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100">
                    <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
                    <span class="btn-text">登录</span>
                </button>
            </form>
            
            <div class="text-center mt-3">
                <a href="/register" class="text-decoration-none">还没有账户？立即注册</a>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <!-- 简化认证管理器 -->
    <script src="{{ url_for('static', filename='js/simple-auth-manager.js') }}"></script>
    
    <script>
        $(document).ready(function() {
            const $form = $('#loginForm');
            const $submitBtn = $form.find('button[type="submit"]');
            const $errorAlert = $('#errorAlert');
            const $successAlert = $('#successAlert');
            const $errorMessage = $('#errorMessage');
            const $successMessage = $('#successMessage');
            
            // 隐藏提示信息
            function hideAlerts() {
                $errorAlert.hide();
                $successAlert.hide();
            }
            
            // 显示错误信息
            function showError(message) {
                hideAlerts();
                $errorMessage.text(message);
                $errorAlert.show();
            }
            
            // 显示成功信息
            function showSuccess(message) {
                hideAlerts();
                $successMessage.text(message);
                $successAlert.show();
            }
            
            // 设置加载状态
            function setLoading(loading) {
                if (loading) {
                    $submitBtn.addClass('loading').prop('disabled', true);
                } else {
                    $submitBtn.removeClass('loading').prop('disabled', false);
                }
            }
            
            // 监听认证状态变化
            window.simpleAuth.addListener(function(eventData) {
                if (eventData.newState === window.simpleAuth.AuthState.LOGGED_IN) {
                    showSuccess('登录成功！正在跳转...');
                    
                    // 根据用户类型跳转
                    const user = eventData.user;
                    let redirectUrl = '/';
                    
                    if (user && user.user_type === 'enterprise') {
                        redirectUrl = '/admin';
                    } else if (user && (user.user_type === 'pro' || user.user_type === 'premium')) {
                        redirectUrl = '/dashboard';
                    }
                    
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1500);
                } else if (eventData.newState === window.simpleAuth.AuthState.ERROR) {
                    setLoading(false);
                    showError(eventData.errorMessage || '登录失败');
                }
            });
            
            // 表单提交处理
            $form.on('submit', async function(e) {
                e.preventDefault();
                
                hideAlerts();
                setLoading(true);
                
                const identifier = $('#identifier').val().trim();
                const password = $('#password').val();
                const rememberMe = $('#rememberMe').is(':checked');
                
                // 基本验证
                if (!identifier || !password) {
                    setLoading(false);
                    showError('请填写完整的登录信息');
                    return;
                }
                
                // 调用登录
                try {
                    const result = await window.simpleAuth.login(identifier, password, rememberMe);
                    
                    if (!result.success) {
                        setLoading(false);
                        showError(result.message || '登录失败');
                    }
                    // 成功的情况由监听器处理
                } catch (error) {
                    setLoading(false);
                    showError('网络错误，请重试');
                    console.error('登录错误:', error);
                }
            });
            
            // 检查是否已登录
            const authState = window.simpleAuth.getState();
            if (authState.isLoggedIn) {
                showSuccess('您已登录，正在跳转...');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            }
        });
    </script>
</body>
</html>