import os
import sys
import unittest
import json
from unittest.mock import patch, MagicMock
import time

# 处理路径问题
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)
sys.path.insert(0, project_dir)

from backend.services.image_service import ImageService
from backend.services.task_service import task_service
from backend.config.app_config import AppConfig

class TestImageGeneration(unittest.TestCase):
    """测试图像生成流程"""
    
    def setUp(self):
        """测试前准备"""
        self.image_service = ImageService()
        # 清空任务列表
        task_service.tasks.clear()
        # 确保输出目录存在
        os.makedirs(AppConfig.OUTPUT_FOLDER, exist_ok=True)
    
    def wait_for_task_completion(self, task_id: str, timeout: int = 1):
        """等待任务完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            task = task_service.get_task(task_id)
            if task['status'] in ['completed', 'failed']:
                # 给后台线程一点时间完成日志记录
                time.sleep(0.1)
                return True
            time.sleep(0.1)
        return False

    def test_background_generation(self):
        """测试后台生成流程"""
        # 模拟BFL生成器
        mock_generator = MagicMock()
        mock_generator.generate_image.return_value = {
            'success': True,
            'status': 'Success',
            'output_file': os.path.join(AppConfig.OUTPUT_FOLDER, 'test_output.jpg')
        }
        self.image_service.generator = mock_generator
        
        # 创建任务
        task_id = task_service.create_task(
            'generate',
            prompt='测试提示词',
            final_prompt='Test prompt',
            model='flux-kontext-pro'
        )
        
        # 启动生成任务
        self.image_service._background_generate(
            task_id,
            'Test prompt',
            'flux-kontext-pro'
        )
        
        # 等待任务完成
        self.assertTrue(self.wait_for_task_completion(task_id))
        
        # 验证状态更新
        task = task_service.get_task(task_id)
        self.assertEqual(task['status'], 'completed')
        self.assertTrue('output_file' in task)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 模拟生成失败
        mock_generator = MagicMock()
        mock_generator.generate_image.return_value = {
            'success': False,
            'status': 'Error',
            'error': 'Test error',
            'details': 'Test error details'
        }
        self.image_service.generator = mock_generator
        
        # 创建任务
        task_id = task_service.create_task(
            'generate',
            prompt='测试提示词',
            final_prompt='Test prompt',
            model='flux-kontext-pro'
        )
        
        # 启动生成任务
        self.image_service._background_generate(
            task_id,
            'Test prompt',
            'flux-kontext-pro'
        )
        
        # 等待任务完成
        self.assertTrue(self.wait_for_task_completion(task_id))
        
        # 验证错误状态
        task = task_service.get_task(task_id)
        self.assertEqual(task['status'], 'failed')
        self.assertIn('Test error', task['message'])
    
    def test_response_format(self):
        """测试API响应格式"""
        # 创建任务
        task_id = task_service.create_task(
            'generate',
            prompt='测试提示词',
            final_prompt='Test prompt',
            model='flux-kontext-pro'
        )
        
        # 更新为完成状态
        output_filename = os.path.join(AppConfig.OUTPUT_FOLDER, f'test_output_{task_id[:8]}.jpg')
        with open(output_filename, 'w') as f:
            f.write('test image content')
            
        task_service.update_task_status(
            task_id, 
            'completed', 
            '图像生成完成!',
            output_file=output_filename
        )
        
        # 获取任务信息
        task = task_service.get_task(task_id)
        
        # 验证响应格式
        self.assertTrue('task_id' in task)
        self.assertTrue('status' in task)
        self.assertTrue('message' in task)
        self.assertTrue('output_file' in task)
        
        # 验证URL格式
        filename = os.path.basename(task['output_file'])
        self.assertTrue(filename.startswith('test_output_'))
        self.assertTrue(filename.endswith('.jpg'))
        
        # 清理测试文件
        if os.path.exists(output_filename):
            os.remove(output_filename)
    
    def test_task_creation_and_status_update(self):
        """测试任务创建和状态更新"""
        # 1. 创建任务
        task_id = task_service.create_task(
            'generate',
            prompt='测试提示词',
            final_prompt='Test prompt',
            model='flux-kontext-pro'
        )
        
        # 验证任务创建
        self.assertTrue(task_service.task_exists(task_id))
        task = task_service.get_task(task_id)
        self.assertEqual(task['status'], 'queued')
        self.assertEqual(task['message'], '任务已排队')
        
        # 2. 更新为处理中状态
        task_service.update_task_status(task_id, 'processing', '正在生成图像...')
        task = task_service.get_task(task_id)
        self.assertEqual(task['status'], 'processing')
        
        # 3. 更新为完成状态
        output_filename = os.path.join(AppConfig.OUTPUT_FOLDER, f'test_output_{task_id[:8]}.jpg')
        # 创建一个测试图片文件
        with open(output_filename, 'w') as f:
            f.write('test image content')
            
        task_service.update_task_status(
            task_id, 
            'completed', 
            '图像生成完成!',
            output_file=output_filename
        )
        
        task = task_service.get_task(task_id)
        self.assertEqual(task['status'], 'completed')
        self.assertTrue('output_file' in task)
        self.assertEqual(task['output_file'], output_filename)
        
        # 清理测试文件
        if os.path.exists(output_filename):
            os.remove(output_filename)

if __name__ == '__main__':
    unittest.main() 