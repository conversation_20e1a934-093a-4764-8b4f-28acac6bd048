// 前端注册功能测试脚本
console.log('开始测试前端注册功能...');

// 检查jQuery是否加载
if (typeof $ === 'undefined') {
    console.error('❌ jQuery未加载');
} else {
    console.log('✅ jQuery已加载');
}

// 测试注册API调用
function testRegistrationAPI() {
    const timestamp = Date.now().toString().slice(-6); // 只取最后6位
    const testUser = {
        username: 'test' + timestamp,
        email: 'test' + timestamp + '@example.com',
        password: 'test123456'
    };
    
    console.log('测试用户数据:', testUser);
    
    $.ajax({
        url: '/api/user/register',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(testUser),
        success: function(response) {
            console.log('✅ 注册成功:', response);
            
            // 测试登录
            testLoginAPI(testUser.email, testUser.password);
        },
        error: function(xhr) {
            console.error('❌ 注册失败:', xhr.responseText);
        }
    });
}

// 测试登录API调用
function testLoginAPI(email, password) {
    const loginData = {
        email: email,
        password: password
    };
    
    console.log('测试登录数据:', loginData);
    
    $.ajax({
        url: '/api/user/login',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(loginData),
        success: function(response) {
            console.log('✅ 登录成功:', response);
            
            // 保存token
            if (response.access_token) {
                localStorage.setItem('access_token', response.access_token);
                console.log('✅ Token已保存');
                
                // 测试获取用户信息
                testGetProfile();
            }
        },
        error: function(xhr) {
            console.error('❌ 登录失败:', xhr.responseText);
        }
    });
}

// 测试获取用户资料
function testGetProfile() {
    const token = localStorage.getItem('access_token');
    
    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            console.log('✅ 获取用户资料成功:', response);
        },
        error: function(xhr) {
            console.error('❌ 获取用户资料失败:', xhr.responseText);
        }
    });
}

// 在页面加载完成后运行测试
$(document).ready(function() {
    console.log('页面加载完成，开始测试...');
    
    // 延迟1秒后开始测试，确保所有资源都加载完成
    setTimeout(function() {
        testRegistrationAPI();
    }, 1000);
});

// 导出测试函数供手动调用
window.testFunctions = {
    testRegistrationAPI: testRegistrationAPI,
    testLoginAPI: testLoginAPI,
    testGetProfile: testGetProfile
};
