#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
sys.path.insert(0, backend_dir)

from flask import Flask
from backend.config.app_config import AppConfig
from backend.models.database import db, init_db
from backend.models.system_settings import init_system_settings
from backend.models.user import User, UserProfile, UserType
from backend.models.credit import CreditPackage, TransactionType


def init_credit_packages():
    """初始化积分套餐"""
    packages = [
        {
            'name': '入门套餐',
            'credits_amount': 50,
            'price': 4.99,
            'currency': 'USD',
            'discount_percentage': 0,
            'description': '适合新手用户体验',
            'features': ['50积分', '适合体验用户', '无过期时间'],
            'sort_order': 1
        },
        {
            'name': '标准套餐',
            'credits_amount': 150,
            'price': 12.99,
            'currency': 'USD',
            'discount_percentage': 15,
            'description': '性价比最高的选择',
            'features': ['150积分', '比单独购买节省15%', '无过期时间', '推荐套餐'],
            'sort_order': 2
        },
        {
            'name': '专业套餐',
            'credits_amount': 300,
            'price': 23.99,
            'currency': 'USD',
            'discount_percentage': 20,
            'description': '专业用户的理想选择',
            'features': ['300积分', '比单独购买节省20%', '无过期时间', '专业用户首选'],
            'sort_order': 3
        },
        {
            'name': '企业套餐',
            'credits_amount': 1000,
            'price': 69.99,
            'currency': 'USD',
            'discount_percentage': 30,
            'description': '大量使用的最佳选择',
            'features': ['1000积分', '比单独购买节省30%', '无过期时间', '企业级支持'],
            'sort_order': 4
        }
    ]
    
    for package_data in packages:
        # 检查是否已存在
        existing = CreditPackage.query.filter_by(name=package_data['name']).first()
        if not existing:
            package = CreditPackage(**package_data)
            db.session.add(package)
    
    db.session.commit()
    print("积分套餐初始化完成")


def create_admin_user():
    """创建管理员用户"""
    # 检查是否已存在管理员
    admin_user = User.query.filter_by(username='admin').first()
    if admin_user:
        print("管理员用户已存在")
        return admin_user
    
    # 创建管理员用户
    admin_user = User(
        phone='99999999999',
        username='admin',
        email='<EMAIL>',
        user_type=UserType.enterprise,
        is_verified=True
    )
    admin_user.set_password('admin123')  # 请在生产环境中更改密码
    
    db.session.add(admin_user)
    db.session.commit()
    
    # 创建管理员资料
    admin_profile = UserProfile(
        user_id=admin_user.id,
        display_name='系统管理员',
        bio='系统管理员账户'
    )
    db.session.add(admin_profile)
    db.session.commit()
    
    print(f"管理员用户创建完成: {admin_user.username}")
    return admin_user


def create_test_user():
    """创建测试用户"""
    # 检查是否已存在测试用户
    test_user = User.query.filter_by(username='testuser').first()
    if test_user:
        print("测试用户已存在")
        return test_user
    
    # 创建测试用户
    test_user = User(
        phone='12345678901',
        username='testuser',
        email='<EMAIL>',
        user_type=UserType.free,
        is_verified=True
    )
    test_user.set_password('test123')
    
    db.session.add(test_user)
    db.session.commit()
    
    # 创建测试用户资料
    test_profile = UserProfile(
        user_id=test_user.id,
        display_name='测试用户',
        bio='这是一个测试用户账户'
    )
    db.session.add(test_profile)
    db.session.commit()
    
    print(f"测试用户创建完成: {test_user.username}")
    return test_user


def main():
    """主函数"""
    app = Flask(__name__)
    app.config.from_object(AppConfig)
    
    with app.app_context():
        print("开始数据库初始化...")
        
        # 初始化数据库
        init_db(app)
        
        # 初始化系统设置
        init_system_settings()
        
        # 初始化积分套餐
        init_credit_packages()
        
        # 创建默认用户
        create_admin_user()
        create_test_user()
        
        print("数据库初始化完成！")
        
        # 输出默认用户信息
        print("\n默认用户信息:")
        print("管理员: admin / admin123")
        print("测试用户: testuser / test123")


if __name__ == '__main__':
    main()
