{% extends "base.html" %}

{% block title %}图像编辑 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-edit{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/components/upload-component.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/components/common-form-styles.css') }}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i> 图像编辑
                </h5>
            </div>
            <div class="card-body">
                <form id="editForm" action="/api/edit" enctype="multipart/form-data">
                    <!-- 图像上传 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-upload"></i> 上传图像 *
                        </label>
                        <div class="modern-upload-area" id="uploadArea">
                            <input type="file" id="image" name="image" accept="image/*" style="display: none;">
                            <div class="upload-content">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="upload-text">
                                    <h6 class="upload-title">拖拽图像到这里或点击上传</h6>
                                    <p class="upload-subtitle">支持 JPG、PNG、GIF、WebP 格式</p>
                                    <p class="upload-size">建议尺寸不超过 2048x2048</p>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="browseBtn">
                                    <i class="fas fa-folder-open"></i> 选择文件
                                </button>
                            </div>
                            <div class="upload-preview" style="display: none;">
                                <img class="preview-image" src="#" alt="预览图像">
                                <div class="preview-overlay">
                                    <div class="preview-info">
                                        <p class="file-name">filename.jpg</p>
                                        <p class="file-size">1.2 MB</p>
                                    </div>
                                    <div class="preview-actions">
                                        <button type="button" class="btn btn-sm btn-outline-light change-btn">
                                            <i class="fas fa-sync"></i> 更换
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger remove-btn">
                                            <i class="fas fa-trash"></i> 移除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <!-- 编辑指令 -->
                    <div class="mb-3">
                        <label for="prompt" class="form-label">
                            <i class="fas fa-pen"></i> 编辑指令 *
                        </label>
                        <textarea class="form-control" id="prompt" name="prompt" rows="3" 
                                placeholder="请描述您想要对图像进行的修改，支持中文或英文..." required></textarea>
                        <div class="form-text">
                            <i class="fas fa-lightbulb"></i> 提示：具体描述修改内容，支持中文输入并自动翻译
                        </div>
                        
                        <!-- 翻译按钮 -->
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-info btn-sm" id="translateBtn">
                                <i class="fas fa-language"></i> 翻译为英文
                            </button>
                            <span id="translateStatus" class="ms-2 text-muted" style="display: none;"></span>
                        </div>
                    </div>
                    
                    <!-- 翻译结果 -->
                    <div class="mb-3" id="translatedPromptArea" style="display: none;">
                        <label for="translatedPrompt" class="form-label">
                            <i class="fas fa-globe"></i> 英文编辑指令 (可编辑)
                        </label>
                        <textarea class="form-control" id="translatedPrompt" name="translatedPrompt" rows="3" 
                                placeholder="翻译结果将显示在这里，您可以进行修改..."></textarea>
                        <div class="form-text">
                            <i class="fas fa-edit"></i> 您可以修改翻译结果以获得更精确的编辑指令
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-outline-success btn-sm" id="useTranslatedBtn">
                                <i class="fas fa-check"></i> 使用此英文指令
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="hideTranslatedBtn">
                                <i class="fas fa-times"></i> 隐藏翻译
                            </button>
                        </div>
                    </div>
                    
                    <!-- 模型选择 -->
                    <div class="mb-3">
                        <label for="model" class="form-label">
                            <i class="fas fa-cog"></i> 模型选择
                        </label>
                        <select class="form-select" id="model" name="model">
                            <option value="flux-kontext-pro" selected>FLUX Kontext Pro (推荐)</option>
                            <option value="flux-kontext-max">FLUX Kontext Max (最高质量)</option>
                        </select>
                        <div class="form-text">
                            图像编辑功能需要使用 Kontext 系列模型
                        </div>
                    </div>
                    
                    <!-- 高级参数 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h"></i> 高级参数
                                <button type="button" class="btn btn-sm btn-outline-secondary float-end" 
                                        data-bs-toggle="collapse" data-bs-target="#advancedParams">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </h6>
                        </div>
                        <div class="collapse" id="advancedParams">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="steps" class="form-label">扩散步数 (1-50)</label>
                                            <input type="number" class="form-control" id="steps" name="steps" 
                                                   min="1" max="50" placeholder="默认: 40">
                                            <div class="form-text">更高的步数通常产生更好的质量</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="seed" class="form-label">随机种子</label>
                                            <input type="number" class="form-control" id="seed" name="seed" 
                                                   placeholder="留空为随机">
                                            <div class="form-text">相同种子产生相同结果</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="guidance" class="form-label">引导强度</label>
                                            <input type="number" class="form-control" id="guidance" name="guidance" 
                                                   step="0.1" placeholder="默认值">
                                            <div class="form-text">控制对编辑指令的遵循程度</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="mt-4">
                        <!-- 测试模式和重置按钮 -->
                        <div class="row g-2 mb-3">
                            <div class="col-md-4">
                                <div class="form-check form-switch h-100 d-flex align-items-center">
                                    <input class="form-check-input" type="checkbox" id="testMode" name="testMode">
                                    <label class="form-check-label" for="testMode">
                                        <i class="fas fa-bug"></i> 测试模式（显示API请求内容）
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="button" id="resetBtn" class="btn btn-outline-secondary w-100">
                                    <i class="fas fa-undo"></i> 重置表单
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100" id="editBtn">
                                    <i class="fas fa-edit"></i> 开始编辑
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 状态和结果显示 -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> 编辑状态
                </h5>
            </div>
            <div class="card-body">
                <div id="statusArea">
                    <div class="text-center text-muted">
                        <i class="fas fa-edit fa-3x mb-3"></i>
                        <p>上传图像并填写编辑指令来开始编辑</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 结果显示区域 -->
        <div id="resultArea" class="mt-4"></div>
        
        <!-- 编辑示例 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb"></i> 编辑示例
                </h6>
            </div>
            <div class="card-body">
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>颜色修改:</strong> 将猫的颜色改为橙色
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>添加元素:</strong> 给人物戴上一顶红色帽子
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>背景修改:</strong> 将背景改为海滩场景
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>删除元素:</strong> 去掉图中的汽车
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>文字修改:</strong> 将标牌上的文字改为"Hello World"
                </div>
                <div class="example-prompt" onclick="useExample(this)">
                    <strong>风格调整:</strong> 让图像看起来更加卡通化
                </div>
            </div>
        </div>
        
        <!-- 编辑技巧 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-tips"></i> 编辑技巧
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> 描述要具体明确</li>
                    <li><i class="fas fa-check text-success"></i> 可以进行局部修改</li>
                    <li><i class="fas fa-check text-success"></i> 支持添加/删除元素</li>
                    <li><i class="fas fa-check text-success"></i> 可以修改颜色和风格</li>
                    <li><i class="fas fa-check text-success"></i> 支持文字内容编辑</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script type="module" src="{{ url_for('static', filename='js/pages/edit.js') }}"></script>
{% endblock %} 