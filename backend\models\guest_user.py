"""
访客用户模型 - 为未登录用户提供基本访问
"""
from .database import db, BaseModel
from datetime import datetime, date
import uuid


class GuestSession(BaseModel):
    """访客会话模型"""
    __tablename__ = 'guest_sessions'
    
    # 主键UUID
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 会话标识
    session_id = db.Column(db.String(255), unique=True, nullable=False, index=True)  # 浏览器会话ID
    ip_address = db.Column(db.String(45), nullable=False, index=True)  # IP地址（支持IPv6）
    user_agent = db.Column(db.Text)  # 用户代理字符串
    
    # 访问限制
    page_views = db.Column(db.Integer, default=0, nullable=False)  # 页面浏览次数
    last_activity = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)  # 最后活动时间
    
    # 状态
    is_blocked = db.Column(db.<PERSON>, default=False, nullable=False)  # 是否被阻止
    block_reason = db.Column(db.String(255))  # 阻止原因
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
    
    def update_activity(self):
        """更新活动时间和页面浏览次数"""
        self.last_activity = datetime.utcnow()
        self.page_views += 1
    
    def is_session_expired(self, hours=24):
        """检查会话是否过期（默认24小时）"""
        if not self.last_activity:
            return True
        
        from datetime import timedelta
        expiry_time = datetime.utcnow() - timedelta(hours=hours)
        return self.last_activity < expiry_time
    
    def can_access_page(self, max_views_per_day=100):
        """检查是否可以访问页面"""
        if self.is_blocked:
            return False, self.block_reason or "访问被阻止"
        
        if self.is_session_expired():
            return False, "会话已过期，请刷新页面"
        
        # 检查每日页面浏览限制
        if self.page_views >= max_views_per_day:
            return False, f"今日页面浏览次数已达上限（{max_views_per_day}次），请注册账户以获得更多访问权限"
        
        return True, "可以访问"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'ip_address': self.ip_address,
            'page_views': self.page_views,
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'is_blocked': self.is_blocked,
            'block_reason': self.block_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def get_or_create_session(cls, session_id, ip_address, user_agent=None):
        """获取或创建访客会话"""
        session = cls.query.filter_by(session_id=session_id).first()
        
        if not session:
            session = cls(
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.session.add(session)
            db.session.commit()
        else:
            # 更新IP地址和用户代理（可能会变化）
            session.ip_address = ip_address
            if user_agent:
                session.user_agent = user_agent
            db.session.commit()
        
        return session
    
    @classmethod
    def cleanup_expired_sessions(cls, hours=24):
        """清理过期的访客会话"""
        from datetime import timedelta
        expiry_time = datetime.utcnow() - timedelta(hours=hours)
        
        expired_sessions = cls.query.filter(cls.last_activity < expiry_time).all()
        for session in expired_sessions:
            db.session.delete(session)
        
        db.session.commit()
        return len(expired_sessions)
    
    def __repr__(self):
        return f'<GuestSession {self.session_id} views:{self.page_views}>'


class GuestUserManager:
    """访客用户管理器"""
    
    @staticmethod
    def create_guest_response():
        """创建访客用户响应"""
        return {
            'user_type': 'guest',
            'username': 'Guest',
            'display_name': '访客',
            'available_credits': 0,
            'daily_remaining': 0,
            'can_generate': False,
            'can_edit': False,
            'can_style_transfer': False,
            'can_view_gallery': True,
            'can_view_examples': True,
            'message': '您正在以访客身份浏览，注册账户即可开始创作图像'
        }
    
    @staticmethod
    def get_guest_limitations():
        """获取访客限制说明"""
        return {
            'limitations': [
                '无法生成图像',
                '无法编辑图像', 
                '无法使用风格迁移',
                '无法保存作品',
                '无法查看历史记录',
                '页面浏览次数有限'
            ],
            'benefits_of_registration': [
                '免费获得10积分',
                '每日可生成5张图像',
                '保存和管理作品',
                '查看生成历史',
                '参与社区分享',
                '升级获得更多功能'
            ]
        }
