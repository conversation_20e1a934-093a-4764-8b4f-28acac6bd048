#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预处理器 - BFL API 专用
专门用于BFL API的图像预处理功能
注意：通用的100万像素限制功能已迁移到backend.utils.common_utils.resize_image_for_upload
"""

import os
import math
from PIL import Image, ImageOps
from typing import Tuple, Optional

def generate_bfl_candidate_dimensions(target_pixels=1040000, step=16, tolerance=0.05):
    """
    生成BFL API支持的候选尺寸表 - 已重构为调用统一函数
    
    Args:
        target_pixels: 目标像素数
        step: 步长（16的倍数）
        tolerance: 像素数容忍度
        
    Returns:
        候选尺寸列表
    """
    # 导入统一工具函数 - 修复路径
    try:
        from .common_utils import generate_bfl_dimensions
    except ImportError:
        from common_utils import generate_bfl_dimensions
    
    # 使用BFL API的宽高比范围（21:9 到 9:21）
    bfl_ratio_range = (9/21, 21/9)
    
    return generate_bfl_dimensions(
        target_pixels=target_pixels,
        step=step, 
        tolerance=tolerance,
        aspect_ratio_range=bfl_ratio_range
    )


def find_best_bfl_dimensions(input_width: int, input_height: int) -> Tuple[int, int, str]:
    """
    根据输入图像尺寸，找到BFL API支持的最佳尺寸
    
    Args:
        input_width: 输入图像宽度
        input_height: 输入图像高度
    
    Returns:
        (最佳宽度, 最佳高度, 宽高比字符串)
    """
    input_ratio = input_width / input_height
    
    # 生成候选尺寸
    candidates = generate_bfl_candidate_dimensions()
    
    # 找到宽高比最接近的候选
    best_match = None
    min_ratio_diff = float('inf')
    best_aspect_ratio = "2:3"  # 默认
    
    for width, height, pixels, ratio in candidates:
        ratio_diff = abs(ratio - input_ratio)
        
        if ratio_diff < min_ratio_diff:
            min_ratio_diff = ratio_diff
            best_match = (width, height)
            best_aspect_ratio = calculate_precise_aspect_ratio(width, height)
    
    if best_match:
        return best_match[0], best_match[1], best_aspect_ratio
    else:
        return 1024, 1024, "1:1"  # 默认正方形


def calculate_precise_aspect_ratio(width: int, height: int) -> str:
    """
    计算精确的宽高比字符串 - 已重构为调用统一函数
    
    Args:
        width: 图像宽度
        height: 图像高度
    
    Returns:
        精确的宽高比字符串
    """
    # 导入统一工具函数 - 修复路径
    try:
        from .common_utils import simplify_aspect_ratio
    except ImportError:
        from common_utils import simplify_aspect_ratio
    
    return simplify_aspect_ratio(width, height)


def smart_crop_to_target(image, target_width, target_height):
    """
    智能裁剪图像到目标尺寸
    
    Args:
        image: PIL Image对象
        target_width: 目标宽度
        target_height: 目标高度
        
    Returns:
        裁剪后的PIL Image对象
    """
    current_width, current_height = image.size
    target_ratio = target_width / target_height
    current_ratio = current_width / current_height
    
    print(f"📐 原始尺寸: {current_width}x{current_height} (比例: {current_ratio:.4f})")
    print(f"🎯 目标尺寸: {target_width}x{target_height} (比例: {target_ratio:.4f})")
    
    # 计算裁剪尺寸
    if current_ratio > target_ratio:
        # 当前图像更宽，需要裁剪宽度
        new_width = int(current_height * target_ratio)
        new_height = current_height
        left = (current_width - new_width) // 2
        top = 0
        right = left + new_width
        bottom = current_height
        print(f"✂️  裁剪宽度: 从 {current_width} 裁剪到 {new_width}")
    else:
        # 当前图像更高，需要裁剪高度
        new_width = current_width
        new_height = int(current_width / target_ratio)
        left = 0
        top = (current_height - new_height) // 2
        right = current_width
        bottom = top + new_height
        print(f"✂️  裁剪高度: 从 {current_height} 裁剪到 {new_height}")
    
    # 执行裁剪
    cropped_image = image.crop((left, top, right, bottom))
    
    # 缩放到目标尺寸
    final_image = cropped_image.resize((target_width, target_height), Image.Resampling.LANCZOS)
    
    print(f"✅ 最终尺寸: {final_image.size}")
    
    return final_image


def preprocess_image_for_bfl(input_path: str, output_path: str) -> Tuple[bool, dict]:
    """
    专门为BFL API预处理图像
    包括：像素限制检查 + BFL尺寸适配
    
    Args:
        input_path: 输入图像路径
        output_path: 输出图像路径
        
    Returns:
        (处理成功标志, 处理信息字典)
    """
    try:
        print(f"🔄 开始为BFL API预处理图像: {input_path}")
        
        # 检查输入文件是否存在
        if not os.path.exists(input_path):
            error_msg = f"输入文件不存在: {input_path}"
            print(f"❌ {error_msg}")
            return False, {
                'processed': False,
                'error': error_msg,
                'output_file': input_path
            }
        
        # 第一步：使用通用的100万像素限制功能
        try:
            from .common_utils import resize_image_for_upload
        except ImportError:
            from common_utils import resize_image_for_upload
        
        # 先进行像素限制检查和调整
        temp_path = resize_image_for_upload(input_path, target_pixels=1000000)
        
        # 第二步：为BFL API调整尺寸
        with Image.open(temp_path) as image:
            original_width, original_height = image.size
            
            # 找到BFL API支持的最佳尺寸
            target_width, target_height, aspect_ratio = find_best_bfl_dimensions(
                original_width, original_height
            )
            
            print(f"📏 BFL适配: {original_width}x{original_height} -> {target_width}x{target_height}")
            print(f"📐 宽高比: {aspect_ratio}")
            
            # 智能裁剪和缩放到目标尺寸
            processed_image = smart_crop_to_target(image, target_width, target_height)
            
            # 保存处理后的图像
            save_kwargs = {'quality': 95, 'optimize': True}
            if processed_image.mode == 'RGBA':
                processed_image = processed_image.convert('RGB')
            
            processed_image.save(output_path, format='JPEG', **save_kwargs)
            
            print(f"✅ BFL预处理完成: {output_path}")
            
            return True, {
                'processed': True,
                'original_size': (original_width, original_height),
                'final_size': (target_width, target_height),
                'aspect_ratio': aspect_ratio,
                'output_file': output_path
            }
            
    except Exception as e:
        error_msg = f"BFL预处理过程中发生错误: {str(e)}"
        print(f"❌ {error_msg}")
        return False, {
            'processed': False,
            'error': error_msg,
            'output_file': input_path
        }