#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ollama 翻译和润色服务
独立的Ollama服务模块，提供翻译和润色功能
"""

import requests
import json
import time
from typing import Optional, Dict, Any
import logging
import re

logger = logging.getLogger(__name__)

# 默认提示词模板常量
DEFAULT_TRANSLATION_PROMPT = """你是一个专业的AI图像生成提示词翻译专家。请将以下中文提示词翻译成英文，要求：\n\n1. 保持原意准确，适合AI图像生成\n2. 使用专业的艺术和摄影术语\n3. 保持描述的生动性和具体性\n4. 如果有艺术风格、技法等专业词汇，请使用准确的英文对应词\n5. 只返回翻译结果，不要添加其他解释\n\n中文提示词：{chinese_prompt}\n\n英文翻译："""

DEFAULT_POLISH_PROMPT = """你是一名精通人工智能绘图的数字艺术家。现在有如下的文字描述，请根据你的理解在原语义的基础上补充更多细节，以便能方便人工智能更好的理解并绘制出优质的图像画面。\n\n要求：\n1. 保持原始语义和主题不变\n2. 添加更多视觉细节描述（光线、色彩、构图、材质等）\n3. 补充艺术风格和技法描述\n4. 增强画面的层次感和氛围感\n5. 使用专业的艺术和摄影术语\n6. 直接返回润色后的结果，不要重复原始描述，不要添加其他解释\n\n原始描述：{original_text}\n\n润色后的描述："""

class OllamaService:
    """Ollama翻译服务，参数名与DeepseekService完全统一"""
    def __init__(
        self,
        base_url: str = 'http://localhost:11434',
        model: str = 'gemma3',
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: int = 1,
        translation_prompt: Optional[str] = None,
        polish_prompt_template: Optional[str] = None,
        service_type: str = 'ollama'
    ):
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.service_type = service_type
        self.translation_prompt = translation_prompt or DEFAULT_TRANSLATION_PROMPT
        self.polish_prompt_template = polish_prompt_template or DEFAULT_POLISH_PROMPT

        # 初始化时检查服务可用性
        if not self.check_service_status():
            logger.warning(f"⚠️ Ollama服务不可用: {base_url}")
        else:
            logger.info(f"✅ Ollama服务可用: {base_url}")
        
        # 翻译结果提取标识
        self.translation_markers = [
            r'英文翻译：(.*?)(?:\n|$)',
            r'Translation:(.*?)(?:\n|$)',
            r'Translated:(.*?)(?:\n|$)',
            r'English:(.*?)(?:\n|$)'
        ]

    def check_service_status(self) -> bool:
        """检查服务是否可用"""
        try:
            response = self._make_request('GET', '/api/version')
            return response is not None and response.status_code == 200
        except Exception as e:
            logger.error(f"检查服务状态失败: {e}")
            return False

    def check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            # 直接尝试使用模型进行一个简单的生成请求
            test_data = {
                "model": self.model,
                "prompt": "test",
                "stream": False
            }
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=test_data,
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"检查模型可用性失败: {e}")
            return False

    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        info = {
            'type': self.service_type,
            'url': self.base_url,
            'model': self.model,
            'status': 'unavailable',
            'message': ''
        }
        
        try:
            if self.check_service_status():
                info['status'] = 'available'
                info['message'] = '服务正常'
            else:
                info['message'] = '服务不可用'
        except Exception as e:
            info['message'] = str(e)
            
        return info

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Optional[requests.Response]:
        """
        发送HTTP请求到Ollama服务
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据（可选）
            
        Returns:
            响应对象或None
        """
        url = f"{self.base_url.rstrip('/')}{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                if method.upper() == 'GET':
                    response = requests.get(url, timeout=self.timeout)
                elif method.upper() == 'POST':
                    headers = {'Content-Type': 'application/json'}
                    response = requests.post(url, json=data, headers=headers, timeout=self.timeout)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                    
                return response
                
            except requests.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                    
        return None

    def _extract_result(self, raw_response: str, markers: list) -> str:
        """从模型响应中提取结果"""
        if not raw_response:
            return ""
        
        # 移除 <think> 标签和内容
        think_pattern = r'<think>.*?</think>'
        cleaned_text = re.sub(think_pattern, '', raw_response, flags=re.DOTALL).strip()
        
        # 寻找标识后的内容
        for pattern in markers:
            match = re.search(pattern, cleaned_text, re.IGNORECASE | re.DOTALL)
            if match:
                result = match.group(1).strip()
                if result:
                    return result
        
        # 如果没有找到标识，返回清理后的文本
        if cleaned_text:
            return cleaned_text
        
        return raw_response.strip()

    def translate(self, chinese_text: str) -> Dict[str, Any]:
        """
        将中文文本翻译为英文
        
        Args:
            chinese_text: 中文文本
            
        Returns:
            翻译结果字典
        """
        if not chinese_text or not chinese_text.strip():
            return {
                'success': False,
                'original': chinese_text,
                'translated': '',
                'error': '输入文本不能为空',
                'service': self.service_type,
                'model': self.model
            }
            
        try:
            prompt = self.translation_prompt.format(chinese_prompt=chinese_text.strip())
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            response = self._make_request('POST', '/api/generate', data=request_data)
            if not response:
                return {
                    'success': False,
                    'original': chinese_text,
                    'translated': '',
                    'error': '翻译请求失败',
                    'service': self.service_type,
                    'model': self.model
                }
                
            response_data = response.json()
            raw_response = response_data.get('response', '').strip()
            
            if not raw_response:
                return {
                    'success': False,
                    'original': chinese_text,
                    'translated': '',
                    'error': '翻译结果为空',
                    'service': self.service_type,
                    'model': self.model
                }
                
            # 提取翻译结果
            translated_text = self._extract_result(raw_response, self.translation_markers)
            
            return {
                'success': True,
                'original': chinese_text,
                'translated': translated_text,
                'service': self.service_type,
                'model': self.model
            }
            
        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return {
                'success': False,
                'original': chinese_text,
                'translated': '',
                'error': str(e),
                'service': self.service_type,
                'model': self.model
            }

    def polish(self, original_text: str) -> Dict[str, Any]:
        """
        润色文本，添加更多细节描述
        
        Args:
            original_text: 原始文本
            
        Returns:
            润色结果字典
        """
        result = {
            'success': False,
            'original': original_text,
            'polished': '',
            'error': '',
            'service': self.service_type,
            'model': self.model
        }
        
        if not original_text or not original_text.strip():
            result['error'] = '输入文本不能为空'
            return result
        
        try:
            prompt = self.polish_prompt_template.format(original_text=original_text.strip())
            request_data = {
                'model': self.model,
                'prompt': prompt,
                'stream': False,
                'options': {
                    'temperature': 0.3,
                    'top_p': 0.9,
                    'top_k': 40
                }
            }
            
            response = self._make_request('POST', '/api/generate', data=request_data)
            if not response:
                result['error'] = '润色请求失败'
                return result
                
            response_data = response.json()
            raw_response = response_data.get('response', '').strip()
            
            if raw_response:
                # 提取润色结果
                polish_markers = [
                    r'润色后的描述[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)',
                    r'润色结果[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)',
                    r'最终结果[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)',
                    r'修改后[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)',
                    r'优化后[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)',
                    r'补充后[：:]\s*([^\n]+(?:\n(?!原始描述|润色后)[^\n]+)*)'
                ]
                
                final_polished = self._extract_result(raw_response, polish_markers)
                
                if final_polished:
                    result['success'] = True
                    result['polished'] = final_polished
                else:
                    result['error'] = '无法提取有效的润色结果'
            else:
                result['error'] = '模型返回空结果'
                
        except Exception as e:
            logger.error(f"润色失败: {e}")
            result['error'] = str(e)
        
        return result

    def get_available_models(self) -> list:
        """获取可用模型列表"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [model['name'] for model in models]
            return []
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return [] 