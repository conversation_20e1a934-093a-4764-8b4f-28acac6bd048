import os
import sys
import time
import threading
from typing import Dict, Any
from PIL import Image
from datetime import datetime
import asyncio
import base64
import io
import math

# 处理路径问题 - 支持从不同目录运行
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir)
project_dir = os.path.dirname(backend_dir)
bfl_dir = os.path.join(project_dir, 'BFL')

sys.path.insert(0, backend_dir)
sys.path.insert(0, project_dir)
sys.path.insert(0, bfl_dir)

from bfl_image_generator import BFLImageGenerator
from backend.config.app_config import AppConfig
from backend.utils.helpers import generate_timestamp_filename, calculate_aspect_ratio, log_operation, generate_unique_id, format_task_for_response
from backend.utils.common_utils import resize_image_for_upload
from backend.services.task_service import task_service

# 延迟导入积分服务以避免循环导入
def get_credit_service():
    """延迟导入积分服务"""
    try:
        from backend.services.credit_service import credit_service
        return credit_service
    except ImportError:
        return None

class ImageService:
    """图像处理服务 - 负责图像生成、编辑和风格迁移"""
    
    def __init__(self):
        self.generator = BFLImageGenerator(AppConfig.BFL_API_KEY)
    
    def prepare_generation_parameters(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备图像生成参数"""
        kwargs = {}
        
        # 处理尺寸参数
        if request_data.get('aspect_ratio'):
            kwargs['aspect_ratio'] = request_data.get('aspect_ratio')
        
        if request_data.get('width'):
            kwargs['width'] = int(request_data.get('width'))
        
        if request_data.get('height'):
            kwargs['height'] = int(request_data.get('height'))
        
        # 处理高级参数
        if request_data.get('steps'):
            kwargs['steps'] = int(request_data.get('steps'))
        
        if request_data.get('seed'):
            kwargs['seed'] = int(request_data.get('seed'))
        
        if request_data.get('guidance'):
            kwargs['guidance'] = float(request_data.get('guidance'))
        
        if request_data.get('prompt_upsampling'):
            kwargs['prompt_upsampling'] = bool(request_data.get('prompt_upsampling'))
        
        if request_data.get('safety_tolerance'):
            kwargs['safety_tolerance'] = int(request_data.get('safety_tolerance'))
        
        if request_data.get('raw'):
            kwargs['raw'] = bool(request_data.get('raw'))
        
        return kwargs
    
    def prepare_edit_parameters(self, request_form: Dict[str, Any]) -> Dict[str, Any]:
        """准备图像编辑参数"""
        kwargs = {}
        
        if request_form.get('steps'):
            kwargs['steps'] = int(request_form.get('steps'))
        
        if request_form.get('seed'):
            kwargs['seed'] = int(request_form.get('seed'))
        
        if request_form.get('guidance'):
            kwargs['guidance'] = float(request_form.get('guidance'))
        
        return kwargs
    
    def detect_image_aspect_ratio(self, image_path: str) -> str:
        """检测图像的宽高比"""
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                aspect_ratio = calculate_aspect_ratio(width, height)
                log_operation("检测图像尺寸", f"{width}x{height}, 宽高比: {aspect_ratio}")
                return aspect_ratio
        except Exception as e:
            log_operation("检测图像尺寸失败", str(e), False)
            return "1:1"  # 默认正方形
    
    def generate_output_filename(self, prefix: str, task_id: str) -> str:
        """生成输出文件名"""
        timestamp = int(time.time())
        filename = f"{prefix}_{timestamp}_{task_id[:8]}.jpg"
        return AppConfig.get_output_path(filename)
    
    def start_generation_task(self, task_id: str, prompt: str, model: str, **kwargs):
        """启动图像生成任务"""
        # 使用统一的更新方法
        self._safe_update_task_status(task_id, 'processing', '正在生成图像...')
        
        thread = threading.Thread(
            target=self._background_generate,
            args=(task_id, prompt, model),
            kwargs=kwargs
        )
        thread.daemon = True
        thread.start()
    
    def start_edit_task(self, task_id: str, input_image: str, prompt: str, model: str, **kwargs):
        """启动图像编辑任务"""
        thread = threading.Thread(
            target=self._background_edit,
            args=(task_id, input_image, prompt, model),
            kwargs=kwargs
        )
        thread.daemon = True
        thread.start()
    
    def start_style_transfer_task(self, task_id: str, reference_image: str, prompt: str, model: str, **kwargs):
        """启动风格迁移任务"""
        thread = threading.Thread(
            target=self._background_style_transfer,
            args=(task_id, reference_image, prompt, model),
            kwargs=kwargs
        )
        thread.daemon = True
        thread.start()

    def _safe_update_task_status(self, task_id: str, status: str, message: str, **kwargs):
        """安全地更新任务状态，处理循环导入和服务未找到的问题"""
        try:
            # 直接导入全局实例
            success = task_service.update_task_status(task_id, status, message, **kwargs)
            if not success:
                log_operation(f"任务不存在或更新失败", f"ID: {task_id[:8]}", success=False)

            # 如果任务失败，尝试退还积分
            if status == 'failed':
                self._handle_task_failure_refund(task_id)

        except Exception as e:
            log_operation(f"更新任务 {task_id[:8]} 状态失败", str(e), success=False)

    def _handle_task_failure_refund(self, task_id: str):
        """处理任务失败时的积分退还"""
        try:
            # 获取任务信息
            task = task_service.get_task(task_id)
            if not task:
                return

            user_id = task.get('user_id')
            credits_cost = task.get('credits_cost')

            if user_id and credits_cost:
                credit_service = get_credit_service()
                if credit_service:
                    refund_result = credit_service.refund_credits(
                        user_id, credits_cost, f'任务失败退还积分', task_id
                    )
                    if refund_result['success']:
                        log_operation(f"任务失败积分退还", f"ID: {task_id[:8]}, 退还: {credits_cost}积分")
                    else:
                        log_operation(f"任务失败积分退还失败", f"ID: {task_id[:8]}, 错误: {refund_result['message']}", success=False)
        except Exception as e:
            log_operation(f"处理任务失败退还积分异常", f"ID: {task_id[:8]}, 错误: {str(e)}", success=False)

    def _background_generate(self, task_id: str, prompt: str, model: str, **kwargs):
        """后台图像生成 - 重构为分步式处理"""
        try:
            # 第一步：提交请求到BFL API
            self._safe_update_task_status(task_id, 'processing', '正在提交生成请求到BFL API...')
            log_operation("开始图像生成", f"提示词: {prompt[:50]}...")
            
            bfl_request_id = self.generator.submit_generation_request(
                prompt=prompt, model=model, **kwargs
            )
            
            if not bfl_request_id:
                self._safe_update_task_status(task_id, 'failed', '提交BFL API请求失败')
                log_operation("BFL API提交失败", "", success=False)
                return
            
            log_operation("BFL API请求提交成功", f"request_id: {bfl_request_id}")
            
            # 第二步：异步轮询BFL API状态
            self._poll_bfl_result(task_id, bfl_request_id, "generated")
                
        except Exception as e:
            error_msg = f'生成错误: {str(e)}'
            self._safe_update_task_status(task_id, 'failed', error_msg)
            log_operation("图像生成异常", str(e), success=False)
    
    def _background_edit(self, task_id: str, input_image: str, prompt: str, model: str, **kwargs):
        """后台图像编辑 - 重构为分步式处理"""
        try:
            if not os.path.exists(input_image):
                error_msg = f'输入图像文件不存在: {input_image}'
                self._safe_update_task_status(task_id, 'failed', error_msg)
                return
            
            # 新增：上传前预处理图像，将其调整到1MP左右
            self._safe_update_task_status(task_id, 'processing', '正在预处理上传的图像...')
            processed_image_path = resize_image_for_upload(input_image)

            self._safe_update_task_status(task_id, 'processing', '正在提交编辑请求到BFL API...')
            log_operation("开始图像编辑", f"输入: {os.path.basename(processed_image_path)}")
            
            kwargs['aspect_ratio'] = self.detect_image_aspect_ratio(processed_image_path)
            
            bfl_request_id = self.generator.submit_generation_request(
                prompt=prompt, model=model, image=processed_image_path, **kwargs
            )
            
            if not bfl_request_id:
                self._safe_update_task_status(task_id, 'failed', '提交BFL API请求失败')
                log_operation("BFL API提交失败", "", success=False)
                return
            
            log_operation("BFL API请求提交成功", f"request_id: {bfl_request_id}")
            
            self._poll_bfl_result(task_id, bfl_request_id, "edited")
                
        except Exception as e:
            error_msg = f'编辑错误: {str(e)}'
            self._safe_update_task_status(task_id, 'failed', error_msg)
            log_operation("图像编辑异常", str(e), success=False)
    
    def _poll_bfl_result(self, task_id: str, bfl_request_id: str, prefix: str):
        """轮询BFL API结果并实时更新任务状态"""
        try:
            max_polls = 150
            poll_count = 0
            
            while poll_count < max_polls:
                poll_count += 1
                bfl_result = self.generator.get_result(bfl_request_id)
                
                if bfl_result:
                    status = bfl_result.get("status", "unknown")
                    
                    if status == "Ready":
                        sample_url = bfl_result.get("result", {}).get("sample")
                        if sample_url:
                            output_filename = self.generate_output_filename(prefix, task_id)
                            if self.generator.download_image(sample_url, output_filename):
                                msg = f"图像{prefix}完成!"
                                self._safe_update_task_status(task_id, 'completed', msg, output_file=output_filename)
                                log_operation(f"图像{prefix}成功", f"输出: {os.path.basename(output_filename)}")
                            else:
                                msg = f"图像{prefix}完成但下载失败"
                                self._safe_update_task_status(task_id, 'failed', msg)
                                log_operation(f"图像{prefix}下载失败", "", success=False)
                        else:
                            msg = '任务完成但未找到图像URL'
                            self._safe_update_task_status(task_id, 'failed', msg)
                            log_operation(f"图像{prefix}失败", "未找到图像URL", success=False)
                        return
                        
                    elif status in ["Task not found", "Pending"]:
                        msg = f'任务排队中... ({poll_count}/{max_polls})' if status == "Pending" else f'等待BFL API处理... ({poll_count}/{max_polls})'
                        self._safe_update_task_status(task_id, 'processing', msg)

                    elif status in ["Request Moderated", "Content Moderated"]:
                        msg = f'请求被内容审核拦截，请修改提示词后重试'
                        self._safe_update_task_status(task_id, 'failed', msg)
                        log_operation("BFL内容审核", f"{status}: {bfl_result}", success=False)
                        return

                    elif status == "Error":
                        error_details = bfl_result.get("details", "未知错误")
                        msg = f'BFL API错误: {error_details}'
                        self._safe_update_task_status(task_id, 'failed', msg)
                        log_operation("BFL API错误", error_details, success=False)
                        return
                        
                    else: # Unknown status
                        msg = f'处理中 ({status})... ({poll_count}/{max_polls})'
                        self._safe_update_task_status(task_id, 'processing', msg)
                else:
                    msg = f'连接BFL API中... ({poll_count}/{max_polls})'
                    self._safe_update_task_status(task_id, 'processing', msg)
                
                time.sleep(2)
            
            # Timeout
            msg = f'任务超时 (已轮询{max_polls * 2}秒)，请重试'
            self._safe_update_task_status(task_id, 'failed', msg)
            log_operation("BFL轮询超时", f"request_id: {bfl_request_id}", success=False)
                
        except Exception as e:
            error_msg = f'轮询BFL结果异常: {str(e)}'
            self._safe_update_task_status(task_id, 'failed', error_msg)
            log_operation("BFL轮询异常", str(e), success=False)
    
    def _background_style_transfer(self, task_id: str, reference_image: str, prompt: str, model: str, **kwargs):
        """后台风格迁移 - 重构为分步式处理"""
        try:
            if not os.path.exists(reference_image):
                error_msg = f'参考图像文件不存在: {reference_image}'
                self._safe_update_task_status(task_id, 'failed', error_msg)
                return
            
            # 新增：上传前预处理图像
            self._safe_update_task_status(task_id, 'processing', '正在预处理参考图像...')
            processed_image_path = resize_image_for_upload(reference_image)

            self._safe_update_task_status(task_id, 'processing', '正在提交风格迁移请求到BFL API...')
            log_operation("开始风格迁移", f"参考图像: {os.path.basename(processed_image_path)}")
            
            kwargs['aspect_ratio'] = self.detect_image_aspect_ratio(processed_image_path)
            
            bfl_request_id = self.generator.submit_generation_request(
                prompt=prompt, model=model, image=processed_image_path, **kwargs
            )
            
            if not bfl_request_id:
                self._safe_update_task_status(task_id, 'failed', '提交BFL API请求失败')
                log_operation("BFL API提交失败", "", success=False)
                return
            
            log_operation("BFL API请求提交成功", f"request_id: {bfl_request_id}")
            
            self._poll_bfl_result(task_id, bfl_request_id, "styled")
                
        except Exception as e:
            error_msg = f'风格迁移错误: {str(e)}'
            self._safe_update_task_status(task_id, 'failed', error_msg)
            log_operation("风格迁移异常", str(e), success=False)

# 全局图像服务实例
image_service = ImageService()