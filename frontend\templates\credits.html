{% extends "base.html" %}

{% block title %}积分商城 - BFL Image Generator{% endblock %}

{% block extra_css %}
<style>
.credit-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.credit-balance {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    margin-top: 2rem;
    backdrop-filter: blur(10px);
}

.package-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.package-card.recommended {
    border: 3px solid #007bff;
    transform: scale(1.05);
}

.package-card.recommended::before {
    content: "推荐";
    position: absolute;
    top: 20px;
    right: -30px;
    background: #007bff;
    color: white;
    padding: 5px 40px;
    font-size: 12px;
    font-weight: bold;
    transform: rotate(45deg);
}

.package-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.package-price {
    font-size: 3rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.package-price .currency {
    font-size: 1.5rem;
    vertical-align: top;
}

.package-price .original {
    font-size: 1.2rem;
    text-decoration: line-through;
    color: #999;
    margin-left: 10px;
}

.package-credits {
    font-size: 1.5rem;
    color: #007bff;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.package-savings {
    background: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.feature-list {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
}

.feature-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li::before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-right: 10px;
}

.btn-purchase {
    width: 100%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 1rem;
    border-radius: 10px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-purchase:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    color: white;
}

.btn-purchase:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.action-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
}

.action-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.action-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.checkin-streak {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 1rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cost-preview {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    margin: 1rem 0;
}

.cost-preview.insufficient {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.referral-section {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    margin-top: 2rem;
}

.referral-code {
    background: rgba(255, 255, 255, 0.2);
    padding: 1rem;
    border-radius: 10px;
    font-family: monospace;
    font-size: 1.2rem;
    text-align: center;
    margin: 1rem 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.referral-code:hover {
    background: rgba(255, 255, 255, 0.3);
}
</style>
{% endblock %}

{% block content %}
<div class="credit-header">
    <div class="container">
        <h1 class="display-4 mb-0">积分商城</h1>
        <p class="lead">购买积分，解锁无限创作可能</p>
        
        <div class="credit-balance" id="creditBalance">
            <div class="row text-center">
                <div class="col-md-3">
                    <h3 id="availableCredits">-</h3>
                    <p>可用积分</p>
                </div>
                <div class="col-md-3">
                    <h3 id="totalCredits">-</h3>
                    <p>总积分</p>
                </div>
                <div class="col-md-3">
                    <h3 id="dailyRemaining">-</h3>
                    <p>今日剩余</p>
                </div>
                <div class="col-md-3">
                    <h3 id="userType">-</h3>
                    <p>用户类型</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container my-5">
    <!-- 快速操作 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="quick-actions">
                <h3 class="mb-4">快速操作</h3>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="action-card" onclick="dailyCheckin()">
                            <div class="action-icon">📅</div>
                            <h5>每日签到</h5>
                            <p class="text-muted">签到获得积分奖励</p>
                            <div id="checkinInfo" class="checkin-streak">
                                <small>连续签到：<span id="consecutiveDays">0</span> 天</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="action-card" onclick="showReferralModal()">
                            <div class="action-icon">👥</div>
                            <h5>推荐好友</h5>
                            <p class="text-muted">邀请好友注册获得奖励</p>
                            <small>每推荐一人奖励5积分</small>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="action-card" onclick="showTransactionHistory()">
                            <div class="action-icon">📊</div>
                            <h5>交易记录</h5>
                            <p class="text-muted">查看积分使用明细</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分套餐 -->
    <div class="row mb-5">
        <div class="col-12">
            <h2 class="text-center mb-5">选择积分套餐</h2>
            <div class="row" id="creditPackages">
                <!-- 套餐将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 积分消费预览 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>积分消费预览</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label>任务类型</label>
                            <select class="form-control" id="taskType" onchange="updateCostPreview()">
                                <option value="generate">图像生成</option>
                                <option value="edit">图像编辑</option>
                                <option value="style">风格迁移</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>模型</label>
                            <select class="form-control" id="model" onchange="updateCostPreview()">
                                <option value="flux-dev">Flux Dev (0.8x)</option>
                                <option value="flux-pro">Flux Pro (1.0x)</option>
                                <option value="flux-kontext-pro">Flux Kontext Pro (1.5x)</option>
                                <option value="flux-kontext-max">Flux Kontext Max (2.0x)</option>
                                <option value="flux-pro-1.1-ultra">Flux Pro Ultra (3.0x)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>额外选项</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="highResolution" onchange="updateCostPreview()">
                                <label class="form-check-label" for="highResolution">高分辨率 (+1积分)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="commercialLicense" onchange="updateCostPreview()">
                                <label class="form-check-label" for="commercialLicense">商业授权 (x3倍数)</label>
                            </div>
                        </div>
                    </div>
                    <div id="costPreview" class="cost-preview mt-3">
                        <strong>预计消费：<span id="estimatedCost">1</span> 积分</strong>
                        <p class="mb-0" id="costDescription">基础图像生成任务</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 推荐码模态框 -->
<div class="modal fade" id="referralModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">推荐好友</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="referral-section">
                    <h6>您的推荐码</h6>
                    <div class="referral-code" id="referralCode" onclick="copyReferralCode()">
                        点击复制推荐码
                    </div>
                    <p class="mb-0">
                        <small>分享给好友，他们注册时使用此推荐码，您将获得5积分奖励！</small>
                    </p>
                </div>
                <div class="mt-3">
                    <h6>推荐统计</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 id="referredCount">0</h4>
                            <p>推荐人数</p>
                        </div>
                        <div class="col-6">
                            <h4 id="totalReferralRewards">0</h4>
                            <p>获得积分</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 交易记录模态框 -->
<div class="modal fade" id="transactionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">积分交易记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="transactionHistory">
                    <!-- 交易记录将动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载遮罩 -->
<div class="loading-overlay" id="loadingOverlay" style="display: none;">
    <div class="loading-content">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentUser = null;
let creditPackages = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUserInfo();
    loadCreditPackages();
    updateCostPreview();
});

// 加载用户信息
async function loadUserInfo() {
    try {
        const response = await apiClient.get('/api/user/profile');
        if (response.success) {
            currentUser = response.user;
            updateCreditDisplay();
        }
    } catch (error) {
        console.error('加载用户信息失败:', error);
    }
}

// 更新积分显示
function updateCreditDisplay() {
    if (!currentUser) return;
    
    document.getElementById('availableCredits').textContent = currentUser.available_credits || 0;
    document.getElementById('totalCredits').textContent = currentUser.total_credits || 0;
    document.getElementById('dailyRemaining').textContent = 
        currentUser.daily_limit === -1 ? '无限制' : (currentUser.daily_remaining || 0);
    document.getElementById('userType').textContent = currentUser.user_type || 'free';
}

// 加载积分套餐
async function loadCreditPackages() {
    try {
        const response = await apiClient.get('/api/user/credit-packages');
        if (response.success) {
            creditPackages = response.packages;
            renderCreditPackages();
        }
    } catch (error) {
        console.error('加载积分套餐失败:', error);
    }
}

// 渲染积分套餐
function renderCreditPackages() {
    const container = document.getElementById('creditPackages');
    
    creditPackages.forEach((pkg, index) => {
        const isRecommended = pkg.name.includes('标准') || pkg.name.includes('推荐');
        
        const packageCard = `
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="package-card ${isRecommended ? 'recommended' : ''}">
                    <div class="package-header">
                        <h4>${pkg.name}</h4>
                        <div class="package-credits">${pkg.credits_amount} 积分</div>
                        <div class="package-price">
                            <span class="currency">$</span>${pkg.price}
                            ${pkg.original_price > pkg.price ? 
                                `<span class="original">$${pkg.original_price.toFixed(2)}</span>` : ''}
                        </div>
                        ${pkg.discount_percentage > 0 ? 
                            `<div class="package-savings">节省 ${pkg.discount_percentage}%</div>` : ''}
                    </div>
                    
                    <ul class="feature-list">
                        ${pkg.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    
                    <button class="btn btn-purchase" onclick="purchaseCredits('${pkg.id}')">
                        立即购买
                    </button>
                </div>
            </div>
        `;
        
        container.innerHTML += packageCard;
    });
}

// 购买积分
async function purchaseCredits(packageId) {
    const pkg = creditPackages.find(p => p.id === packageId);
    if (!pkg) return;
    
    if (!confirm(`确认购买 ${pkg.name}（${pkg.credits_amount} 积分）吗？`)) {
        return;
    }
    
    showLoading();
    
    try {
        const response = await apiClient.post('/api/user/purchase-credits', {
            package_id: packageId,
            payment_method: 'mock'  // 模拟支付
        });
        
        if (response.success) {
            alert(`购买成功！获得 ${pkg.credits_amount} 积分`);
            await loadUserInfo();  // 刷新用户信息
        } else {
            alert('购买失败: ' + response.message);
        }
    } catch (error) {
        alert('购买失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 每日签到
async function dailyCheckin() {
    try {
        const response = await apiClient.post('/api/user/daily-checkin');
        
        if (response.success) {
            alert(`签到成功！获得 ${response.reward_credits} 积分`);
            document.getElementById('consecutiveDays').textContent = response.consecutive_days;
            await loadUserInfo();
        } else {
            alert(response.message);
        }
    } catch (error) {
        alert('签到失败: ' + error.message);
    }
}

// 显示推荐码模态框
async function showReferralModal() {
    try {
        const response = await apiClient.get('/api/user/referral-code');
        
        if (response.success) {
            document.getElementById('referralCode').textContent = response.referral_code;
            document.getElementById('referredCount').textContent = response.referred_count;
            document.getElementById('totalReferralRewards').textContent = response.total_rewards;
            
            new bootstrap.Modal(document.getElementById('referralModal')).show();
        }
    } catch (error) {
        alert('获取推荐码失败: ' + error.message);
    }
}

// 复制推荐码
function copyReferralCode() {
    const referralCode = document.getElementById('referralCode').textContent;
    navigator.clipboard.writeText(referralCode).then(() => {
        alert('推荐码已复制到剪贴板！');
    });
}

// 显示交易记录
async function showTransactionHistory() {
    try {
        const response = await apiClient.get('/api/user/credits/transactions');
        
        if (response.success) {
            renderTransactionHistory(response.transactions);
            new bootstrap.Modal(document.getElementById('transactionModal')).show();
        }
    } catch (error) {
        alert('获取交易记录失败: ' + error.message);
    }
}

// 渲染交易记录
function renderTransactionHistory(transactions) {
    const container = document.getElementById('transactionHistory');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">暂无交易记录</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table">';
    html += '<thead><tr><th>时间</th><th>类型</th><th>积分变化</th><th>描述</th><th>余额</th></tr></thead><tbody>';
    
    transactions.forEach(trans => {
        const typeNames = {
            'purchase': '购买',
            'usage': '使用',
            'refund': '退款',
            'bonus': '奖励',
            'admin': '管理员调整'
        };
        
        const changeClass = trans.credits_amount > 0 ? 'text-success' : 'text-danger';
        const changePrefix = trans.credits_amount > 0 ? '+' : '';
        
        html += `
            <tr>
                <td>${new Date(trans.created_at).toLocaleString()}</td>
                <td>${typeNames[trans.transaction_type] || trans.transaction_type}</td>
                <td class="${changeClass}">${changePrefix}${trans.credits_amount}</td>
                <td>${trans.description || '-'}</td>
                <td>${trans.balance_after}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// 更新积分消费预览
async function updateCostPreview() {
    const taskType = document.getElementById('taskType').value;
    const model = document.getElementById('model').value;
    const highResolution = document.getElementById('highResolution').checked;
    const commercialLicense = document.getElementById('commercialLicense').checked;
    
    try {
        const response = await apiClient.post('/api/preview-cost', {
            task_type: taskType,
            model: model,
            parameters: {
                high_resolution: highResolution,
                commercial_license: commercialLicense
            }
        });
        
        if (response.success) {
            document.getElementById('estimatedCost').textContent = response.credits_cost;
            
            const preview = document.getElementById('costPreview');
            const description = document.getElementById('costDescription');
            
            if (response.user_info && !response.can_afford) {
                preview.className = 'cost-preview insufficient';
                description.textContent = response.suggestion ? response.suggestion.message : '积分不足';
            } else {
                preview.className = 'cost-preview';
                description.textContent = `${taskType} - ${model}${highResolution ? ' + 高分辨率' : ''}${commercialLicense ? ' + 商业授权' : ''}`;
            }
        }
    } catch (error) {
        console.error('获取消费预览失败:', error);
    }
}

// 显示/隐藏加载遮罩
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}
</script>
{% endblock %} 