<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证测试</title>
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
</head>
<body>
    <div style="padding: 20px;">
        <h2>认证功能测试</h2>
        
        <div id="loginSection">
            <h3>登录测试</h3>
            <form id="loginForm">
                <div style="margin: 10px 0;">
                    <label>用户名/手机号:</label>
                    <input type="text" id="identifier" value="admin" style="margin-left: 10px; padding: 5px;">
                </div>
                <div style="margin: 10px 0;">
                    <label>密码:</label>
                    <input type="password" id="password" value="admin123" style="margin-left: 10px; padding: 5px;">
                </div>
                <button type="submit" style="padding: 8px 16px;">登录</button>
            </form>
        </div>
        
        <div id="profileSection" style="margin-top: 30px;">
            <h3>Profile测试</h3>
            <button id="testProfile" style="padding: 8px 16px;">测试Profile接口</button>
        </div>
        
        <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; white-space: pre-wrap;"></div>
    </div>
    
    <script>
    let currentToken = null;
    
    $('#loginForm').on('submit', function(e) {
        e.preventDefault();
        
        const identifier = $('#identifier').val();
        const password = $('#password').val();
        
        $('#result').text('登录中...');
        
        $.ajax({
            url: '/api/user/login',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                identifier: identifier,
                password: password
            }),
            success: function(response) {
                $('#result').text('登录响应:\n' + JSON.stringify(response, null, 2));
                
                if (response.success && response.access_token) {
                    currentToken = response.access_token;
                    $('#result').append('\n\n✅ 登录成功！Token已保存。');
                } else {
                    $('#result').append('\n\n❌ 登录失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr) {
                $('#result').text('登录失败:\n状态码: ' + xhr.status + '\n响应: ' + xhr.responseText);
            }
        });
    });
    
    $('#testProfile').on('click', function() {
        if (!currentToken) {
            $('#result').text('请先登录获取Token');
            return;
        }
        
        $('#result').text('测试Profile接口中...');
        
        $.ajax({
            url: '/api/user/profile',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + currentToken
            },
            success: function(response) {
                $('#result').text('Profile响应:\n' + JSON.stringify(response, null, 2));
                
                if (response.success) {
                    $('#result').append('\n\n✅ Profile接口访问成功！');
                } else {
                    $('#result').append('\n\n❌ Profile接口返回失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr) {
                $('#result').text('Profile接口失败:\n状态码: ' + xhr.status + '\n响应: ' + xhr.responseText);
            }
        });
    });
    </script>
</body>
</html>