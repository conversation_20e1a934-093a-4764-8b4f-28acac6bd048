{% extends "base.html" %}

{% block title %}旧照片修复{% endblock %}

{% block body_class %}theme-restore{% endblock %}

{% block extra_css %}
<style>
/* 旧照片修复页面专用样式 */
body.theme-restore {
    background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
    min-height: 100vh;
}

.restore-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.restore-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.restore-header {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.restore-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.restore-header h1 {
    position: relative;
    z-index: 1;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.restore-header p {
    position: relative;
    z-index: 1;
    font-size: 1.1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.restore-content {
    padding: 2.5rem;
}

/* 上传区域 */
.upload-section {
    margin-bottom: 2rem;
}

/* 现代化拖拽上传组件样式 */
.modern-upload-area {
    position: relative;
    border: 2px dashed #8B4513;
    border-radius: 12px;
    background: rgba(139, 69, 19, 0.05);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
    min-height: 200px;
}

.modern-upload-area:hover {
    border-color: #A0522D;
    background: rgba(139, 69, 19, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.1);
}

.modern-upload-area.drag-over {
    border-color: #8B4513 !important;
    background: rgba(139, 69, 19, 0.1) !important;
    transform: scale(1.02);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    height: 100%;
    min-height: 200px;
    cursor: pointer;
}

.upload-icon {
    font-size: 3rem;
    color: #8B4513;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.modern-upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: #A0522D;
}

.upload-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.upload-subtitle {
    color: #A0522D;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.upload-size {
    color: #A0522D;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.upload-preview {
    position: relative;
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-preview img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 8px;
    object-fit: contain;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
    border-radius: 8px;
}

.upload-preview:hover .preview-overlay {
    opacity: 1;
}

.preview-info {
    color: white;
}

.file-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    word-break: break-all;
}

.file-size {
    font-size: 0.8rem;
    opacity: 0.8;
    margin: 0;
}

.preview-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.preview-actions .btn {
    backdrop-filter: blur(10px);
    border-width: 1px;
}

/* 修复选项 */
.restore-options {
    margin-bottom: 2rem;
    display: none;
}

.options-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #8B4513;
    margin-bottom: 1rem;
    text-align: center;
}

.option-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.option-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.option-card:hover {
    border-color: #8B4513;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.15);
}

.option-card.selected {
    border-color: #8B4513;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1) 0%, rgba(160, 82, 45, 0.05) 100%);
}

.option-card .icon {
    font-size: 2.5rem;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.option-card .title {
    font-weight: 600;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.option-card .description {
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.4;
}

/* 修复按钮 */
.restore-button-container {
    text-align: center;
    margin-bottom: 2rem;
    display: none;
}

.restore-btn {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    border: none;
    color: white;
    padding: 1rem 3rem;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
    position: relative;
    overflow: hidden;
}

.restore-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(139, 69, 19, 0.4);
}

.restore-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.restore-btn .spinner {
    display: none;
}

.restore-btn.loading .spinner {
    display: inline-block;
    margin-right: 0.5rem;
}

.restore-btn.loading .btn-text {
    display: none;
}

/* 状态提示 */
.status-alert {
    margin-bottom: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    display: none;
}

.status-alert.success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.05) 100%);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #155724;
}

.status-alert.error {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(200, 35, 51, 0.05) 100%);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #721c24;
}

.status-alert.info {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1) 0%, rgba(160, 82, 45, 0.05) 100%);
    border: 1px solid rgba(139, 69, 19, 0.3);
    color: #8B4513;
}

/* 使用提示 */
.usage-tips {
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, rgba(160, 82, 45, 0.1) 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.tips-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #8B4513;
    margin-bottom: 1rem;
    text-align: center;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.tip-item {
    text-align: center;
}

.tip-icon {
    font-size: 2rem;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.tip-title {
    font-weight: 600;
    color: #8B4513;
    margin-bottom: 0.5rem;
}

.tip-description {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
}

/* 预处理选项样式 */
.preprocessing-option {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(40, 167, 69, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.preprocessing-option .form-check-label {
    font-size: 1rem;
    color: #333;
    cursor: pointer;
}

.preprocessing-description {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 对比结果样式 */
.comparison-result {
    margin-top: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.05) 0%, rgba(160, 82, 45, 0.1) 100%);
    border-radius: 20px;
    border: 1px solid rgba(139, 69, 19, 0.2);
}

.comparison-header {
    text-align: center;
    margin-bottom: 2rem;
}

.comparison-header h3 {
    color: #8B4513;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.comparison-header p {
    color: #A0522D;
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

/* 自适应尺寸的图像对比容器 */
.image-comparison {
    position: relative;
    width: 100%;
    height: 500px; /* 固定高度，可以通过JavaScript调整 */
    margin: 2rem 0;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
    user-select: none;
}

/* 修复前的图像 - 作为背景层 */
.img-before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1;
}

/* 修复后的图像 - 可裁剪层 */
.img-after {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 2;
    /* 初始状态：只显示左半部分 */
    clip-path: inset(0 50% 0 0);
}

/* 分割线 */
.divider {
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: white;
    z-index: 3;
    cursor: ew-resize;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transform: translateX(-50%);
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.divider::after {
    content: '⟷';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #8B4513;
    font-size: 1.2rem;
    font-weight: bold;
    z-index: 1;
}

.labels {
    position: absolute;
    top: 1rem;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    z-index: 4;
    pointer-events: none;
}

.label {
    background: rgba(139, 69, 19, 0.9);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 1rem;
}

.comparison-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin: 2rem 0 0 0;
}

.action-btn {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    border: none;
    color: white;
    padding: 0.8rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
    color: white;
    text-decoration: none;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.action-btn.secondary:hover {
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .restore-container {
        padding: 1rem;
    }
    
    .restore-content {
        padding: 1.5rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .restore-header h1 {
        font-size: 2rem;
    }
    
    .option-cards {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    let selectedFile = null;
    let selectedOption = 'advanced';
    let statusCheckInterval = null;
    let currentTaskId = null;
    

    
    // 修复选项配置
    const restoreOptions = {
        'advanced': {
            name: '深度修复',
            prompt: 'Professional vintage photo restoration: remove all damage including scratches, stains, tears, water damage, and fading. Enhance image clarity and sharpness, fix missing facial features or background details seamlessly, improve contrast and brightness, reduce noise and grain, enhance overall image definition. Maintain original color tone and historical authenticity while dramatically improving quality. High resolution, detailed restoration, preserve vintage character.'
        },
        'colorize': {
            name: '上色修复',
            prompt: 'Professional vintage photo restoration with colorization: remove all damage including scratches, stains, tears, water damage, and fading. Enhance image clarity and sharpness, fix missing facial features or background details seamlessly, improve contrast and brightness, reduce noise and grain. Add natural, historically accurate colors while maintaining the original mood and atmosphere. Professional colorization with realistic skin tones, natural hair colors, period-appropriate clothing colors, and authentic environmental colors. High quality, detailed, photorealistic restoration.'
        }
    };

    // 初始化现代化上传功能
    initModernUpload();

    // 现代化拖拽上传功能
    function initModernUpload() {
        const uploadArea = $('#uploadArea');
        const uploadContent = $('#uploadContent');
        const uploadPreview = $('#uploadPreview');
        const fileInput = $('#file-input');
        
        // 点击上传区域触发文件选择
        uploadContent.click(function() {
            fileInput.click();
        });
        
        // 浏览按钮点击事件
        $('#browseBtn').click(function(e) {
            e.stopPropagation();
            fileInput.click();
        });
        
        // 更换按钮点击事件
        $('#changeBtn').click(function() {
            fileInput.click();
        });
        
        // 移除按钮点击事件
        $('#removeBtn').click(function() {
            resetUploadArea();
            fileInput.val('');
            selectedFile = null;
            $('.restore-options').hide();
            $('.restore-button-container').hide();
            hideStatus();
            
            // 清理任务状态检查
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
            currentTaskId = null;
            resetButton();
        });
        
        // 拖拽事件处理
        uploadArea.on('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.addClass('drag-over');
        });
        
        uploadArea.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.removeClass('drag-over');
        });
        
        uploadArea.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.removeClass('drag-over');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (isValidImageFile(file)) {
                    fileInput[0].files = files;
                    handleFileSelect(file);
                } else {
                    showStatus('请选择有效的图像文件 (JPG, PNG, GIF, WebP)', 'error');
                }
            }
        });
    }

    // 文件输入框变化事件
    $('#file-input').change(function() {
        if (this.files && this.files[0]) {
            handleFileSelect(this.files[0]);
        }
    });

    // 处理文件选择
    function handleFileSelect(file) {
        if (!isValidImageFile(file)) {
            showStatus('请选择有效的图像文件 (JPG, PNG, GIF, WebP)', 'error');
            return;
        }
        
        // 验证文件大小 (10MB限制)
        if (file.size > 10 * 1024 * 1024) {
            showStatus('文件太大！请选择小于10MB的图像。', 'error');
            return;
        }

        selectedFile = file;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            showImagePreview(e.target.result, file);
        };
        reader.onerror = function() {
            showStatus('文件读取失败，请重试！', 'error');
        };
        reader.readAsDataURL(file);
    }

    // 显示图像预览
    function showImagePreview(src, file) {
        const uploadContent = $('#uploadContent');
        const uploadPreview = $('#uploadPreview');
        const previewImage = $('#previewImage');
        const fileName = $('#fileName');
        const fileSize = $('#fileSize');
        
        // 设置预览图像
        previewImage.attr('src', src);
        
        // 设置文件信息
        fileName.text(file.name);
        fileSize.text(formatFileSize(file.size));
        
        // 切换显示
        uploadContent.hide();
        uploadPreview.show();
        
        // 显示修复选项和按钮
        $('.restore-options').show();
        $('.restore-button-container').show();
        
        showStatus('照片上传成功！请选择修复类型。', 'success');
    }

    // 重置上传区域
    function resetUploadArea() {
        const uploadContent = $('#uploadContent');
        const uploadPreview = $('#uploadPreview');
        
        uploadPreview.hide();
        uploadContent.show();
    }

    // 验证图像文件
    function isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        return validTypes.includes(file.type);
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }





    // 修复选项选择
    $('.option-card').on('click', function() {
        $('.option-card').removeClass('selected');
        $(this).addClass('selected');
        selectedOption = $(this).data('option');
    });

    // 修复按钮
    $('#restore-btn').on('click', function() {
        if (!selectedFile) {
            showStatus('请先上传照片！', 'error');
            return;
        }

        // 根据选择的选项确定修复类型
        let restoreType = 'deep_restore'; // 默认深度修复
        if (selectedOption === 'colorize') {
            restoreType = 'colorize_restore';
        }

        const formData = new FormData();
        formData.append('image', selectedFile);
        formData.append('restore_type', restoreType);
        
        // 添加预处理选项
        const enablePreprocessing = $('#enablePreprocessing').is(':checked');
        formData.append('enable_preprocessing', enablePreprocessing ? 'true' : 'false');

        $(this).addClass('loading').prop('disabled', true);
        showStatus('正在分析照片并开始修复，请稍候...', 'info');

        $.ajax({
            url: '/api/restore',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.task_id) {
                    currentTaskId = response.task_id;
                    showStatus('照片已上传，AI正在进行专业修复...', 'info');
                    startStatusCheck();
                } else {
                    showStatus('修复任务创建失败：' + (response.error || '未知错误'), 'error');
                    resetButton();
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = '修复失败：网络错误或服务不可用';
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = '修复失败：' + xhr.responseJSON.error;
                }
                showStatus(errorMessage, 'error');
                resetButton();
            }
        });
    });

    // 开始状态检查
    function startStatusCheck() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
        
        statusCheckInterval = setInterval(function() {
            if (currentTaskId) {
                checkTaskStatus(currentTaskId);
            }
        }, 2000);
    }

    // 检查任务状态
    function checkTaskStatus(taskId) {
        $.get('/api/status/' + taskId)
            .done(function(response) {
                if (response.status === 'completed') {
                    clearInterval(statusCheckInterval);
                    statusCheckInterval = null;
                    currentTaskId = null;
                    showStatus('照片修复完成！正在跳转到对比页面...', 'success');
                    
                    setTimeout(function() {
                        // 显示嵌入式对比结果
                        const beforeImagePath = `/uploads/${response.uploaded_filename}`;
                        // 提取文件名部分，兼容Windows和Unix路径分隔符
                        const afterImageFilename = response.output_file.split(/[/\\]/).pop();
                        const afterImagePath = `/view/${afterImageFilename}`;
                        showComparisonResult(beforeImagePath, afterImagePath);
                    }, 1000);
                    
                } else if (response.status === 'failed') {
                    clearInterval(statusCheckInterval);
                    statusCheckInterval = null;
                    currentTaskId = null;
                    showStatus('修复失败：' + (response.error || '未知错误'), 'error');
                    resetButton();
                    
                } else {
                    // 更新进度信息
                    if (response.message) {
                        showStatus('修复进度：' + response.message, 'info');
                    }
                }
            })
            .fail(function() {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
                currentTaskId = null;
                showStatus('无法获取修复状态，请稍后查看结果', 'error');
                resetButton();
            });
    }

    // 重置按钮状态
    function resetButton() {
        $('#restore-btn').removeClass('loading').prop('disabled', false);
    }

    // 显示状态信息
    function showStatus(message, type) {
        const statusAlert = $('.status-alert');
        statusAlert.removeClass('success error info')
                  .addClass(type)
                  .html(`<i class="fas fa-${getStatusIcon(type)} me-2"></i>${message}`)
                  .show();
    }

    // 隐藏状态信息
    function hideStatus() {
        $('.status-alert').hide();
    }

    // 获取状态图标
    function getStatusIcon(type) {
        switch(type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-triangle';
            case 'info': return 'info-circle';
            default: return 'info-circle';
        }
    }

    // 默认选择深度修复
    $('.option-card[data-option="advanced"]').addClass('selected');
    selectedOption = 'advanced'; // 设置默认选择
    
    // 页面卸载时清理轮询
    $(window).on('beforeunload', function() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
    });

    // 对比功能相关变量
    let isDragging = false;
    let currentBeforeImage = '';
    let currentAfterImage = '';

    // 显示对比结果
    function showComparisonResult(beforeImagePath, afterImagePath) {
        currentBeforeImage = beforeImagePath;
        currentAfterImage = afterImagePath;
        
        // 隐藏上传区域和使用提示
        $('.upload-section, .restore-options, .restore-button-container, #usageTips').hide();
        
        // 设置图像背景
        $('#imgBefore').css('background-image', `url('${beforeImagePath}')`);
        $('#imgAfter').css('background-image', `url('${afterImagePath}')`);
        
        // 显示对比结果区域
        $('#comparisonResult').show();
        
        // 初始化对比交互
        initImageComparison();
        
        // 滚动到对比区域
        setTimeout(() => {
            $('#comparisonResult')[0].scrollIntoView({ behavior: 'smooth' });
        }, 300);
        
        resetButton();
    }

    // 初始化图像对比交互
    function initImageComparison() {
        const divider = $('#divider');
        const imgAfter = $('#imgAfter');
        const comparison = $('#imageComparison');
        
        // 鼠标按下事件
        divider.on('mousedown', function(e) {
            isDragging = true;
            e.preventDefault();
            $('body').css('cursor', 'ew-resize');
        });
        
        // 鼠标移动事件
        $(document).on('mousemove', function(e) {
            if (!isDragging) return;
            
            const rect = comparison[0].getBoundingClientRect();
            const x = e.clientX - rect.left;
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
            
            // 更新分割线位置
            divider.css('left', percentage + '%');
            
            // 更新裁剪路径
            imgAfter.css('clip-path', `inset(0 ${100 - percentage}% 0 0)`);
        });
        
        // 鼠标松开事件
        $(document).on('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                $('body').css('cursor', 'default');
            }
        });
        
        // 触摸事件支持（移动端）
        divider.on('touchstart', function(e) {
            isDragging = true;
            e.preventDefault();
        });
        
        $(document).on('touchmove', function(e) {
            if (!isDragging) return;
            
            const touch = e.originalEvent.touches[0];
            const rect = comparison[0].getBoundingClientRect();
            const x = touch.clientX - rect.left;
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
            
            divider.css('left', percentage + '%');
            imgAfter.css('clip-path', `inset(0 ${100 - percentage}% 0 0)`);
        });
        
        $(document).on('touchend', function() {
            isDragging = false;
        });
    }

    // 下载修复后图片
    $('#downloadAfterBtn').on('click', function() {
        if (currentAfterImage) {
            const link = document.createElement('a');
            link.href = currentAfterImage;
            link.download = `restored_photo_${Date.now()}.jpg`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    });

    // 全屏对比
    $('#fullCompareBtn').on('click', function() {
        if (currentBeforeImage && currentAfterImage) {
            const compareUrl = `/compare?before=${encodeURIComponent(currentBeforeImage)}&after=${encodeURIComponent(currentAfterImage)}`;
            window.open(compareUrl, '_blank');
        }
    });

    // 修复新照片
    $('#newRestoreBtn').on('click', function() {
        // 重置所有状态
        selectedFile = null;
        selectedOption = 'advanced';
        currentBeforeImage = '';
        currentAfterImage = '';
        
        // 清理任务状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
        currentTaskId = null;
        
        // 重置文件输入
        $('#file-input').val('');
        resetUploadArea();
        
        // 显示上传区域和使用提示
        $('.upload-section, #usageTips, #testSection').show();
        $('.restore-options, .restore-button-container').hide();
        $('#comparisonResult').hide();
        
        // 隐藏状态提示
        hideStatus();
        
        // 重置按钮状态
        resetButton();
        
        // 重置选项选择
        $('.option-card').removeClass('selected');
        $('.option-card[data-option="advanced"]').addClass('selected');
        
        // 滚动到顶部
        $('html, body').animate({ scrollTop: 0 }, 500);
    });

    // 测试对比功能按钮（临时演示用）
    $('#testCompareBtn').on('click', function() {
        // 使用本地示例图片进行演示
        const beforeImage = '/outputs/test_precise_aspect.jpg';
        const afterImage = '/outputs/test_precise_result.jpg';
        
        showComparisonResult(beforeImage, afterImage);
        
        // 隐藏测试按钮
        $('#testSection').hide();
    });
});
</script>
{% endblock %}

{% block content %}
<div class="restore-container">
    <div class="restore-card">
        <!-- 页面头部 -->
        <div class="restore-header">
            <h1><i class="fas fa-history me-3"></i>旧照片修复</h1>
            <p>让珍贵的回忆重焕光彩，AI智能修复您的老照片</p>
        </div>

        <div class="restore-content">
            <!-- 状态提示 -->
            <div class="status-alert"></div>

            <!-- 上传区域 -->
            <div class="upload-section">
                <div class="modern-upload-area" id="uploadArea">
                    <input type="file" id="file-input" name="image" accept="image/*" required style="display: none;">
                    <div class="upload-content" id="uploadContent">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <h6 class="upload-title">拖拽照片到这里或点击上传</h6>
                            <p class="upload-subtitle">支持 JPG、PNG、GIF 格式</p>
                            <p class="upload-size">最大 10MB</p>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="browseBtn">
                            <i class="fas fa-folder-open"></i> 选择文件
                        </button>
                    </div>
                    <div class="upload-preview" style="display: none;">
                        <img class="preview-image" src="#" alt="预览图像">
                        <div class="preview-overlay">
                            <div class="preview-info">
                                <p class="file-name">filename.jpg</p>
                                <p class="file-size">1.2 MB</p>
                            </div>
                            <div class="preview-actions">
                                <button type="button" class="btn btn-sm btn-outline-light change-btn">
                                    <i class="fas fa-sync"></i> 更换
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger remove-btn">
                                    <i class="fas fa-trash"></i> 移除
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 修复选项 -->
            <div class="restore-options">
                <h5 class="options-title">选择修复选项</h5>
                <div class="option-cards">
                    <div class="option-card" data-option="restore">
                        <i class="fas fa-magic"></i>
                        <h6>智能修复</h6>
                        <p>综合修复划痕、折痕和噪点</p>
                    </div>
                    <div class="option-card" data-option="colorize">
                        <i class="fas fa-palette"></i>
                        <h6>智能上色</h6>
                        <p>为黑白照片添加自然色彩</p>
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="submit-section text-center">
                <button type="submit" class="btn btn-restore" disabled>
                    <i class="fas fa-hammer"></i> 开始修复
                </button>
            </div>

            <!-- 对比结果区域 -->
            <div class="comparison-result" id="comparisonResult" style="display: none;">
                <div class="comparison-header">
                    <h3><i class="fas fa-eye me-2"></i>修复效果对比</h3>
                    <p>拖动中间的分割线查看修复前后的效果对比</p>
                </div>
                
                <!-- 图像对比容器 -->
                <div class="image-comparison" id="imageComparison">
                    <div class="img-before" id="imgBefore"></div>
                    <div class="img-after" id="imgAfter"></div>
                    <div class="divider" id="divider"></div>
                    <div class="labels">
                        <div class="label">修复前</div>
                        <div class="label">修复后</div>
                    </div>
                </div>
                
                <!-- 对比操作按钮 -->
                <div class="comparison-actions">
                    <button type="button" class="action-btn" id="downloadAfterBtn">
                        <i class="fas fa-download"></i> 下载修复后图片
                    </button>
                    <button type="button" class="action-btn secondary" id="fullCompareBtn">
                        <i class="fas fa-expand"></i> 全屏对比
                    </button>
                    <button type="button" class="action-btn secondary" id="newRestoreBtn">
                        <i class="fas fa-plus"></i> 修复新照片
                    </button>
                </div>
            </div>

            <!-- 测试按钮（临时用于演示对比功能） -->
            <div class="text-center mb-4" id="testSection">
                <button type="button" class="btn btn-outline-secondary" id="testCompareBtn">
                    <i class="fas fa-eye"></i> 测试对比功能（演示用）
                </button>
            </div>

            <!-- 使用提示 -->
            <div class="usage-tips" id="usageTips">
                <div class="tips-title">
                    <i class="fas fa-lightbulb me-2"></i>使用提示
                </div>
                <div class="tips-grid">
                    <div class="tip-item">
                        <div class="tip-icon"><i class="fas fa-image"></i></div>
                        <div class="tip-title">照片要求</div>
                        <div class="tip-description">支持各种老照片格式，包括发黄、褪色、有划痕的历史照片</div>
                    </div>
                    <div class="tip-item">
                        <div class="tip-icon"><i class="fas fa-clock"></i></div>
                        <div class="tip-title">处理时间</div>
                        <div class="tip-description">根据照片复杂度，修复过程通常需要30秒到2分钟</div>
                    </div>
                    <div class="tip-item">
                        <div class="tip-icon"><i class="fas fa-star"></i></div>
                        <div class="tip-title">最佳效果</div>
                        <div class="tip-description">清晰度较高的原始照片能获得更好的修复效果</div>
                    </div>
                    <div class="tip-item">
                        <div class="tip-icon"><i class="fas fa-download"></i></div>
                        <div class="tip-title">保存结果</div>
                        <div class="tip-description">修复完成后可以下载高质量的修复照片</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 