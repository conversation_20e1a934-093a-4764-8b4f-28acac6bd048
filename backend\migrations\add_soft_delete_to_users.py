"""
添加用户软删除字段的数据库迁移
"""
import sqlite3
from datetime import datetime
import os

def migrate_add_soft_delete():
    """添加软删除字段到用户表"""
    db_path = os.path.join(os.path.dirname(__file__), '..', 'instance', 'bfl_app.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(users)")
        columns = [col[1] for col in cursor.fetchall()]
        
        # 添加 is_deleted 字段
        if 'is_deleted' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN is_deleted BOOLEAN DEFAULT 0 NOT NULL")
            print("✓ 添加 is_deleted 字段成功")
        else:
            print("- is_deleted 字段已存在")
        
        # 添加 deleted_at 字段
        if 'deleted_at' not in columns:
            cursor.execute("ALTER TABLE users ADD COLUMN deleted_at DATETIME")
            print("✓ 添加 deleted_at 字段成功")
        else:
            print("- deleted_at 字段已存在")
        
        conn.commit()
        print("✓ 用户软删除字段迁移完成")
        
    except Exception as e:
        print(f"✗ 迁移失败: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    migrate_add_soft_delete()
