#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置模块
"""

import os
from datetime import timedelta

class AppConfig:
    """应用配置类"""

    # 基础配置
    DEBUG = True
    HOST = '127.0.0.1'
    PORT = 5000
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')

    # 目录配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # backend目录
    PROJECT_ROOT = os.path.dirname(BASE_DIR)  # 项目根目录
    
    # 数据库配置
    _instance_dir = os.path.join(PROJECT_ROOT, 'instance')
    _db_path = os.path.join(_instance_dir, 'bfl_app.db')
    DATABASE_URL = os.getenv('DATABASE_URL', f'sqlite:///{_db_path}')
    DATABASE_ENABLED = os.getenv('DATABASE_ENABLED', 'true').lower() == 'true'
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }

    # JWT配置
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    JWT_CSRF_PROTECT = False  # 禁用CSRF保护

    # 用户系统配置
    USER_SYSTEM_ENABLED = os.getenv('USER_SYSTEM_ENABLED', 'true').lower() == 'true'
    REGISTRATION_ENABLED = os.getenv('REGISTRATION_ENABLED', 'true').lower() == 'true'
    EMAIL_VERIFICATION_REQUIRED = os.getenv('EMAIL_VERIFICATION_REQUIRED', 'false').lower() == 'true'
    
    # 大模型配置
    DEEPSEEK_MODEL = 'deepseek-chat'
    OLLAMA_MODEL = 'qwen3:4b'
    NO_THINK = True  # 控制是否在Ollama提示词后添加/no_think
    UPLOAD_FOLDER = os.path.join(PROJECT_ROOT, 'uploads')
    OUTPUT_FOLDER = os.path.join(PROJECT_ROOT, 'outputs')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 文件类型配置
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    
    # BFL API配置
    BFL_API_KEY = os.getenv('BFL_API_KEY', 'b915d133-2d74-411c-a531-2dfc56474223')

    # 默认模型配置
    DEFAULT_MODELS = {
        'generate': 'flux-kontext-pro',
        'edit': 'flux-kontext-pro',
        'style': 'flux-kontext-pro'
    }

    # 积分系统配置
    CREDIT_COSTS = {
        'generate': 1,
        'edit': 2,
        'style': 3,
        'high_res_bonus': 1,  # 高分辨率额外消费
        'commercial_multiplier': 3  # 商业授权倍数
    }

    # 模型积分倍数
    MODEL_MULTIPLIERS = {
        'flux-dev': 0.8,
        'flux-pro': 1.0,
        'flux-kontext-pro': 1.5,
        'flux-kontext-max': 2.0,
        'flux-pro-1.1-ultra': 3.0
    }

    # 用户限制配置
    USER_LIMITS = {
        'free': {'daily': 5, 'monthly': 150, 'credits': 10},
        'premium': {'daily': 50, 'monthly': 1500, 'credits': 100},
        'pro': {'daily': 200, 'monthly': 6000, 'credits': 500},
        'enterprise': {'daily': -1, 'monthly': -1, 'credits': 1000}  # -1 表示无限制
    }

    # 支付系统配置
    PAYMENT_ENABLED = os.getenv('PAYMENT_ENABLED', 'false').lower() == 'true'
    STRIPE_PUBLISHABLE_KEY = os.getenv('STRIPE_PUBLISHABLE_KEY')
    STRIPE_SECRET_KEY = os.getenv('STRIPE_SECRET_KEY')
    STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET')

    # 邮件配置
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', '<EMAIL>')
    
    # 翻译服务配置 - 纯配置文件管理
    TRANSLATION_CONFIG = {
        # 主服务选择：'deepseek' 或 'ollama'
        'primary': 'ollama',
        
        # 提示词模板
        'prompts': {
            'translation': """你是一个专业的AI图像生成提示词翻译专家。请将以下中文提示词翻译成英文，要求：\n\n1. 保持原意准确，适合AI图像生成\n2. 使用专业的艺术和摄影术语\n3. 保持描述的生动性和具体性\n4. 如果有艺术风格、技法等专业词汇，请使用准确的英文对应词\n5. 只返回翻译结果，不要添加其他解释\n\n中文提示词：{chinese_prompt}\n\n英文翻译：""",
            'polish': """你是一名精通人工智能绘图的数字艺术家。现在有如下的文字描述，请根据你的理解在原语义的基础上补充更多细节，以便能方便人工智能更好的理解并绘制出优质的图像画面。\n\n要求：\n1. 保持原始语义和主题不变\n2. 添加更多视觉细节描述（光线、色彩、构图、材质等）\n3. 不需要补充艺术风格和技法描述\n4. 用语言增强画面的层次感和氛围感\n5. 使用专业的艺术和摄影术语\n6. 只返回润色后的结果，不要添加其他解释\n\n原始描述：{original_text}\n\n润色后的描述："""
        },
        
        # DeepSeek 配置
        'deepseek': {
            'api_key': '***********************************',
            'api_url': 'https://api.deepseek.com/v1/chat/completions', 
            'model': DEEPSEEK_MODEL,
            'timeout': 30
        },
        
        # Ollama 配置
        'ollama': {
            'api_url': 'http://localhost:11434',
            'model': OLLAMA_MODEL,
            'timeout': 30
        }
    }
    
    @classmethod
    def init_directories(cls):
        """初始化必要的目录"""
        os.makedirs(cls.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(cls.OUTPUT_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(cls.BASE_DIR, 'static'), exist_ok=True)
        os.makedirs(os.path.join(cls.BASE_DIR, 'templates'), exist_ok=True)
    
    @classmethod
    def get_upload_path(cls, filename):
        """获取上传文件的完整路径"""
        return os.path.join(cls.UPLOAD_FOLDER, filename)
    
    @classmethod
    def get_output_path(cls, filename):
        """获取输出文件的完整路径"""
        return os.path.join(cls.OUTPUT_FOLDER, filename)
    
    @classmethod
    def set_translation_primary(cls, service: str):
        """
        设置主翻译服务
        Args:
            service: 'deepseek' 或 'ollama'
        """
        if service not in ['deepseek', 'ollama']:
            raise ValueError("服务必须是 'deepseek' 或 'ollama'")
        cls.TRANSLATION_CONFIG['primary'] = service
    
    @classmethod
    def get_translation_primary(cls):
        """获取当前主翻译服务"""
        return cls.TRANSLATION_CONFIG.get('primary', 'deepseek')
    
    @classmethod
    def set_no_think(cls, enabled: bool):
        """
        设置是否在Ollama提示词后添加/no_think
        Args:
            enabled: True表示添加/no_think，False表示不添加
        """
        cls.NO_THINK = enabled
        # 更新提示词
        for key in ['translation', 'polish']:
            base_prompt = cls.TRANSLATION_CONFIG['prompts'][key].rstrip('/no_think')
            cls.TRANSLATION_CONFIG['prompts'][key] = base_prompt + ('/no_think' if enabled else '')

# ===============================================
# 翻译服务配置说明
# ===============================================
"""
翻译服务配置说明：

1. 主服务切换：
   只需修改 TRANSLATION_CONFIG['primary'] 的值：
   - 'deepseek' : 优先使用 DeepSeek API
   - 'ollama'   : 优先使用本地 Ollama

2. 服务参数配置：
   在对应的 'deepseek' 或 'ollama' 块中修改参数

3. 自动备份机制：
   主服务不可用时会自动切换到备用服务

4. NO_THINK开关：
   控制是否在Ollama提示词后添加/no_think后缀
   - True: 添加/no_think（禁用思考）
   - False: 不添加（允许思考）
   可通过set_no_think()方法动态切换

使用示例：
   # 切换到 Ollama 优先
   AppConfig.TRANSLATION_CONFIG['primary'] = 'ollama'
   
   # 或使用方法
   AppConfig.set_translation_primary('ollama')
   
   # 切换no_think状态
   AppConfig.set_no_think(True)  # 启用no_think
   AppConfig.set_no_think(False) # 禁用no_think
"""