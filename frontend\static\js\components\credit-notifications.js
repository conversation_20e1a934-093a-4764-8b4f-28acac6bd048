/**
 * 积分通知系统
 * 处理积分不足警告、每日限制提醒等
 */

class CreditNotificationSystem {
    constructor() {
        this.currentUser = null;
        this.notifications = [];
        this.settings = {
            lowCreditThreshold: 5,    // 低积分警告阈值
            criticalCreditThreshold: 1,  // 危险积分警告阈值
            dailyLimitWarningPercent: 0.8, // 每日限制警告百分比
            enableSound: true,
            enableBrowserNotification: true
        };
        
        this.init();
    }
    
    init() {
        this.createNotificationContainer();
        this.loadSettings();
        this.requestNotificationPermission();
        
        // 定期检查积分状态
        setInterval(() => this.checkCreditStatus(), 30000); // 30秒检查一次
    }
    
    createNotificationContainer() {
        // 创建通知容器
        if (!document.getElementById('credit-notifications')) {
            const container = document.createElement('div');
            container.id = 'credit-notifications';
            container.className = 'credit-notification-container';
            container.innerHTML = `
                <style>
                    .credit-notification-container {
                        position: fixed;
                        top: 80px;
                        right: 20px;
                        width: 350px;
                        z-index: 9999;
                        pointer-events: none;
                    }
                    
                    .credit-notification {
                        background: white;
                        border-radius: 10px;
                        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
                        margin-bottom: 10px;
                        padding: 15px;
                        border-left: 4px solid #007bff;
                        animation: slideInRight 0.3s ease;
                        pointer-events: auto;
                        position: relative;
                    }
                    
                    .credit-notification.warning {
                        border-left-color: #ffc107;
                    }
                    
                    .credit-notification.danger {
                        border-left-color: #dc3545;
                    }
                    
                    .credit-notification.success {
                        border-left-color: #28a745;
                    }
                    
                    .credit-notification.info {
                        border-left-color: #17a2b8;
                    }
                    
                    .credit-notification-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 8px;
                    }
                    
                    .credit-notification-icon {
                        font-size: 20px;
                        margin-right: 8px;
                    }
                    
                    .credit-notification-title {
                        font-weight: bold;
                        font-size: 14px;
                        margin: 0;
                        flex-grow: 1;
                    }
                    
                    .credit-notification-close {
                        background: none;
                        border: none;
                        font-size: 18px;
                        cursor: pointer;
                        color: #999;
                        padding: 0;
                        width: 20px;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .credit-notification-body {
                        font-size: 13px;
                        color: #666;
                        line-height: 1.4;
                    }
                    
                    .credit-notification-actions {
                        margin-top: 10px;
                        display: flex;
                        gap: 8px;
                    }
                    
                    .credit-notification-btn {
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 5px 10px;
                        border-radius: 5px;
                        font-size: 12px;
                        cursor: pointer;
                        text-decoration: none;
                        display: inline-block;
                    }
                    
                    .credit-notification-btn.secondary {
                        background: #6c757d;
                    }
                    
                    .credit-notification-btn:hover {
                        opacity: 0.8;
                    }
                    
                    @keyframes slideInRight {
                        from {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                        to {
                            transform: translateX(0);
                            opacity: 1;
                        }
                    }
                    
                    @keyframes slideOutRight {
                        from {
                            transform: translateX(0);
                            opacity: 1;
                        }
                        to {
                            transform: translateX(100%);
                            opacity: 0;
                        }
                    }
                    
                    .credit-notification.removing {
                        animation: slideOutRight 0.3s ease forwards;
                    }
                    
                    .credit-progress-bar {
                        background: #e9ecef;
                        border-radius: 10px;
                        height: 6px;
                        margin-top: 8px;
                        overflow: hidden;
                    }
                    
                    .credit-progress-fill {
                        height: 100%;
                        transition: width 0.3s ease;
                        border-radius: 10px;
                    }
                    
                    .credit-progress-fill.success {
                        background: #28a745;
                    }
                    
                    .credit-progress-fill.warning {
                        background: #ffc107;
                    }
                    
                    .credit-progress-fill.danger {
                        background: #dc3545;
                    }
                </style>
            `;
            document.body.appendChild(container);
        }
    }
    
    async loadSettings() {
        try {
            const saved = localStorage.getItem('creditNotificationSettings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
        } catch (error) {
            console.error('加载通知设置失败:', error);
        }
    }
    
    saveSettings() {
        localStorage.setItem('creditNotificationSettings', JSON.stringify(this.settings));
    }
    
    async requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            await Notification.requestPermission();
        }
    }
    
    async checkCreditStatus() {
        try {
            const response = await apiClient.get('/api/user/profile');
            if (response.success) {
                this.currentUser = response.user;
                this.evaluateCreditStatus();
            }
        } catch (error) {
            // 用户未登录或其他错误，静默处理
        }
    }
    
    evaluateCreditStatus() {
        if (!this.currentUser) return;
        
        const credits = this.currentUser.available_credits;
        const dailyUsed = this.currentUser.daily_used;
        const dailyLimit = this.currentUser.daily_limit;
        
        // 检查积分不足
        if (credits <= this.settings.criticalCreditThreshold) {
            this.showCriticalCreditWarning(credits);
        } else if (credits <= this.settings.lowCreditThreshold) {
            this.showLowCreditWarning(credits);
        }
        
        // 检查每日限制
        if (dailyLimit > 0) {
            const usagePercent = dailyUsed / dailyLimit;
            if (usagePercent >= this.settings.dailyLimitWarningPercent) {
                this.showDailyLimitWarning(dailyUsed, dailyLimit);
            }
        }
    }
    
    showLowCreditWarning(credits) {
        // 避免重复显示相同警告
        if (this.hasActiveNotification('low-credit')) return;
        
        this.showNotification({
            id: 'low-credit',
            type: 'warning',
            icon: '⚠️',
            title: '积分余额不足',
            message: `您当前还有 ${credits} 积分，建议及时充值以免影响使用。`,
            actions: [
                {
                    text: '立即充值',
                    href: '/credits',
                    class: 'primary'
                },
                {
                    text: '稍后提醒',
                    action: () => this.snoozeNotification('low-credit', 3600000) // 1小时后再提醒
                }
            ],
            autoClose: 10000
        });
    }
    
    showCriticalCreditWarning(credits) {
        if (this.hasActiveNotification('critical-credit')) return;
        
        this.showNotification({
            id: 'critical-credit',
            type: 'danger',
            icon: '🚨',
            title: '积分严重不足！',
            message: `您只剩下 ${credits} 积分，可能无法完成下一个任务。`,
            actions: [
                {
                    text: '紧急充值',
                    href: '/credits',
                    class: 'primary'
                }
            ],
            persistent: true, // 不自动关闭
            sound: true
        });
        
        // 发送浏览器通知
        this.sendBrowserNotification('积分不足警告', `您只剩下 ${credits} 积分`);
    }
    
    showDailyLimitWarning(used, limit) {
        if (this.hasActiveNotification('daily-limit')) return;
        
        const remaining = limit - used;
        const usagePercent = Math.round((used / limit) * 100);
        
        this.showNotification({
            id: 'daily-limit',
            type: 'info',
            icon: '📊',
            title: '每日使用量提醒',
            message: `今日已使用 ${used}/${limit} 次（${usagePercent}%），还可使用 ${remaining} 次。`,
            progress: {
                current: used,
                total: limit,
                type: usagePercent >= 90 ? 'danger' : usagePercent >= 80 ? 'warning' : 'success'
            },
            actions: [
                {
                    text: '升级账户',
                    href: '/profile/subscription',
                    class: 'primary'
                }
            ],
            autoClose: 8000
        });
    }
    
    showTaskCompletionReward(credits, taskType) {
        this.showNotification({
            id: `task-reward-${Date.now()}`,
            type: 'success',
            icon: '🎉',
            title: '任务完成！',
            message: `${taskType}任务已完成，消费了 ${credits} 积分。`,
            autoClose: 5000
        });
    }
    
    showPurchaseSuccess(credits, packageName) {
        this.showNotification({
            id: `purchase-success-${Date.now()}`,
            type: 'success',
            icon: '💰',
            title: '充值成功！',
            message: `成功购买 ${packageName}，获得 ${credits} 积分。`,
            autoClose: 5000
        });
        
        this.sendBrowserNotification('充值成功', `获得 ${credits} 积分`);
    }
    
    showDailyCheckinReward(credits, consecutiveDays) {
        this.showNotification({
            id: `checkin-reward-${Date.now()}`,
            type: 'success',
            icon: '📅',
            title: '签到成功！',
            message: `连续签到 ${consecutiveDays} 天，获得 ${credits} 积分奖励。`,
            autoClose: 5000
        });
    }
    
    showReferralReward(credits, referredUser) {
        this.showNotification({
            id: `referral-reward-${Date.now()}`,
            type: 'success',
            icon: '👥',
            title: '推荐奖励！',
            message: `好友 ${referredUser} 注册成功，您获得 ${credits} 积分奖励。`,
            autoClose: 8000
        });
    }
    
    showNotification(options) {
        const container = document.getElementById('credit-notifications');
        if (!container) return;
        
        // 检查是否已存在相同ID的通知
        const existing = container.querySelector(`[data-notification-id="${options.id}"]`);
        if (existing) {
            existing.remove();
        }
        
        const notification = document.createElement('div');
        notification.className = `credit-notification ${options.type || 'info'}`;
        notification.setAttribute('data-notification-id', options.id);
        
        let progressBar = '';
        if (options.progress) {
            const percent = Math.round((options.progress.current / options.progress.total) * 100);
            progressBar = `
                <div class="credit-progress-bar">
                    <div class="credit-progress-fill ${options.progress.type}" style="width: ${percent}%"></div>
                </div>
            `;
        }
        
        let actions = '';
        if (options.actions) {
            actions = `
                <div class="credit-notification-actions">
                    ${options.actions.map(action => {
                        if (action.href) {
                            return `<a href="${action.href}" class="credit-notification-btn ${action.class || ''}">${action.text}</a>`;
                        } else {
                            return `<button class="credit-notification-btn ${action.class || ''}" data-action="${options.actions.indexOf(action)}">${action.text}</button>`;
                        }
                    }).join('')}
                </div>
            `;
        }
        
        notification.innerHTML = `
            <div class="credit-notification-header">
                <div style="display: flex; align-items: center;">
                    <span class="credit-notification-icon">${options.icon || 'ℹ️'}</span>
                    <h6 class="credit-notification-title">${options.title}</h6>
                </div>
                <button class="credit-notification-close">&times;</button>
            </div>
            <div class="credit-notification-body">
                ${options.message}
                ${progressBar}
            </div>
            ${actions}
        `;
        
        // 添加事件监听器
        notification.querySelector('.credit-notification-close').addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // 处理操作按钮
        if (options.actions) {
            notification.querySelectorAll('[data-action]').forEach(btn => {
                btn.addEventListener('click', () => {
                    const actionIndex = parseInt(btn.getAttribute('data-action'));
                    const action = options.actions[actionIndex];
                    if (action.action) {
                        action.action();
                    }
                    this.removeNotification(notification);
                });
            });
        }
        
        container.appendChild(notification);
        
        // 播放提示音
        if (options.sound && this.settings.enableSound) {
            this.playNotificationSound();
        }
        
        // 自动关闭
        if (options.autoClose && !options.persistent) {
            setTimeout(() => {
                if (notification.parentNode) {
                    this.removeNotification(notification);
                }
            }, options.autoClose);
        }
        
        // 记录通知
        this.notifications.push({
            id: options.id,
            element: notification,
            timestamp: Date.now()
        });
    }
    
    removeNotification(element) {
        element.classList.add('removing');
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            // 从记录中移除
            this.notifications = this.notifications.filter(n => n.element !== element);
        }, 300);
    }
    
    hasActiveNotification(id) {
        return this.notifications.some(n => n.id === id && n.element.parentNode);
    }
    
    snoozeNotification(id, duration) {
        // 移除当前通知
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            this.removeNotification(notification.element);
        }
        
        // 设置稍后提醒
        setTimeout(() => {
            this.checkCreditStatus(); // 重新评估状态
        }, duration);
    }
    
    sendBrowserNotification(title, body) {
        if (!this.settings.enableBrowserNotification) return;
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                body: body,
                icon: '/static/img/logo.png', // 假设有logo
                tag: 'credit-notification' // 防止重复通知
            });
        }
    }
    
    playNotificationSound() {
        // 创建简单的提示音
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }
    
    // 清除所有通知
    clearAll() {
        const container = document.getElementById('credit-notifications');
        if (container) {
            container.innerHTML = '';
        }
        this.notifications = [];
    }
    
    // 更新设置
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }
    
    // 手动触发状态检查
    refresh() {
        this.checkCreditStatus();
    }
}

// 全局实例
window.creditNotifications = new CreditNotificationSystem();

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CreditNotificationSystem;
} 