"""
用户相关路由
"""
from flask import Blueprint, request, jsonify, current_app
# JWT imports removed - using simple auth system
from datetime import timedelta
import time

# Old auth system imports removed
from backend.routes.simple_auth_routes import require_auth, get_current_user
from backend.models.database import db
from backend.models.user import User, UserProfile
from backend.models.credit import CreditsTransaction
from backend.models.task import Task
from backend.services.credit_service import credit_service
from backend.utils.validators import validate_text_input
from backend.utils.helpers import create_response


user_bp = Blueprint('user', __name__, url_prefix='/api/user')


def serialize_user(user):
    """序列化用户信息"""
    if not user:
        return None
    
    return {
        'id': user.id,
        'username': user.username,
        'phone': user.phone,
        'email': user.email,
        'is_active': user.is_active,
        'is_premium': user.is_premium,
        'created_at': user.created_at.isoformat() if user.created_at else None,
        'profile': {
            'avatar_url': user.profile.avatar_url if user.profile else None,
            'bio': user.profile.bio if user.profile else None,
            'preferences': user.profile.preferences if user.profile else {}
        } if hasattr(user, 'profile') and user.profile else None
    }


@user_bp.route('/register', methods=['POST'])
def register():
    """用户注册 - 手机号为主要标识"""
    try:
        data = request.get_json()
        print(f"注册请求数据: {data}")  # 调试信息

        phone = data.get('phone', '').strip()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')

        print(f"解析后数据: phone={phone}, username={username}, email={email}, password={'*' * len(password) if password else 'None'}")  # 调试信息

        # 基本验证 - 手机号和密码是必需的
        if not phone:
            print("基本验证失败: 手机号为空")  # 调试信息
            return jsonify({'success': False, 'message': '请填写手机号'}), 400

        if not password:
            print("基本验证失败: 密码为空")  # 调试信息
            return jsonify({'success': False, 'message': '请填写密码'}), 400

        # 验证手机号格式
        import re
        phone_pattern = r'^(\+86)?1[3-9]\d{9}$'  # 中国手机号格式
        if not re.match(phone_pattern, phone):
            return jsonify({'success': False, 'message': '手机号格式不正确'}), 400

        # 验证用户名（如果提供）
        if username and (len(username) < 3 or len(username) > 20):
            return jsonify({'success': False, 'message': '用户名长度应在3-20字符之间'}), 400

        # 验证邮箱格式（如果提供）
        if email:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return jsonify({'success': False, 'message': '邮箱格式不正确'}), 400

        # 检查手机号是否已存在
        existing_user = User.query.filter_by(phone=phone).first()
        if existing_user:
            return jsonify({'success': False, 'message': '手机号已被注册'}), 400
            
        # 检查邮箱是否已存在（如果提供）
        if email:
            existing_email = User.query.filter_by(email=email).first()
            if existing_email:
                return jsonify({'success': False, 'message': '邮箱已被注册'}), 400
        
        # 创建新用户
        from werkzeug.security import generate_password_hash
        new_user = User(
            phone=phone,
            username=username or f"用户{phone[-4:]}",  # 如果没有用户名，使用手机号后4位
            email=email,
            password_hash=generate_password_hash(password)
        )
        
        try:
            db.session.add(new_user)
            db.session.commit()
            
            # 创建用户档案
            profile = UserProfile(user_id=new_user.id)
            db.session.add(profile)
            db.session.commit()
            
            return jsonify({
                'success': True, 
                'message': '注册成功',
                'user_id': new_user.id
            }), 200
            
        except Exception as db_error:
            db.session.rollback()
            print(f"数据库操作失败: {db_error}")
            return jsonify({'success': False, 'message': '注册失败，请重试'}), 500
        
    except Exception as e:
        print(f"注册异常: {e}")  # 调试信息
        import traceback
        print(f"注册异常堆栈: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': '注册失败，请重试'}), 500


# 旧的登录端点已被新的简化认证系统替代
# 请使用 /api/auth/login 进行登录
# @user_bp.route('/login', methods=['POST'])
# def login():
#     """用户登录 - 已废弃，请使用 /api/auth/login"""
#     return jsonify({'success': False, 'message': '此端点已废弃，请使用 /api/auth/login'}), 410


@user_bp.route('/check-phone', methods=['POST'])
def check_phone():
    """检查手机号是否可用"""
    try:
        data = request.get_json()
        phone = data.get('phone', '').strip()

        if not phone:
            return jsonify({'available': False, 'message': '手机号不能为空'}), 400

        # 检查手机号是否已存在
        existing_user = User.query.filter_by(phone=phone).first()
        available = existing_user is None

        return jsonify({
            'available': available,
            'message': '手机号可用' if available else '手机号已被注册'
        })

    except Exception as e:
        print(f"检查手机号异常: {e}")
        return jsonify({'available': False, 'message': '检查失败'}), 500


@user_bp.route('/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出"""
    # JWT是无状态的，客户端删除令牌即可
    # 这里可以添加令牌黑名单功能
    response = jsonify({'success': True, 'message': '登出成功'})

    # 清除Cookie中的token
    response.set_cookie('access_token', '', expires=0)
    response.set_cookie('refresh_token', '', expires=0)

    return response


@user_bp.route('/refresh', methods=['POST'])
def refresh():
    """刷新访问令牌 - 支持多种token来源"""
    try:
        from flask import request
        from flask_jwt_extended import verify_jwt_in_request, get_jwt_identity, create_access_token

        # 尝试从多个位置获取refresh token
        refresh_token = None

        # 1. 从Authorization头获取
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token_value = auth_header.split(' ')[1]
            if token_value and token_value.lower() != 'null':
                refresh_token = token_value

        # 2. 从Cookie获取
        if not refresh_token:
            cookie_token = request.cookies.get('refresh_token')
            if cookie_token and cookie_token.lower() != 'null':
                refresh_token = cookie_token

        # 3. 从请求体获取
        if not refresh_token:
            data = request.get_json() or {}
            body_token = data.get('refresh_token')
            if body_token and body_token.lower() != 'null':
                refresh_token = body_token

        if not refresh_token:
            return jsonify({
                'success': False,
                'message': '未找到有效的刷新令牌',
                'error_type': 'no_refresh_token'
            }), 401

        # 手动设置Authorization头进行验证
        request.environ['HTTP_AUTHORIZATION'] = f'Bearer {refresh_token}'

        # 验证refresh token
        verify_jwt_in_request(refresh=True)
        user_id = get_jwt_identity()

        if not user_id:
            return jsonify({
                'success': False,
                'message': '无效的刷新令牌',
                'error_type': 'invalid_refresh_token'
            }), 401

        # 获取用户信息
        user = User.query.filter_by(id=user_id).first()
        if not user:
            return jsonify({
                'success': False,
                'message': '用户不存在',
                'error_type': 'user_not_found'
            }), 401

        # 生成新的access token
        new_access_token = create_access_token(identity=str(user.id))

        return jsonify({
            'success': True,
            'access_token': new_access_token,
            'user': serialize_user(user)
        }), 200

    except Exception as e:
        current_app.logger.error(f"令牌刷新失败: {e}")
        return jsonify({
            'success': False,
            'message': '令牌刷新失败',
            'error_type': 'refresh_failed'
        }), 401


@user_bp.route('/profile', methods=['GET'])
@require_auth
def get_profile():
    """获取用户资料"""
    try:
        user = get_current_user()
        return jsonify({
            'success': True,
            'user': serialize_user(user)
        })
    except Exception as e:
        return jsonify({'success': False, 'message': '获取资料失败'}), 500


@user_bp.route('/profile', methods=['PUT'])
@require_auth
def update_profile():
    """更新用户资料"""
    try:
        user = get_current_user()
        data = request.get_json()
        
        # 更新用户基本信息
        if 'username' in data:
            new_username = data['username'].strip()
            if len(new_username) < 3 or len(new_username) > 20:
                return jsonify({'success': False, 'message': '用户名长度应在3-20字符之间'}), 400
            
            # 检查用户名是否已被使用
            existing_user = User.query.filter_by(username=new_username).first()
            if existing_user and existing_user.id != user.id:
                return jsonify({'success': False, 'message': '用户名已被使用'}), 400
            
            user.username = new_username
        
        # 更新邮箱
        if 'email' in data:
            new_email = data['email'].strip()
            if new_email:
                # 验证邮箱格式
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, new_email):
                    return jsonify({'success': False, 'message': '邮箱格式不正确'}), 400
                
                # 检查邮箱是否已被使用
                existing_user = User.query.filter_by(email=new_email).first()
                if existing_user and existing_user.id != user.id:
                    return jsonify({'success': False, 'message': '邮箱已被使用'}), 400
                
                user.email = new_email
                # 如果更新了邮箱，将验证状态重置
                user.is_verified = False
        
        # 更新或创建用户资料
        profile = user.profile
        if not profile:
            profile = UserProfile(user_id=user.id)
            db.session.add(profile)
        
        # 更新资料字段
        profile_fields = ['display_name', 'bio', 'website', 'location']
        for field in profile_fields:
            if field in data:
                setattr(profile, field, data[field])
        
        # 更新偏好设置
        if 'preferences' in data:
            if not profile.preferences:
                profile.preferences = {}
            # 合并偏好设置
            profile.preferences.update(data['preferences'])
        
        # 处理单个偏好设置字段
        preference_fields = ['theme', 'language', 'timezone', 'default_model', 'default_aspect_ratio', 
                           'email_notifications', 'task_notifications', 'promotion_notifications']
        
        for field in preference_fields:
            if field in data:
                if not profile.preferences:
                    profile.preferences = {}
                profile.preferences[field] = data[field]
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': '资料更新成功',
            'user': serialize_user(user)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '更新失败，请重试'}), 500


@user_bp.route('/change-password', methods=['POST'])
@require_auth
def change_password():
    """修改密码"""
    try:
        user = get_current_user()
        data = request.get_json()
        
        old_password = data.get('old_password', '')
        new_password = data.get('new_password', '')
        
        if not all([old_password, new_password]):
            return jsonify({'success': False, 'message': '请填写完整信息'}), 400
        
        # 验证旧密码
        from werkzeug.security import check_password_hash, generate_password_hash
        if not check_password_hash(user.password_hash, old_password):
            return jsonify({'success': False, 'message': '原密码错误'}), 400
        
        # 验证新密码强度
        if len(new_password) < 6:
            return jsonify({'success': False, 'message': '新密码长度至少6位'}), 400
        
        # 更新密码
        try:
            user.password_hash = generate_password_hash(new_password)
            db.session.commit()
            return jsonify({'success': True, 'message': '密码修改成功'}), 200
        except Exception as db_error:
            db.session.rollback()
            print(f"密码修改失败: {db_error}")
            return jsonify({'success': False, 'message': '密码修改失败，请重试'}), 500
        
    except Exception as e:
        return jsonify({'success': False, 'message': '修改失败，请重试'}), 500


@user_bp.route('/credits', methods=['GET'])
@require_auth
def get_credits():
    """获取用户积分信息"""
    try:
        user = get_current_user()
        
        return jsonify({
            'success': True,
            'credits': {
                'total': user.total_credits,
                'used': user.used_credits,
                'available': user.available_credits,
                'daily_limit': user.daily_limit,
                'daily_used': user.daily_used,
                'daily_remaining': user.daily_remaining
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': '获取积分信息失败'}), 500


@user_bp.route('/credits/summary', methods=['GET'])
@require_auth
def get_credits_summary():
    """获取积分汇总信息"""
    try:
        user = get_current_user()

        return jsonify({
            'success': True,
            'summary': {
                'total_credits': user.total_credits,
                'used_credits': user.used_credits,
                'available_credits': user.available_credits,
                'daily_limit': user.daily_limit,
                'daily_used': user.daily_used,
                'daily_remaining': user.daily_remaining
            }
        })

    except Exception as e:
        print(f"获取积分汇总失败: {e}")
        return jsonify({'success': False, 'message': '获取积分汇总失败'}), 500


@user_bp.route('/credits/transactions', methods=['GET'])
@require_auth
def get_credit_transactions():
    """获取积分交易记录"""
    try:
        user = get_current_user()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 查询积分交易记录
        transactions = CreditsTransaction.query.filter_by(user_id=user.id)\
            .order_by(CreditsTransaction.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)

        transaction_list = []
        for tx in transactions.items:
            # 使用模型的to_dict方法来正确处理枚举序列化
            tx_dict = tx.to_dict()
            transaction_list.append(tx_dict)

        return jsonify({
            'success': True,
            'transactions': transaction_list,
            'pagination': {
                'page': transactions.page,
                'pages': transactions.pages,
                'per_page': transactions.per_page,
                'total': transactions.total,
                'has_next': transactions.has_next,
                'has_prev': transactions.has_prev
            }
        })

    except Exception as e:
        print(f"获取积分交易记录失败: {e}")
        return jsonify({'success': False, 'message': '获取积分交易记录失败'}), 500


@user_bp.route('/credits/purchase', methods=['POST'])
@require_auth
def purchase_credits():
    """模拟购买积分（测试功能）"""
    try:
        user = get_current_user()
        data = request.get_json()

        package_id = data.get('package_id')
        amount = data.get('amount', 0)

        # 预定义的积分套餐（与前端一致）
        packages = {
            'basic': {'credits': 50, 'price': 19, 'name': '基础包'},
            'standard': {'credits': 150, 'price': 49, 'name': '推荐包'},
            'premium': {'credits': 500, 'price': 149, 'name': '专业包'},
            'enterprise': {'credits': 1500, 'price': 399, 'name': '企业包'}
        }

        if package_id and package_id in packages:
            package = packages[package_id]
            credits_to_add = package['credits']
            description = f"购买{package['name']} - {package['credits']}积分"
        elif amount > 0:
            credits_to_add = amount
            description = f"自定义购买 - {amount}积分"
        else:
            return jsonify({'success': False, 'message': '无效的购买参数'}), 400

        # 模拟购买成功，添加积分
        from backend.services.credit_service import CreditService
        credit_service = CreditService()

        result = credit_service.add_credits(
            user_id=user.id,
            credits_amount=credits_to_add,
            description=description,
            transaction_type='purchase'
        )

        if result['success']:
            return jsonify({
                'success': True,
                'message': f'成功购买{credits_to_add}积分',
                'credits_added': credits_to_add,
                'new_balance': user.total_credits + credits_to_add
            })
        else:
            return jsonify({'success': False, 'message': result['message']}), 400

    except Exception as e:
        print(f"购买积分失败: {e}")
        return jsonify({'success': False, 'message': '购买积分失败'}), 500



@user_bp.route('/dashboard-data', methods=['GET'])
@require_auth
def get_dashboard_data():
    """获取用户仪表板数据"""
    try:
        user = get_current_user()

        # 基础用户信息
        user_info = {
            'username': user.username,
            'user_type': user.user_type.value,
            'phone': user.phone,
            'email': user.email,
            'is_verified': user.is_verified,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }

        # 积分信息
        credits_info = {
            'total_credits': user.total_credits,
            'used_credits': user.used_credits,
            'available_credits': user.available_credits,
            'daily_limit': user.daily_limit,
            'daily_used': user.daily_used,
            'daily_remaining': user.daily_remaining
        }

        # 任务统计
        from backend.models.task import Task
        total_tasks = Task.query.filter_by(user_id=user.id).count()
        completed_tasks = Task.query.filter_by(user_id=user.id, status='completed').count()
        failed_tasks = Task.query.filter_by(user_id=user.id, status='failed').count()

        task_stats = {
            'total': total_tasks,
            'completed': completed_tasks,
            'failed': failed_tasks,
            'success_rate': round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 1)
        }

        # 最近任务
        recent_tasks = Task.query.filter_by(user_id=user.id)\
            .order_by(Task.created_at.desc())\
            .limit(5).all()

        recent_tasks_data = [{
            'id': task.id,
            'type': task.type,
            'status': task.status,
            'message': task.message,
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'credits_cost': task.credits_cost
        } for task in recent_tasks]

        response_data = {
            'success': True,
            'data': {
                'user': user_info,
                'credits': credits_info,
                'tasks': task_stats,
                'recent_tasks': recent_tasks_data
            }
        }
        print(f"仪表板数据响应 - 用户: {user_info}")
        print(f"仪表板数据响应 - 积分: {credits_info}")
        print(f"仪表板数据响应 - 任务: {task_stats}")
        return jsonify(response_data)

    except Exception as e:
        print(f"获取仪表板数据失败: {e}")
        import traceback
        print(f"错误堆栈: {traceback.format_exc()}")
        return jsonify({'success': False, 'message': '获取仪表板数据失败'}), 500


@user_bp.route('/usage-stats', methods=['GET'])
@require_auth
def get_usage_stats():
    """获取用户使用统计"""
    try:
        user = get_current_user()
        days = request.args.get('days', 30, type=int)

        # 获取任务统计
        task_stats = Task.get_stats_by_user(user.id)

        # 获取积分使用统计
        credit_stats = CreditsTransaction.get_usage_stats(user.id, days)

        return jsonify({
            'success': True,
            'stats': {
                'tasks': task_stats,
                'credits': credit_stats
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'message': '获取统计信息失败'}), 500


@user_bp.route('/tasks', methods=['GET'])
@require_auth
def get_user_tasks():
    """获取用户的任务列表"""
    try:
        user = get_current_user()
        limit = request.args.get('limit', 20, type=int)
        status = request.args.get('status')
        
        tasks = Task.get_user_tasks(user.id, limit, status)
        
        return jsonify({
            'success': True,
            'tasks': [task.to_dict() for task in tasks]
        })
    except Exception as e:
        return jsonify({'success': False, 'message': '获取任务列表失败'}), 500


@user_bp.route('/verify-email/<token>', methods=['GET'])
def verify_email(token):
    """验证邮箱 - 暂时禁用"""
    return jsonify({'success': False, 'message': '邮箱验证功能暂时不可用'}), 503


@user_bp.route('/forgot-password', methods=['POST'])
def forgot_password():
    """忘记密码"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        
        if not email:
            return jsonify({'success': False, 'message': '请输入邮箱地址'}), 400
        
        # 密码重置功能暂时禁用
        return jsonify({'success': False, 'message': '密码重置功能暂时不可用'}), 503
        
    except Exception as e:
        return jsonify({'success': False, 'message': '请求失败，请重试'}), 500


@user_bp.route('/reset-password', methods=['POST'])
def reset_password():
    """重置密码"""
    try:
        data = request.get_json()
        token = data.get('token', '')
        new_password = data.get('new_password', '')
        
        if not all([token, new_password]):
            return jsonify({'success': False, 'message': '请填写完整信息'}), 400
        
        # 密码重置功能暂时禁用
        return jsonify({'success': False, 'message': '密码重置功能暂时不可用'}), 503
        
    except Exception as e:
        return jsonify({'success': False, 'message': '重置失败，请重试'}), 500


@user_bp.route('/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        
        if not username:
            return jsonify({'success': False, 'message': '用户名不能为空'}), 400
        
        if len(username) < 3 or len(username) > 20:
            return jsonify({'success': False, 'message': '用户名长度应在3-20字符之间'}), 400
        
        existing_user = User.query.filter_by(username=username).first()
        available = existing_user is None
        
        return jsonify({
            'success': True,
            'available': available,
            'message': '用户名可用' if available else '用户名已被使用'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '检查失败'}), 500


@user_bp.route('/check-email', methods=['POST'])
def check_email():
    """检查邮箱是否可用"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()

        if not email:
            return jsonify({'success': False, 'message': '邮箱不能为空'}), 400

        # 验证邮箱格式
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({'success': False, 'message': '邮箱格式不正确'}), 400

        existing_user = User.query.filter_by(email=email).first()
        available = existing_user is None

        return jsonify({
            'success': True,
            'available': available,
            'message': '邮箱可用' if available else '邮箱已被注册'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': '检查失败'}), 500


@user_bp.route('/credits/summary', methods=['GET'])
@require_auth
def get_credit_summary():
    """获取用户积分详细摘要"""
    try:
        user = get_current_user()
        result = credit_service.get_user_credit_summary(user.id)

        if result['success']:
            return jsonify(result)
        else:
            return jsonify({'success': False, 'message': result['message']}), 500

    except Exception as e:
        return jsonify({'success': False, 'message': '获取积分摘要失败'}), 500


@user_bp.route('/credits/calculate-cost', methods=['POST'])
@require_auth
def calculate_task_cost():
    """计算任务积分消费"""
    try:
        data = request.get_json()
        task_type = data.get('task_type', 'generate')
        model = data.get('model', 'flux-kontext-pro')
        parameters = data.get('parameters', {})

        cost = credit_service.calculate_task_cost(task_type, model, parameters)

        return jsonify({
            'success': True,
            'cost': cost,
            'task_type': task_type,
            'model': model,
            'parameters': parameters
        })

    except Exception as e:
        return jsonify({'success': False, 'message': '计算积分消费失败'}), 500


@user_bp.route('/credits/check-limits', methods=['POST'])
@require_auth
def check_user_limits():
    """检查用户限制"""
    try:
        user = get_current_user()
        data = request.get_json()
        credits_needed = data.get('credits_needed', 1)

        result = credit_service.check_user_limits(user.id, credits_needed)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': '检查用户限制失败'}), 500


@user_bp.route('/login-history', methods=['GET'])
@require_auth
def get_login_history():
    """获取登录历史记录"""
    try:
        user = get_current_user()
        limit = request.args.get('limit', 10, type=int)
        
        # TODO: 这里应该从实际的登录历史表中获取数据
        # 目前返回模拟数据，实际应该创建 LoginHistory 模型
        mock_history = [
            {
                'time': user.last_login.isoformat() if user.last_login else datetime.utcnow().isoformat(),
                'ip': '*************',
                'device': 'Chrome/Windows',
                'status': '成功',
                'location': '中国'
            }
        ]
        
        return jsonify({
            'success': True,
            'history': mock_history
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '获取登录历史失败'}), 500


@user_bp.route('/preferences', methods=['GET'])
@require_auth
def get_preferences():
    """获取用户偏好设置"""
    try:
        user = get_current_user()
        profile = user.profile
        
        # 默认偏好设置
        default_preferences = {
            'theme': 'light',
            'language': 'zh-CN',
            'timezone': 'Asia/Shanghai',
            'default_model': 'flux-kontext-pro',
            'default_aspect_ratio': '1:1',
            'email_notifications': True,
            'task_notifications': True,
            'promotion_notifications': False
        }
        
        # 如果用户有自定义偏好设置，合并到默认设置中
        if profile and profile.preferences:
            default_preferences.update(profile.preferences)
        
        return jsonify({
            'success': True,
            'preferences': default_preferences
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '获取偏好设置失败'}), 500


@user_bp.route('/preferences', methods=['PUT'])
@require_auth
def update_preferences():
    """更新用户偏好设置"""
    try:
        user = get_current_user()
        data = request.get_json()
        
        # 更新或创建用户资料
        profile = user.profile
        if not profile:
            profile = UserProfile(user_id=user.id)
            db.session.add(profile)
        
        # 初始化偏好设置
        if not profile.preferences:
            profile.preferences = {}
        
        # 更新偏好设置
        profile.preferences.update(data)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '偏好设置保存成功',
            'preferences': profile.preferences
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': '保存偏好设置失败'}), 500


@user_bp.route('/credit-packages', methods=['GET'])
def get_credit_packages():
    """获取可用的积分套餐"""
    try:
        from backend.models.credit import CreditPackage
        
        packages = CreditPackage.get_active_packages()
        
        return jsonify({
            'success': True,
            'packages': [package.to_dict() for package in packages]
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '获取积分套餐失败'}), 500




@user_bp.route('/daily-checkin', methods=['POST'])
@require_auth
def daily_checkin():
    """每日签到领取积分"""
    try:
        user = get_current_user()
        
        # 检查今天是否已经签到
        from datetime import date
        today = date.today()
        
        # 从用户资料中获取签到信息
        profile = user.profile
        if not profile:
            from backend.models.user import UserProfile
            profile = UserProfile(user_id=user.id)
            db.session.add(profile)
        
        # 初始化偏好设置
        if not profile.preferences:
            profile.preferences = {}
        
        last_checkin = profile.preferences.get('last_checkin_date')
        if last_checkin == str(today):
            return jsonify({'success': False, 'message': '今天已经签到过了'}), 400
        
        # 计算连续签到天数
        consecutive_days = profile.preferences.get('consecutive_checkin_days', 0)
        if last_checkin == str(today - timedelta(days=1)):
            consecutive_days += 1
        else:
            consecutive_days = 1
        
        # 计算奖励积分（连续签到有额外奖励）
        base_reward = 1
        bonus_reward = min(consecutive_days // 7, 3)  # 每连续7天额外奖励1积分，最多3积分
        total_reward = base_reward + bonus_reward
        
        # 增加积分
        result = credit_service.add_credits(
            user.id,
            total_reward,
            f'每日签到奖励 (连续{consecutive_days}天)',
            'bonus'
        )
        
        if result['success']:
            # 更新签到记录
            profile.preferences.update({
                'last_checkin_date': str(today),
                'consecutive_checkin_days': consecutive_days,
                'total_checkin_days': profile.preferences.get('total_checkin_days', 0) + 1
            })
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': f'签到成功，获得 {total_reward} 积分',
                'reward_credits': total_reward,
                'consecutive_days': consecutive_days,
                'new_balance': result['new_balance'],
                'next_bonus_in': 7 - (consecutive_days % 7) if consecutive_days % 7 != 0 else 7
            })
        else:
            return jsonify({'success': False, 'message': result['message']}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'message': '签到失败，请重试'}), 500


@user_bp.route('/referral-code', methods=['GET'])
@require_auth
def get_referral_code():
    """获取用户推荐码"""
    try:
        user = get_current_user()
        
        # 生成或获取推荐码
        profile = user.profile
        if not profile:
            from backend.models.user import UserProfile
            profile = UserProfile(user_id=user.id)
            db.session.add(profile)
        
        if not profile.preferences:
            profile.preferences = {}
        
        referral_code = profile.preferences.get('referral_code')
        if not referral_code:
            # 生成推荐码：用户名前3位 + ID后6位
            import hashlib
            user_hash = hashlib.md5(user.id.encode()).hexdigest()
            referral_code = f"{user.username[:3].upper()}{user_hash[:6].upper()}"
            profile.preferences['referral_code'] = referral_code
            db.session.commit()
        
        # 获取推荐统计
        referred_count = profile.preferences.get('referred_users_count', 0)
        total_referral_rewards = profile.preferences.get('total_referral_rewards', 0)
        
        return jsonify({
            'success': True,
            'referral_code': referral_code,
            'referred_count': referred_count,
            'total_rewards': total_referral_rewards,
            'reward_per_referral': 5,  # 每推荐一个用户奖励5积分
            'referral_url': f"{request.host_url}register?ref={referral_code}"
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': '获取推荐码失败'}), 500


@user_bp.route('/use-referral', methods=['POST'])
def use_referral_code():
    """使用推荐码（在注册时调用）"""
    try:
        data = request.get_json()
        referral_code = data.get('referral_code', '').strip().upper()
        new_user_id = data.get('user_id')  # 新注册用户的ID
        
        if not referral_code or not new_user_id:
            return jsonify({'success': False, 'message': '参数不完整'}), 400
        
        # 查找推荐人
        from backend.models.user import UserProfile
        referrer_profile = UserProfile.query.filter(
            UserProfile.preferences.contains({
                'referral_code': referral_code
            })
        ).first()
        
        if not referrer_profile:
            return jsonify({'success': False, 'message': '推荐码不存在'}), 400
        
        referrer = referrer_profile.user
        new_user = User.query.get(new_user_id)
        
        if not new_user:
            return jsonify({'success': False, 'message': '新用户不存在'}), 400
        
        # 防止自己推荐自己
        if referrer.id == new_user.id:
            return jsonify({'success': False, 'message': '不能使用自己的推荐码'}), 400
        
        # 给推荐人奖励积分
        referral_reward = 5
        result = credit_service.add_credits(
            referrer.id,
            referral_reward,
            f'推荐用户奖励: {new_user.username}',
            'bonus'
        )
        
        if result['success']:
            # 更新推荐统计
            if not referrer_profile.preferences:
                referrer_profile.preferences = {}
            
            referrer_profile.preferences.update({
                'referred_users_count': referrer_profile.preferences.get('referred_users_count', 0) + 1,
                'total_referral_rewards': referrer_profile.preferences.get('total_referral_rewards', 0) + referral_reward
            })
            
            # 给新用户一些欢迎积分
            welcome_result = credit_service.add_credits(
                new_user.id,
                3,
                f'推荐注册欢迎奖励',
                'bonus'
            )
            
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': f'推荐成功！推荐人获得{referral_reward}积分，新用户获得3积分',
                'referrer_reward': referral_reward,
                'new_user_reward': 3
            })
        else:
            return jsonify({'success': False, 'message': result['message']}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'message': '使用推荐码失败'}), 500
