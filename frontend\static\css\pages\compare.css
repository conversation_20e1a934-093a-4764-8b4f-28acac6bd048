/* 主题样式 */
body.theme-restore {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #D2691E 100%);
    min-height: 100vh;
}

/* 卡片容器 */
.compare-container {
    min-height: 100vh;
    padding: 2rem 0;
}

.compare-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* 功能标签 */
.function-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
    font-weight: 600;
}

/* 上传区域 */
.upload-area {
    margin: 2rem 0;
    text-align: center;
}

.upload-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
}

/* 状态区域 */
.status-area {
    text-align: center;
    padding: 1rem;
    margin: 1rem 0;
}

.status-message {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: 25px;
    font-weight: 500;
}

/* 信息区域 */
.info-area {
    background: var(--primary-gradient);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 2rem 0;
    color: white;
}

.info-content {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.info-item {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.info-item strong {
    color: rgba(255, 255, 255, 0.9);
    min-width: 100px;
}

/* 操作按钮 */
.compare-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin: 2rem 0;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 2rem;
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .compare-container {
        padding: 1rem;
    }
    
    .compare-card {
        padding: 1rem;
    }
    
    .function-tabs {
        flex-direction: column;
        align-items: center;
    }
    
    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .compare-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
} 