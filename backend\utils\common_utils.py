#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用工具函数模块
用于消除项目中的重复逻辑，提供统一的工具函数
"""

import math
import os
from typing import List, Tuple, Optional, Dict, Any
from flask import request, jsonify
from PIL import Image


def calculate_gcd(a: int, b: int) -> int:
    """
    计算两个数的最大公约数 (GCD)
    
    Args:
        a: 第一个数
        b: 第二个数
        
    Returns:
        最大公约数
    """
    while b:
        a, b = b, a % b
    return a


def simplify_aspect_ratio(width: int, height: int) -> str:
    """
    简化宽高比为最简分数形式
    
    Args:
        width: 图像宽度
        height: 图像高度
        
    Returns:
        简化后的宽高比字符串 (如 "16:9")
    """
    if width <= 0 or height <= 0:
        return "1:1"
    
    common_divisor = calculate_gcd(width, height)
    simplified_width = width // common_divisor
    simplified_height = height // common_divisor
    
    return f"{simplified_width}:{simplified_height}"


def generate_bfl_dimensions(target_pixels: int = 1040000, 
                           step: int = 16, 
                           tolerance: float = 0.05,
                           aspect_ratio_range: Tuple[float, float] = (9/21, 21/9)) -> List[Tuple[int, int, int, float]]:
    """
    生成BFL API支持的候选尺寸
    统一的尺寸生成逻辑，支持自定义宽高比范围
    
    Args:
        target_pixels: 目标像素数
        step: 步长（必须是16的倍数）
        tolerance: 像素数容忍度
        aspect_ratio_range: 宽高比范围 (min_ratio, max_ratio)
        
    Returns:
        候选尺寸列表 [(width, height, pixels, ratio), ...]
    """
    candidates = []
    min_pixels = target_pixels * (1 - tolerance)
    max_pixels = target_pixels * (1 + tolerance)
    
    min_ratio, max_ratio = aspect_ratio_range
    
    # 遍历所有可能的宽度（16的倍数）
    for width in range(step, int(math.sqrt(max_pixels * max_ratio)) + step, step):
        # 计算对应的理想高度
        ideal_height = target_pixels / width
        
        # 量化到最近的16倍数
        height = round(ideal_height / step) * step
        if height < step:
            continue
            
        pixels = width * height
        ratio = width / height
        
        # 检查约束条件
        if (min_pixels <= pixels <= max_pixels and 
            min_ratio <= ratio <= max_ratio):
            candidates.append((width, height, pixels, ratio))
    
    return candidates


def validate_uploaded_file(request_obj, 
                          file_key: str = 'image',
                          allowed_extensions: set = None) -> Tuple[bool, Optional[str], Optional[Dict[str, Any]]]:
    """
    验证上传的文件
    统一的文件上传验证逻辑
    
    Args:
        request_obj: Flask request对象
        file_key: 文件字段名
        allowed_extensions: 允许的文件扩展名集合
        
    Returns:
        (is_valid, error_message, error_response)
        - is_valid: 是否验证通过
        - error_message: 错误消息（如果有）
        - error_response: 错误响应字典（如果有）
    """
    if allowed_extensions is None:
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
    
    # 检查是否有文件上传
    if file_key not in request_obj.files:
        error_msg = f"请上传{file_key}文件"
        return False, error_msg, {'error': error_msg}
    
    file = request_obj.files[file_key]
    
    # 检查文件名是否为空
    if file.filename == '':
        error_msg = "未选择文件"
        return False, error_msg, {'error': error_msg}
    
    # 检查文件扩展名
    if not _is_allowed_file(file.filename, allowed_extensions):
        error_msg = "不支持的文件格式"
        return False, error_msg, {'error': error_msg}
    
    return True, None, None


def _is_allowed_file(filename: str, allowed_extensions: set) -> bool:
    """
    检查文件扩展名是否允许
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名集合
        
    Returns:
        是否允许
    """
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in allowed_extensions)


def create_error_response(message: str, 
                         status_code: int = 400,
                         success: bool = False,
                         additional_data: Dict[str, Any] = None) -> Tuple[Dict[str, Any], int]:
    """
    创建统一的错误响应
    
    Args:
        message: 错误消息
        status_code: HTTP状态码
        success: 是否成功标志
        additional_data: 额外数据
        
    Returns:
        (response_dict, status_code)
    """
    response = {
        'success': success,
        'error': message
    }
    
    if additional_data:
        response.update(additional_data)
    
    return response, status_code


def create_success_response(data: Any = None,
                           message: str = None,
                           additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    创建统一的成功响应
    
    Args:
        data: 响应数据
        message: 成功消息
        additional_data: 额外数据
        
    Returns:
        response_dict
    """
    response = {
        'success': True
    }
    
    if data is not None:
        response['data'] = data
    if message:
        response['message'] = message
    if additional_data:
        response.update(additional_data)
    
    return response


def handle_task_status_update(tasks_dict: Dict[str, Dict[str, Any]], 
                             task_id: str, 
                             status: str, 
                             message: str = None,
                             additional_data: Dict[str, Any] = None) -> None:
    """
    更新任务状态的统一函数
    
    Args:
        tasks_dict: 任务字典
        task_id: 任务ID
        status: 新状态
        message: 状态消息
        additional_data: 额外数据
    """
    if task_id not in tasks_dict:
        return
    
    tasks_dict[task_id]['status'] = status
    if message:
        tasks_dict[task_id]['message'] = message
    if additional_data:
        tasks_dict[task_id].update(additional_data)


def log_error_with_context(error: Exception, 
                          context: str = "",
                          print_traceback: bool = True) -> str:
    """
    统一的错误日志记录函数
    
    Args:
        error: 异常对象
        context: 错误上下文
        print_traceback: 是否打印完整错误跟踪
        
    Returns:
        错误消息字符串
    """
    error_msg = f"❌ {context}错误: {str(error)}" if context else f"❌ 错误: {str(error)}"
    print(error_msg)
    
    if print_traceback:
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    return error_msg


def detect_prompt_language(text: str) -> str:
    """
    检测提示词语言类型
    
    Args:
        text: 要检测的文本
        
    Returns:
        'chinese' 如果包含中文字符，否则返回 'english'
    """
    if not text or not text.strip():
        return 'english'  # 空文本默认为英文
    
    # 检测中文字符（包括中文标点符号）
    import re
    chinese_pattern = r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]'
    
    if re.search(chinese_pattern, text):
        return 'chinese'
    
    return 'english'


def is_chinese_text(text: str) -> bool:
    """
    判断文本是否包含中文字符
    
    Args:
        text: 要检测的文本
        
    Returns:
        True 如果包含中文字符，否则 False
    """
    return detect_prompt_language(text) == 'chinese'


def extract_english_from_mixed_text(text: str) -> str:
    """
    从混合文本中提取英文部分（如果翻译结果包含中英混合）
    
    Args:
        text: 混合文本
        
    Returns:
        提取的英文部分
    """
    if not text:
        return ""
    
    # 简单的英文提取逻辑：移除中文字符，保留英文和标点
    import re
    # 移除中文字符，保留英文、数字、标点和空格
    english_only = re.sub(r'[\u4e00-\u9fff\u3000-\u303f\uff00-\uffef]', ' ', text)
    # 清理多余空格
    english_only = ' '.join(english_only.split())
    
    return english_only.strip()


def resize_image_for_upload(image_path: str, target_pixels: int = 1000000, suffix: str = '_resized') -> str:
    """
    在上传前调整图像大小，使其接近目标像素数，同时保持宽高比。
    优化版本：统一使用100万像素限制

    Args:
        image_path (str): 原始图像的路径.
        target_pixels (int, optional): 目标像素数. Defaults to 1,000,000 (1MP).
        suffix (str, optional): 调整大小后文件名添加的后缀. Defaults to '_resized'.

    Returns:
        str: 调整大小后的图像路径。如果图像已经小于目标像素，则返回原始路径。
    """
    try:
        with Image.open(image_path) as img:
            original_width, original_height = img.size
            current_pixels = original_width * original_height

            # 如果图像已经足够小，则无需处理
            if current_pixels <= target_pixels:
                print(f"🖼️ 图像尺寸 ({original_width}x{original_height}) 已在目标范围内，无需调整。")
                return image_path

            # 计算缩放比例以达到目标像素
            scale_ratio = math.sqrt(target_pixels / current_pixels)
            new_width = int(original_width * scale_ratio)
            new_height = int(original_height * scale_ratio)

            print(f"🎨 正在调整图像尺寸：从 {original_width}x{original_height} 缩放到 {new_width}x{new_height}...")
            print(f"📊 像素压缩：{current_pixels:,} → {new_width * new_height:,} (减少 {((current_pixels - new_width * new_height) / current_pixels * 100):.1f}%)")
            
            # 使用高质量的LANCZOS滤波器进行缩放
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 确定新文件名
            file_dir, original_filename = os.path.split(image_path)
            file_root, file_ext = os.path.splitext(original_filename)
            new_filename = f"{file_root}{suffix}{file_ext}"
            new_image_path = os.path.join(file_dir, new_filename)
            
            # 保存调整大小后的图像，对于JPEG格式使用较高质量
            save_options = {'quality': 95} if img.format == 'JPEG' else {}
            resized_img.save(new_image_path, format=img.format, **save_options)
            
            print(f"✅ 图像调整成功，已保存至: {new_image_path}")
            return new_image_path

    except Exception as e:
        log_error_with_context(e, "调整图像大小时发生错误")
        return image_path  # 如果失败，返回原始路径