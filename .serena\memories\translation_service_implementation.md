# 翻译服务实现细节

## 服务管理器 (`translation_service_manager.py`)

1. **核心功能**
   - 多服务管理（Ollama和DeepSeek）
   - 自动故障转移
   - 服务状态监控
   - 负载均衡
   - 单例模式实现

2. **主要方法**
   - `translate()` - 文本翻译
   - `polish()` - 文本润色
   - `get_service_status()` - 获取服务状态
   - `switch_service()` - 切换服务
   - `initialize_services()` - 初始化服务

3. **错误处理**
   - 服务不可用自动切换
   - 超时控制
   - 异常捕获和日志记录
   - 备用服务自动切换

4. **配置管理**
   - 环境变量优先
   - 默认配置备选
   - 运行时配置更新
   - 配置验证和检查

## 配置系统 (`translation_config.py`)

1. **服务配置**
   - DeepSeek API配置
   - Ollama服务配置
   - 服务优先级设置
   - 超时和重试设置

2. **环境变量**
   - `DEEPSEEK_API_KEY`
   - `OLLAMA_URL`
   - `OLLAMA_MODEL`
   - `DEFAULT_SERVICE`

3. **默认值**
   - Ollama URL: http://localhost:11434
   - Ollama Model: qwen3:4b
   - 超时时间: 30秒

## 特殊功能

1. **服务状态检查**
   - 快速检查机制（3秒超时）
   - 并发状态检查
   - 服务可用性验证

2. **故障转移**
   - 主服务失败自动切换
   - 备用服务状态监控
   - 错误恢复机制

3. **性能优化**
   - 服务状态缓存
   - 并发请求处理
   - 超时控制
   - 资源释放