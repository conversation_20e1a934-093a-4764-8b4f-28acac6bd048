# BFL AI 项目架构解耦实施指南

> **文档版本**: v1.0  
> **创建时间**: 2025-01-03  
> **适用对象**: Python/Flask 开发人员  
> **预计工期**: 6-8周  

## 📋 实施概述

本指南将指导您将现有的 BFL AI 图像生成器项目解耦为两个独立模块：
1. **图像处理平台** - 独立的图像生成和编辑服务
2. **SaaS业务管理平台** - 通用的用户、积分、支付管理系统

## 🎯 实施前准备

### 环境要求
- Python 3.9+
- Flask 2.0+
- SQLAlchemy 1.4+
- Git (用于版本控制)
- 至少 4GB 可用磁盘空间

### 备份当前项目
```bash
# 1. 创建完整备份
cd /path/to/your/project
cp -r . ../bfl_backup_$(date +%Y%m%d_%H%M%S)

# 2. 提交当前状态到Git
git add .
git commit -m "Pre-decoupling backup - $(date)"
git tag pre-decoupling-v1.0

# 3. 创建解耦分支
git checkout -b feature/architecture-decoupling
```

### 依赖检查
```bash
# 检查当前依赖
pip list > requirements_current.txt

# 验证关键组件
python -c "import flask, sqlalchemy; print('Dependencies OK')"
```

## 🏗️ 阶段1: 接口抽象和标准化 (2-3周)

### 步骤1.1: 创建核心接口模块 (第1-2天)

#### 1.1.1 创建目录结构
```bash
# 创建核心接口目录
mkdir -p backend/core/interfaces
mkdir -p backend/core/adapters
mkdir -p backend/core/managers

# 创建初始化文件
touch backend/core/__init__.py
touch backend/core/interfaces/__init__.py
touch backend/core/adapters/__init__.py
touch backend/core/managers/__init__.py
```

#### 1.1.2 创建业务插件接口
创建文件 `backend/core/interfaces/business_plugin.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
业务插件标准接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from enum import Enum


class PluginStatus(Enum):
    """插件状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    MAINTENANCE = "maintenance"


class TaskResult:
    """任务结果标准格式"""
    
    def __init__(self, success: bool, data: Dict[str, Any] = None, 
                 error: str = None, task_id: str = None):
        self.success = success
        self.data = data or {}
        self.error = error
        self.task_id = task_id
        self.timestamp = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "task_id": self.task_id,
            "timestamp": self.timestamp.isoformat()
        }


class IBusinessPlugin(ABC):
    """业务插件标准接口"""
    
    @abstractmethod
    def get_plugin_info(self) -> Dict[str, Any]:
        """
        获取插件基本信息
        
        Returns:
            Dict包含: name, version, description, author, capabilities
        """
        pass
    
    @abstractmethod
    def get_task_types(self) -> List[str]:
        """
        获取插件支持的任务类型
        
        Returns:
            List[str]: 支持的任务类型列表
        """
        pass
    
    @abstractmethod
    def get_pricing_info(self) -> Dict[str, int]:
        """
        获取任务类型对应的积分消耗
        
        Returns:
            Dict[str, int]: {task_type: credits_cost}
        """
        pass
    
    @abstractmethod
    def validate_task_data(self, task_data: Dict[str, Any]) -> bool:
        """
        验证任务数据格式
        
        Args:
            task_data: 任务数据
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    @abstractmethod
    def process_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """
        处理业务任务
        
        Args:
            task_data: 任务数据
            
        Returns:
            TaskResult: 处理结果
        """
        pass
    
    @abstractmethod
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        pass
    
    @abstractmethod
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def get_plugin_status(self) -> PluginStatus:
        """
        获取插件当前状态
        
        Returns:
            PluginStatus: 插件状态
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        插件健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        pass


class IAuthAdapter(ABC):
    """认证适配器接口"""
    
    @abstractmethod
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        pass
    
    @abstractmethod
    def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """用户认证"""
        pass
    
    @abstractmethod
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        pass
    
    @abstractmethod
    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        pass


class IBillingAdapter(ABC):
    """计费适配器接口"""
    
    @abstractmethod
    def check_credits(self, user_id: str, task_type: str) -> bool:
        """检查用户积分是否足够"""
        pass
    
    @abstractmethod
    def deduct_credits(self, user_id: str, amount: int, task_id: str) -> bool:
        """扣除用户积分"""
        pass
    
    @abstractmethod
    def get_user_credits(self, user_id: str) -> int:
        """获取用户当前积分"""
        pass
    
    @abstractmethod
    def get_task_cost(self, task_type: str) -> int:
        """获取任务类型的积分消耗"""
        pass
```

#### 1.1.3 验证接口创建
```bash
# 验证文件创建成功
ls -la backend/core/interfaces/

# 验证Python语法
python -m py_compile backend/core/interfaces/business_plugin.py
echo "接口文件语法检查通过"
```

**完成标准**: 
- ✅ 目录结构创建完成
- ✅ 接口文件语法检查通过
- ✅ 接口设计评审通过

### 步骤1.2: 创建配置管理系统 (第3天)

#### 1.2.1 创建配置管理器
创建文件 `backend/core/config_manager.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 统一管理应用配置
"""
import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or self._get_default_config_file()
        self.config = self._load_config()
        self._validate_config()
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        project_root = Path(__file__).parent.parent.parent
        return str(project_root / "config" / "app_config.yaml")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                        return yaml.safe_load(f)
                    else:
                        return json.load(f)
            else:
                print(f"配置文件不存在: {self.config_file}, 使用默认配置")
                return self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}, 使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'mode': 'integrated',  # integrated, saas_only, plugin_only
            'auth': {
                'type': 'saas',  # saas, anonymous, local
                'session_timeout': 3600,
                'jwt_secret': os.environ.get('JWT_SECRET', 'dev-secret-key'),
                'password_salt': os.environ.get('PASSWORD_SALT', 'dev-salt')
            },
            'billing': {
                'type': 'saas',  # saas, free, custom
                'default_credits': 10,
                'free_tier_limit': 5
            },
            'plugins': {
                'enabled': ['image_processing'],
                'auto_load': True,
                'plugin_dir': 'plugins',
                'max_concurrent_tasks': 5
            },
            'database': {
                'core_db': os.environ.get('DATABASE_URL', 'sqlite:///core.db'),
                'plugin_db': os.environ.get('PLUGIN_DB_URL', 'sqlite:///plugins.db')
            },
            'api': {
                'host': '0.0.0.0',
                'port': 5000,
                'debug': os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
            },
            'file_storage': {
                'upload_dir': 'uploads',
                'output_dir': 'outputs',
                'max_file_size': 10 * 1024 * 1024,  # 10MB
                'allowed_extensions': ['jpg', 'jpeg', 'png', 'webp']
            }
        }
    
    def _validate_config(self):
        """验证配置有效性"""
        required_keys = ['mode', 'auth', 'billing', 'plugins', 'database']
        for key in required_keys:
            if key not in self.config:
                raise ValueError(f"配置文件缺少必需的键: {key}")
    
    def get(self, key: str, default=None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'auth.type' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def is_saas_mode(self) -> bool:
        """是否为SaaS模式"""
        return self.get('mode') in ['integrated', 'saas_only']
    
    def is_plugin_mode(self) -> bool:
        """是否为插件模式"""
        return self.get('mode') in ['integrated', 'plugin_only']
    
    def is_standalone_mode(self) -> bool:
        """是否为独立模式"""
        return self.get('mode') == 'plugin_only'
    
    def get_enabled_plugins(self) -> List[str]:
        """获取启用的插件列表"""
        return self.get('plugins.enabled', [])
    
    def save_config(self, file_path: str = None):
        """保存配置到文件"""
        target_file = file_path or self.config_file
        
        try:
            os.makedirs(os.path.dirname(target_file), exist_ok=True)
            
            with open(target_file, 'w', encoding='utf-8') as f:
                if target_file.endswith('.yaml') or target_file.endswith('.yml'):
                    yaml.dump(self.config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                else:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            print(f"配置已保存到: {target_file}")
            
        except Exception as e:
            print(f"保存配置失败: {e}")
            raise


# 全局配置管理器实例
config_manager = ConfigManager()
```

#### 1.2.2 创建配置文件模板
```bash
# 创建配置目录
mkdir -p config

# 创建主配置文件模板
cat > config/app_config.yaml << 'EOF'
# BFL AI 项目配置文件
# 运行模式: integrated(集成), saas_only(仅SaaS), plugin_only(仅插件)
mode: integrated

# 认证配置
auth:
  type: saas  # saas, anonymous, local
  session_timeout: 3600
  jwt_secret: ${JWT_SECRET:-dev-secret-key}
  password_salt: ${PASSWORD_SALT:-dev-salt}

# 计费配置
billing:
  type: saas  # saas, free, custom
  default_credits: 10
  free_tier_limit: 5

# 插件配置
plugins:
  enabled:
    - image_processing
  auto_load: true
  plugin_dir: plugins
  max_concurrent_tasks: 5

# 数据库配置
database:
  core_db: ${DATABASE_URL:-sqlite:///core.db}
  plugin_db: ${PLUGIN_DB_URL:-sqlite:///plugins.db}

# API配置
api:
  host: 0.0.0.0
  port: 5000
  debug: ${FLASK_DEBUG:-false}

# 文件存储配置
file_storage:
  upload_dir: uploads
  output_dir: outputs
  max_file_size: 10485760  # 10MB
  allowed_extensions:
    - jpg
    - jpeg
    - png
    - webp
EOF
```

#### 1.2.3 验证配置管理器
```bash
# 测试配置管理器
python3 -c "
from backend.core.config_manager import config_manager
print('配置模式:', config_manager.get('mode'))
print('认证类型:', config_manager.get('auth.type'))
print('启用插件:', config_manager.get_enabled_plugins())
print('配置管理器测试通过')
"
```

**完成标准**:
- ✅ 配置管理器创建完成
- ✅ 配置文件模板创建完成
- ✅ 配置加载测试通过

### 步骤1.3: 创建插件管理器 (第4-5天)

#### 1.3.1 创建插件管理器
创建文件 `backend/core/managers/plugin_manager.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件管理器 - 负责插件的注册、发现、加载和管理
"""
import os
import sys
import importlib
import importlib.util
from typing import Dict, List, Optional, Type
from pathlib import Path
import threading
import time
from datetime import datetime

from backend.core.interfaces.business_plugin import IBusinessPlugin, PluginStatus, TaskResult
from backend.core.config_manager import config_manager


class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins: Dict[str, IBusinessPlugin] = {}
        self.task_type_mapping: Dict[str, str] = {}
        self.plugin_configs: Dict[str, Dict] = {}
        self._lock = threading.RLock()
        self._health_check_interval = 60  # 健康检查间隔(秒)
        self._health_check_thread = None
        self._running = False
    
    def start(self):
        """启动插件管理器"""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            
            # 自动加载插件
            if config_manager.get('plugins.auto_load', True):
                self.auto_load_plugins()
            
            # 启动健康检查
            self._start_health_check()
    
    def stop(self):
        """停止插件管理器"""
        with self._lock:
            self._running = False
            
            if self._health_check_thread:
                self._health_check_thread.join(timeout=5)
    
    def register_plugin(self, plugin: IBusinessPlugin, config: Dict = None):
        """
        注册插件
        
        Args:
            plugin: 插件实例
            config: 插件配置
        """
        with self._lock:
            plugin_info = plugin.get_plugin_info()
            plugin_name = plugin_info['name']
            
            # 验证插件
            if not self._validate_plugin(plugin):
                raise ValueError(f"插件验证失败: {plugin_name}")
            
            # 注册插件
            self.plugins[plugin_name] = plugin
            self.plugin_configs[plugin_name] = config or {}
            
            # 建立任务类型映射
            for task_type in plugin.get_task_types():
                if task_type in self.task_type_mapping:
                    print(f"警告: 任务类型 {task_type} 已被插件 {self.task_type_mapping[task_type]} 注册")
                self.task_type_mapping[task_type] = plugin_name
            
            print(f"插件注册成功: {plugin_name} (支持任务: {plugin.get_task_types()})")
    
    def unregister_plugin(self, plugin_name: str):
        """注销插件"""
        with self._lock:
            if plugin_name not in self.plugins:
                return False
            
            plugin = self.plugins[plugin_name]
            
            # 移除任务类型映射
            for task_type in plugin.get_task_types():
                if self.task_type_mapping.get(task_type) == plugin_name:
                    del self.task_type_mapping[task_type]
            
            # 移除插件
            del self.plugins[plugin_name]
            if plugin_name in self.plugin_configs:
                del self.plugin_configs[plugin_name]
            
            print(f"插件注销成功: {plugin_name}")
            return True
    
    def get_plugin(self, plugin_name: str) -> Optional[IBusinessPlugin]:
        """获取插件实例"""
        return self.plugins.get(plugin_name)
    
    def get_plugin_for_task(self, task_type: str) -> Optional[IBusinessPlugin]:
        """根据任务类型获取插件"""
        plugin_name = self.task_type_mapping.get(task_type)
        return self.plugins.get(plugin_name) if plugin_name else None
    
    def get_all_plugins(self) -> List[Dict]:
        """获取所有插件信息"""
        plugins_info = []
        for name, plugin in self.plugins.items():
            info = plugin.get_plugin_info()
            info['status'] = plugin.get_plugin_status().value
            info['health'] = plugin.health_check()
            plugins_info.append(info)
        return plugins_info
    
    def get_supported_task_types(self) -> List[str]:
        """获取所有支持的任务类型"""
        return list(self.task_type_mapping.keys())
    
    def process_task(self, task_data: Dict) -> TaskResult:
        """
        处理任务
        
        Args:
            task_data: 任务数据，必须包含 'type' 字段
            
        Returns:
            TaskResult: 处理结果
        """
        task_type = task_data.get('type')
        if not task_type:
            return TaskResult(False, error="任务数据缺少 'type' 字段")
        
        plugin = self.get_plugin_for_task(task_type)
        if not plugin:
            return TaskResult(False, error=f"不支持的任务类型: {task_type}")
        
        # 检查插件状态
        if plugin.get_plugin_status() != PluginStatus.ACTIVE:
            return TaskResult(False, error=f"插件 {task_type} 当前不可用")
        
        # 验证任务数据
        if not plugin.validate_task_data(task_data):
            return TaskResult(False, error="任务数据验证失败")
        
        try:
            return plugin.process_task(task_data)
        except Exception as e:
            return TaskResult(False, error=f"任务处理异常: {str(e)}")
    
    def auto_load_plugins(self):
        """自动加载插件"""
        plugin_dir = config_manager.get('plugins.plugin_dir', 'plugins')
        enabled_plugins = config_manager.get('plugins.enabled', [])
        
        if not os.path.exists(plugin_dir):
            print(f"插件目录不存在: {plugin_dir}")
            return
        
        for plugin_name in enabled_plugins:
            try:
                self._load_plugin_from_directory(plugin_dir, plugin_name)
            except Exception as e:
                print(f"加载插件失败 {plugin_name}: {e}")
    
    def _load_plugin_from_directory(self, plugin_dir: str, plugin_name: str):
        """从目录加载插件"""
        plugin_file = os.path.join(plugin_dir, f"{plugin_name}_plugin.py")
        
        if not os.path.exists(plugin_file):
            print(f"插件文件不存在: {plugin_file}")
            return
        
        # 动态导入插件模块
        spec = importlib.util.spec_from_file_location(f"{plugin_name}_plugin", plugin_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 获取插件类
        plugin_class = getattr(module, 'Plugin', None)
        if not plugin_class:
            raise ValueError(f"插件文件 {plugin_file} 中未找到 Plugin 类")
        
        # 创建插件实例
        plugin = plugin_class()
        
        # 加载插件配置
        config_file = os.path.join(plugin_dir, f"{plugin_name}_config.yaml")
        plugin_config = {}
        if os.path.exists(config_file):
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                plugin_config = yaml.safe_load(f)
        
        # 注册插件
        self.register_plugin(plugin, plugin_config)
    
    def _validate_plugin(self, plugin: IBusinessPlugin) -> bool:
        """验证插件是否符合接口规范"""
        try:
            # 检查必需方法
            plugin.get_plugin_info()
            plugin.get_task_types()
            plugin.get_pricing_info()
            plugin.get_plugin_status()
            plugin.health_check()
            
            return True
        except Exception as e:
            print(f"插件验证失败: {e}")
            return False
    
    def _start_health_check(self):
        """启动健康检查线程"""
        def health_check_worker():
            while self._running:
                try:
                    self._perform_health_check()
                except Exception as e:
                    print(f"健康检查异常: {e}")
                
                time.sleep(self._health_check_interval)
        
        self._health_check_thread = threading.Thread(target=health_check_worker, daemon=True)
        self._health_check_thread.start()
    
    def _perform_health_check(self):
        """执行健康检查"""
        for name, plugin in self.plugins.items():
            try:
                health_info = plugin.health_check()
                status = plugin.get_plugin_status()
                
                if status == PluginStatus.ERROR:
                    print(f"插件 {name} 状态异常: {health_info}")
                
            except Exception as e:
                print(f"插件 {name} 健康检查失败: {e}")


# 全局插件管理器实例
plugin_manager = PluginManager()
```

**完成标准**:
- ✅ 插件管理器创建完成
- ✅ 插件注册和发现机制实现
- ✅ 健康检查机制实现
- ✅ 基本功能测试通过

### 步骤1.4: 创建适配器层 (第6-7天)

#### 1.4.1 创建认证适配器
创建文件 `backend/core/adapters/auth_adapter.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证适配器 - 统一不同认证方式的接口
"""
from typing import Dict, Any, Optional, List
from flask import session, request, g
from abc import ABC, abstractmethod

from backend.core.interfaces.business_plugin import IAuthAdapter
from backend.core.config_manager import config_manager


class AnonymousUser:
    """匿名用户类"""

    def __init__(self):
        self.id = "anonymous"
        self.username = "匿名用户"
        self.user_type = "anonymous"
        self.is_authenticated = False
        self.permissions = []

    def to_dict(self):
        return {
            "id": self.id,
            "username": self.username,
            "user_type": self.user_type,
            "is_authenticated": self.is_authenticated
        }


class SaaSAuthAdapter(IAuthAdapter):
    """SaaS认证适配器 - 使用现有的用户系统"""

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        try:
            # 从session获取用户ID
            user_id = session.get('user_id')
            if not user_id:
                return None

            # 从数据库获取用户信息
            from backend.models.user import User
            user = User.query.get(user_id)
            if not user or not user.is_active:
                return None

            return {
                "id": user.id,
                "username": user.username,
                "phone": user.phone,
                "email": user.email,
                "user_type": user.user_type.value,
                "is_authenticated": True,
                "available_credits": user.available_credits,
                "daily_limit": user.daily_limit
            }

        except Exception as e:
            print(f"获取用户信息失败: {e}")
            return None

    def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """用户认证"""
        try:
            from backend.models.user import User

            # 支持多种登录方式
            identifier = credentials.get('identifier')  # 可以是手机号、邮箱或用户名
            password = credentials.get('password')

            if not identifier or not password:
                return None

            # 查找用户
            user = User.query.filter(
                (User.phone == identifier) |
                (User.email == identifier) |
                (User.username == identifier)
            ).first()

            if not user or not user.check_password(password):
                return None

            if not user.is_active:
                return None

            # 更新登录时间
            from datetime import datetime
            user.last_login = datetime.utcnow()
            user.save()

            # 设置session
            session['user_id'] = user.id
            session.permanent = True

            return self.get_current_user()

        except Exception as e:
            print(f"用户认证失败: {e}")
            return None

    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.get_current_user() is not None

    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        try:
            from backend.models.user import User
            user = User.query.get(user_id)
            if not user:
                return []

            permissions = ['basic']

            if user.user_type.value in ['premium', 'pro', 'enterprise']:
                permissions.append('premium')

            if user.user_type.value in ['pro', 'enterprise']:
                permissions.append('advanced')

            if user.user_type.value == 'enterprise':
                permissions.append('enterprise')

            return permissions

        except Exception as e:
            print(f"获取用户权限失败: {e}")
            return []


class AnonymousAuthAdapter(IAuthAdapter):
    """匿名认证适配器 - 用于独立模式"""

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息 - 返回匿名用户"""
        return AnonymousUser().to_dict()

    def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """用户认证 - 匿名模式总是返回匿名用户"""
        return self.get_current_user()

    def is_authenticated(self) -> bool:
        """检查是否已认证 - 匿名模式总是认为已认证"""
        return True

    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限 - 匿名用户只有基本权限"""
        return ['basic', 'anonymous']


class LocalAuthAdapter(IAuthAdapter):
    """本地认证适配器 - 简单的本地认证"""

    def __init__(self):
        self.local_users = {
            "admin": {
                "id": "local_admin",
                "username": "admin",
                "password": "admin123",
                "user_type": "admin",
                "permissions": ["basic", "admin"]
            },
            "user": {
                "id": "local_user",
                "username": "user",
                "password": "user123",
                "user_type": "user",
                "permissions": ["basic"]
            }
        }

    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        user_id = session.get('local_user_id')
        if not user_id:
            return None

        for username, user_data in self.local_users.items():
            if user_data['id'] == user_id:
                return {
                    "id": user_data['id'],
                    "username": user_data['username'],
                    "user_type": user_data['user_type'],
                    "is_authenticated": True
                }
        return None

    def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """用户认证"""
        username = credentials.get('username')
        password = credentials.get('password')

        if username in self.local_users:
            user_data = self.local_users[username]
            if user_data['password'] == password:
                session['local_user_id'] = user_data['id']
                return self.get_current_user()

        return None

    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.get_current_user() is not None

    def get_user_permissions(self, user_id: str) -> List[str]:
        """获取用户权限"""
        for username, user_data in self.local_users.items():
            if user_data['id'] == user_id:
                return user_data['permissions']
        return []


class AuthAdapterFactory:
    """认证适配器工厂"""

    @staticmethod
    def create_adapter() -> IAuthAdapter:
        """根据配置创建认证适配器"""
        auth_type = config_manager.get('auth.type', 'saas')

        if auth_type == 'saas':
            return SaaSAuthAdapter()
        elif auth_type == 'anonymous':
            return AnonymousAuthAdapter()
        elif auth_type == 'local':
            return LocalAuthAdapter()
        else:
            raise ValueError(f"不支持的认证类型: {auth_type}")


# 全局认证适配器实例
auth_adapter = AuthAdapterFactory.create_adapter()
```

#### 1.4.2 创建计费适配器
创建文件 `backend/core/adapters/billing_adapter.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计费适配器 - 统一不同计费方式的接口
"""
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod

from backend.core.interfaces.business_plugin import IBillingAdapter
from backend.core.config_manager import config_manager


class SaaSBillingAdapter(IBillingAdapter):
    """SaaS计费适配器 - 使用现有的积分系统"""

    def check_credits(self, user_id: str, task_type: str) -> bool:
        """检查用户积分是否足够"""
        try:
            if user_id == "anonymous":
                return True  # 匿名用户在SaaS模式下不允许使用

            from backend.models.user import User
            user = User.query.get(user_id)
            if not user:
                return False

            required_credits = self.get_task_cost(task_type)
            return user.available_credits >= required_credits

        except Exception as e:
            print(f"检查积分失败: {e}")
            return False

    def deduct_credits(self, user_id: str, amount: int, task_id: str) -> bool:
        """扣除用户积分"""
        try:
            if user_id == "anonymous":
                return False

            from backend.services.credit_service import credit_service
            return credit_service.deduct_credits(user_id, amount, f"任务消费: {task_id}")

        except Exception as e:
            print(f"扣除积分失败: {e}")
            return False

    def get_user_credits(self, user_id: str) -> int:
        """获取用户当前积分"""
        try:
            if user_id == "anonymous":
                return 0

            from backend.models.user import User
            user = User.query.get(user_id)
            return user.available_credits if user else 0

        except Exception as e:
            print(f"获取用户积分失败: {e}")
            return 0

    def get_task_cost(self, task_type: str) -> int:
        """获取任务类型的积分消耗"""
        # 默认积分消耗配置
        default_costs = {
            'image_generation': 2,
            'image_editing': 3,
            'style_transfer': 4,
            'image_restore': 3,
            'text_translation': 1
        }

        return default_costs.get(task_type, 1)


class FreeBillingAdapter(IBillingAdapter):
    """免费计费适配器 - 用于独立模式"""

    def check_credits(self, user_id: str, task_type: str) -> bool:
        """检查用户积分是否足够 - 免费模式总是返回True"""
        return True

    def deduct_credits(self, user_id: str, amount: int, task_id: str) -> bool:
        """扣除用户积分 - 免费模式不扣除积分"""
        return True

    def get_user_credits(self, user_id: str) -> int:
        """获取用户当前积分 - 免费模式返回无限积分"""
        return 999999

    def get_task_cost(self, task_type: str) -> int:
        """获取任务类型的积分消耗 - 免费模式消耗为0"""
        return 0


class CustomBillingAdapter(IBillingAdapter):
    """自定义计费适配器 - 可扩展的计费逻辑"""

    def __init__(self):
        self.credit_limits = config_manager.get('billing.credit_limits', {})
        self.task_costs = config_manager.get('billing.task_costs', {})

    def check_credits(self, user_id: str, task_type: str) -> bool:
        """检查用户积分是否足够"""
        # 实现自定义计费逻辑
        user_credits = self.get_user_credits(user_id)
        required_credits = self.get_task_cost(task_type)
        return user_credits >= required_credits

    def deduct_credits(self, user_id: str, amount: int, task_id: str) -> bool:
        """扣除用户积分"""
        # 实现自定义扣费逻辑
        # 这里可以集成第三方计费系统
        return True

    def get_user_credits(self, user_id: str) -> int:
        """获取用户当前积分"""
        # 实现自定义积分查询逻辑
        return self.credit_limits.get(user_id, 10)

    def get_task_cost(self, task_type: str) -> int:
        """获取任务类型的积分消耗"""
        return self.task_costs.get(task_type, 1)


class BillingAdapterFactory:
    """计费适配器工厂"""

    @staticmethod
    def create_adapter() -> IBillingAdapter:
        """根据配置创建计费适配器"""
        billing_type = config_manager.get('billing.type', 'saas')

        if billing_type == 'saas':
            return SaaSBillingAdapter()
        elif billing_type == 'free':
            return FreeBillingAdapter()
        elif billing_type == 'custom':
            return CustomBillingAdapter()
        else:
            raise ValueError(f"不支持的计费类型: {billing_type}")


# 全局计费适配器实例
billing_adapter = BillingAdapterFactory.create_adapter()
```

#### 1.4.3 验证适配器层
```bash
# 测试认证适配器
python3 -c "
from backend.core.adapters.auth_adapter import auth_adapter
print('认证适配器类型:', type(auth_adapter).__name__)
print('匿名用户信息:', auth_adapter.get_current_user())
"

# 测试计费适配器
python3 -c "
from backend.core.adapters.billing_adapter import billing_adapter
print('计费适配器类型:', type(billing_adapter).__name__)
print('任务成本:', billing_adapter.get_task_cost('image_generation'))
"
```

**完成标准**:
- ✅ 认证适配器创建完成
- ✅ 计费适配器创建完成
- ✅ 适配器工厂模式实现
- ✅ 基本功能测试通过

### 步骤1.5: 重构现有服务为插件 (第8-10天)

#### 1.5.1 创建图像处理插件
创建目录和文件:
```bash
# 创建插件目录
mkdir -p plugins

# 创建图像处理插件
touch plugins/image_processing_plugin.py
touch plugins/image_processing_config.yaml
```

创建文件 `plugins/image_processing_plugin.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理插件 - 基于现有ImageService的插件实现
"""
import os
import sys
import uuid
from typing import Dict, Any, List
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)
sys.path.insert(0, project_dir)

from backend.core.interfaces.business_plugin import IBusinessPlugin, PluginStatus, TaskResult
from backend.services.image_service import ImageService
from Translation.unified_translator import UnifiedTranslator


class ImageProcessingPlugin(IBusinessPlugin):
    """图像处理插件实现"""

    def __init__(self):
        self.image_service = ImageService()
        self.translator = UnifiedTranslator()
        self.status = PluginStatus.ACTIVE
        self.task_cache = {}  # 简单的任务缓存

    def get_plugin_info(self) -> Dict[str, Any]:
        """获取插件基本信息"""
        return {
            "name": "图像处理插件",
            "version": "2.0.0",
            "description": "基于BFL API的AI图像生成、编辑和风格迁移服务",
            "author": "BFL Team",
            "capabilities": [
                "图像生成",
                "图像编辑",
                "风格迁移",
                "图像修复",
                "智能翻译"
            ],
            "supported_formats": ["jpg", "jpeg", "png", "webp"],
            "max_resolution": "2048x2048",
            "api_version": "v1.0"
        }

    def get_task_types(self) -> List[str]:
        """获取支持的任务类型"""
        return [
            "image_generation",
            "image_editing",
            "style_transfer",
            "image_restore"
        ]

    def get_pricing_info(self) -> Dict[str, int]:
        """获取任务类型对应的积分消耗"""
        return {
            "image_generation": 2,
            "image_editing": 3,
            "style_transfer": 4,
            "image_restore": 3
        }

    def validate_task_data(self, task_data: Dict[str, Any]) -> bool:
        """验证任务数据格式"""
        try:
            # 检查必需字段
            if 'type' not in task_data:
                return False

            task_type = task_data['type']

            # 检查任务类型是否支持
            if task_type not in self.get_task_types():
                return False

            # 根据任务类型验证特定字段
            if task_type == 'image_generation':
                return 'prompt' in task_data

            elif task_type in ['image_editing', 'style_transfer', 'image_restore']:
                return 'input_image' in task_data

            return True

        except Exception as e:
            print(f"任务数据验证失败: {e}")
            return False

    def process_task(self, task_data: Dict[str, Any]) -> TaskResult:
        """处理业务任务"""
        try:
            task_id = str(uuid.uuid4())
            task_type = task_data['type']

            # 缓存任务信息
            self.task_cache[task_id] = {
                'status': 'processing',
                'type': task_type,
                'created_at': datetime.utcnow(),
                'data': task_data
            }

            # 根据任务类型处理
            if task_type == 'image_generation':
                result = self._process_generation(task_data, task_id)
            elif task_type == 'image_editing':
                result = self._process_editing(task_data, task_id)
            elif task_type == 'style_transfer':
                result = self._process_style_transfer(task_data, task_id)
            elif task_type == 'image_restore':
                result = self._process_restore(task_data, task_id)
            else:
                raise ValueError(f"不支持的任务类型: {task_type}")

            # 更新任务状态
            self.task_cache[task_id]['status'] = 'completed'
            self.task_cache[task_id]['completed_at'] = datetime.utcnow()
            self.task_cache[task_id]['result'] = result

            return TaskResult(True, result, task_id=task_id)

        except Exception as e:
            # 更新任务状态为失败
            if task_id in self.task_cache:
                self.task_cache[task_id]['status'] = 'failed'
                self.task_cache[task_id]['error'] = str(e)

            return TaskResult(False, error=str(e), task_id=task_id)

    def _process_generation(self, task_data: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """处理图像生成任务"""
        prompt = task_data.get('prompt', '')

        # 智能翻译提示词
        if self._is_chinese(prompt):
            translated_prompt = self.translator.translate(prompt)
            if translated_prompt:
                prompt = translated_prompt

        # 调用图像生成服务
        result = self.image_service.generate_image(
            prompt=prompt,
            model=task_data.get('model', 'flux-pro'),
            aspect_ratio=task_data.get('aspect_ratio', '1:1'),
            safety_tolerance=task_data.get('safety_tolerance', 2),
            output_format=task_data.get('output_format', 'png')
        )

        return {
            "task_id": task_id,
            "original_prompt": task_data.get('prompt'),
            "translated_prompt": prompt,
            "model": task_data.get('model', 'flux-pro'),
            "image_path": result.get('image_path'),
            "image_url": result.get('image_url'),
            "generation_time": result.get('generation_time'),
            "parameters": {
                "aspect_ratio": task_data.get('aspect_ratio', '1:1'),
                "safety_tolerance": task_data.get('safety_tolerance', 2),
                "output_format": task_data.get('output_format', 'png')
            }
        }

    def _process_editing(self, task_data: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """处理图像编辑任务"""
        input_image = task_data.get('input_image')
        prompt = task_data.get('prompt', '')

        # 翻译编辑提示词
        if self._is_chinese(prompt):
            translated_prompt = self.translator.translate(prompt)
            if translated_prompt:
                prompt = translated_prompt

        # 调用图像编辑服务
        result = self.image_service.edit_image(
            input_image=input_image,
            prompt=prompt,
            model=task_data.get('model', 'flux-pro'),
            strength=task_data.get('strength', 0.8)
        )

        return {
            "task_id": task_id,
            "input_image": input_image,
            "original_prompt": task_data.get('prompt'),
            "translated_prompt": prompt,
            "edited_image_path": result.get('image_path'),
            "edited_image_url": result.get('image_url'),
            "editing_time": result.get('generation_time')
        }

    def _process_style_transfer(self, task_data: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """处理风格迁移任务"""
        # 实现风格迁移逻辑
        return {
            "task_id": task_id,
            "message": "风格迁移功能开发中"
        }

    def _process_restore(self, task_data: Dict[str, Any], task_id: str) -> Dict[str, Any]:
        """处理图像修复任务"""
        # 实现图像修复逻辑
        return {
            "task_id": task_id,
            "message": "图像修复功能开发中"
        }

    def _is_chinese(self, text: str) -> bool:
        """检测文本是否包含中文"""
        for char in text:
            if '\u4e00' <= char <= '\u9fff':
                return True
        return False

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id not in self.task_cache:
            return {"error": "任务不存在"}

        task_info = self.task_cache[task_id]
        return {
            "task_id": task_id,
            "status": task_info['status'],
            "type": task_info['type'],
            "created_at": task_info['created_at'].isoformat(),
            "completed_at": task_info.get('completed_at', '').isoformat() if task_info.get('completed_at') else None,
            "result": task_info.get('result'),
            "error": task_info.get('error')
        }

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.task_cache:
            self.task_cache[task_id]['status'] = 'cancelled'
            return True
        return False

    def get_plugin_status(self) -> PluginStatus:
        """获取插件当前状态"""
        return self.status

    def health_check(self) -> Dict[str, Any]:
        """插件健康检查"""
        try:
            # 检查BFL API连接
            api_status = "healthy"
            try:
                # 这里可以添加实际的API健康检查
                pass
            except Exception:
                api_status = "unhealthy"

            # 检查翻译服务
            translator_status = "healthy"
            try:
                self.translator.translate("test")
            except Exception:
                translator_status = "unhealthy"

            overall_status = "healthy" if api_status == "healthy" and translator_status == "healthy" else "unhealthy"

            return {
                "status": overall_status,
                "components": {
                    "bfl_api": api_status,
                    "translator": translator_status
                },
                "task_cache_size": len(self.task_cache),
                "last_check": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.status = PluginStatus.ERROR
            return {
                "status": "error",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }


# 插件类别名，供插件管理器使用
Plugin = ImageProcessingPlugin
```

#### 1.5.2 创建插件配置文件
创建文件 `plugins/image_processing_config.yaml`:

```yaml
# 图像处理插件配置
plugin:
  name: "图像处理插件"
  enabled: true
  priority: 1
  max_concurrent_tasks: 3

# BFL API配置
bfl:
  api_key: ${BFL_API_KEY}
  base_url: "https://api.bfl.ml"
  timeout: 30
  retry_count: 3

# 翻译服务配置
translation:
  primary_service: "deepseek"
  backup_service: "ollama"
  auto_translate: true
  cache_translations: true

# 任务配置
tasks:
  image_generation:
    default_model: "flux-pro"
    max_resolution: "2048x2048"
    supported_formats: ["png", "jpg", "webp"]

  image_editing:
    default_strength: 0.8
    max_file_size: "10MB"

# 缓存配置
cache:
  task_cache_size: 1000
  result_cache_ttl: 3600  # 1小时
```

#### 1.5.3 测试插件功能
```bash
# 测试插件加载
python3 -c "
import sys
sys.path.append('.')
from plugins.image_processing_plugin import Plugin

plugin = Plugin()
print('插件信息:', plugin.get_plugin_info())
print('支持任务:', plugin.get_task_types())
print('计费信息:', plugin.get_pricing_info())
print('健康检查:', plugin.health_check())
"
```

**完成标准**:
- ✅ 图像处理插件创建完成
- ✅ 插件配置文件创建完成
- ✅ 插件基本功能测试通过
- ✅ 与现有ImageService兼容

### 步骤1.6: 集成测试和验证 (第11-14天)

#### 1.6.1 创建集成测试脚本
创建文件 `tests/test_decoupling_phase1.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解耦阶段1集成测试
"""
import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)
sys.path.insert(0, project_dir)


class TestDecouplingPhase1(unittest.TestCase):
    """解耦阶段1测试套件"""

    def setUp(self):
        """测试前准备"""
        self.test_config = {
            'mode': 'integrated',
            'auth': {'type': 'anonymous'},
            'billing': {'type': 'free'},
            'plugins': {'enabled': ['image_processing']}
        }

    def test_config_manager(self):
        """测试配置管理器"""
        from backend.core.config_manager import ConfigManager

        # 测试配置加载
        config_manager = ConfigManager()
        self.assertIsNotNone(config_manager.config)

        # 测试配置获取
        mode = config_manager.get('mode')
        self.assertIsNotNone(mode)

        # 测试嵌套配置获取
        auth_type = config_manager.get('auth.type')
        self.assertIsNotNone(auth_type)

        # 测试模式判断
        self.assertIsInstance(config_manager.is_saas_mode(), bool)
        self.assertIsInstance(config_manager.is_plugin_mode(), bool)

    def test_plugin_manager(self):
        """测试插件管理器"""
        from backend.core.managers.plugin_manager import PluginManager
        from plugins.image_processing_plugin import Plugin

        # 创建插件管理器
        plugin_manager = PluginManager()

        # 创建测试插件
        plugin = Plugin()

        # 测试插件注册
        plugin_manager.register_plugin(plugin)

        # 测试插件获取
        registered_plugin = plugin_manager.get_plugin("图像处理插件")
        self.assertIsNotNone(registered_plugin)

        # 测试任务类型映射
        task_plugin = plugin_manager.get_plugin_for_task("image_generation")
        self.assertIsNotNone(task_plugin)

        # 测试插件信息获取
        plugins_info = plugin_manager.get_all_plugins()
        self.assertGreater(len(plugins_info), 0)

    def test_auth_adapters(self):
        """测试认证适配器"""
        from backend.core.adapters.auth_adapter import (
            SaaSAuthAdapter, AnonymousAuthAdapter, LocalAuthAdapter
        )

        # 测试匿名认证适配器
        anonymous_adapter = AnonymousAuthAdapter()
        user = anonymous_adapter.get_current_user()
        self.assertIsNotNone(user)
        self.assertEqual(user['id'], 'anonymous')
        self.assertTrue(anonymous_adapter.is_authenticated())

        # 测试本地认证适配器
        local_adapter = LocalAuthAdapter()
        credentials = {'username': 'admin', 'password': 'admin123'}
        auth_user = local_adapter.authenticate_user(credentials)
        self.assertIsNotNone(auth_user)

        # 测试权限获取
        permissions = local_adapter.get_user_permissions('local_admin')
        self.assertIn('basic', permissions)
        self.assertIn('admin', permissions)

    def test_billing_adapters(self):
        """测试计费适配器"""
        from backend.core.adapters.billing_adapter import (
            FreeBillingAdapter, CustomBillingAdapter
        )

        # 测试免费计费适配器
        free_adapter = FreeBillingAdapter()
        self.assertTrue(free_adapter.check_credits('test_user', 'image_generation'))
        self.assertEqual(free_adapter.get_task_cost('image_generation'), 0)
        self.assertEqual(free_adapter.get_user_credits('test_user'), 999999)

        # 测试自定义计费适配器
        custom_adapter = CustomBillingAdapter()
        cost = custom_adapter.get_task_cost('image_generation')
        self.assertIsInstance(cost, int)

    def test_image_processing_plugin(self):
        """测试图像处理插件"""
        from plugins.image_processing_plugin import Plugin

        plugin = Plugin()

        # 测试插件信息
        info = plugin.get_plugin_info()
        self.assertEqual(info['name'], '图像处理插件')
        self.assertIn('capabilities', info)

        # 测试任务类型
        task_types = plugin.get_task_types()
        self.assertIn('image_generation', task_types)

        # 测试计费信息
        pricing = plugin.get_pricing_info()
        self.assertIn('image_generation', pricing)

        # 测试任务数据验证
        valid_task = {'type': 'image_generation', 'prompt': 'test prompt'}
        self.assertTrue(plugin.validate_task_data(valid_task))

        invalid_task = {'type': 'invalid_type'}
        self.assertFalse(plugin.validate_task_data(invalid_task))

        # 测试健康检查
        health = plugin.health_check()
        self.assertIn('status', health)

    @patch('backend.services.image_service.ImageService')
    def test_plugin_task_processing(self, mock_image_service):
        """测试插件任务处理"""
        from plugins.image_processing_plugin import Plugin

        # 模拟图像服务
        mock_service = MagicMock()
        mock_service.generate_image.return_value = {
            'image_path': '/test/path.png',
            'image_url': '/outputs/test.png',
            'generation_time': 5.2
        }
        mock_image_service.return_value = mock_service

        plugin = Plugin()

        # 测试图像生成任务
        task_data = {
            'type': 'image_generation',
            'prompt': '一只可爱的猫',
            'model': 'flux-pro'
        }

        result = plugin.process_task(task_data)
        self.assertTrue(result.success)
        self.assertIsNotNone(result.task_id)
        self.assertIn('image_path', result.data)

    def test_integration_workflow(self):
        """测试集成工作流"""
        from backend.core.managers.plugin_manager import PluginManager
        from backend.core.adapters.auth_adapter import AnonymousAuthAdapter
        from backend.core.adapters.billing_adapter import FreeBillingAdapter
        from plugins.image_processing_plugin import Plugin

        # 创建组件
        plugin_manager = PluginManager()
        auth_adapter = AnonymousAuthAdapter()
        billing_adapter = FreeBillingAdapter()

        # 注册插件
        plugin = Plugin()
        plugin_manager.register_plugin(plugin)

        # 模拟完整工作流
        user = auth_adapter.get_current_user()
        self.assertIsNotNone(user)

        # 检查积分
        has_credits = billing_adapter.check_credits(user['id'], 'image_generation')
        self.assertTrue(has_credits)

        # 处理任务
        task_data = {
            'type': 'image_generation',
            'prompt': 'test prompt'
        }

        result = plugin_manager.process_task(task_data)
        self.assertTrue(result.success)


class TestConfigurationModes(unittest.TestCase):
    """测试不同配置模式"""

    def test_saas_mode_config(self):
        """测试SaaS模式配置"""
        from backend.core.config_manager import ConfigManager

        # 模拟SaaS模式配置
        config_manager = ConfigManager()
        config_manager.set('mode', 'saas_only')
        config_manager.set('auth.type', 'saas')
        config_manager.set('billing.type', 'saas')

        self.assertTrue(config_manager.is_saas_mode())
        self.assertFalse(config_manager.is_standalone_mode())

    def test_standalone_mode_config(self):
        """测试独立模式配置"""
        from backend.core.config_manager import ConfigManager

        # 模拟独立模式配置
        config_manager = ConfigManager()
        config_manager.set('mode', 'plugin_only')
        config_manager.set('auth.type', 'anonymous')
        config_manager.set('billing.type', 'free')

        self.assertTrue(config_manager.is_standalone_mode())
        self.assertFalse(config_manager.is_saas_mode())


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
```

#### 1.6.2 运行集成测试
```bash
# 运行阶段1集成测试
cd /path/to/your/project
python -m pytest tests/test_decoupling_phase1.py -v

# 或者直接运行
python tests/test_decoupling_phase1.py
```

#### 1.6.3 性能基准测试
创建文件 `tests/benchmark_phase1.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解耦阶段1性能基准测试
"""
import time
import statistics
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)
sys.path.insert(0, project_dir)


class PerformanceBenchmark:
    """性能基准测试"""

    def __init__(self):
        self.results = {}

    def benchmark_config_manager(self, iterations=1000):
        """基准测试配置管理器"""
        from backend.core.config_manager import ConfigManager

        config_manager = ConfigManager()

        # 测试配置读取性能
        start_time = time.time()
        for _ in range(iterations):
            config_manager.get('auth.type')
            config_manager.get('billing.type')
            config_manager.get('plugins.enabled')
        end_time = time.time()

        avg_time = (end_time - start_time) / iterations * 1000  # 毫秒
        self.results['config_read'] = avg_time

        print(f"配置读取平均耗时: {avg_time:.3f}ms")

    def benchmark_plugin_manager(self, iterations=100):
        """基准测试插件管理器"""
        from backend.core.managers.plugin_manager import PluginManager
        from plugins.image_processing_plugin import Plugin

        plugin_manager = PluginManager()
        plugin = Plugin()
        plugin_manager.register_plugin(plugin)

        # 测试插件查找性能
        start_time = time.time()
        for _ in range(iterations):
            plugin_manager.get_plugin_for_task('image_generation')
            plugin_manager.get_all_plugins()
        end_time = time.time()

        avg_time = (end_time - start_time) / iterations * 1000
        self.results['plugin_lookup'] = avg_time

        print(f"插件查找平均耗时: {avg_time:.3f}ms")

    def benchmark_adapter_performance(self, iterations=1000):
        """基准测试适配器性能"""
        from backend.core.adapters.auth_adapter import AnonymousAuthAdapter
        from backend.core.adapters.billing_adapter import FreeBillingAdapter

        auth_adapter = AnonymousAuthAdapter()
        billing_adapter = FreeBillingAdapter()

        # 测试认证适配器
        start_time = time.time()
        for _ in range(iterations):
            auth_adapter.get_current_user()
            auth_adapter.is_authenticated()
        end_time = time.time()

        auth_time = (end_time - start_time) / iterations * 1000
        self.results['auth_adapter'] = auth_time

        # 测试计费适配器
        start_time = time.time()
        for _ in range(iterations):
            billing_adapter.check_credits('test_user', 'image_generation')
            billing_adapter.get_task_cost('image_generation')
        end_time = time.time()

        billing_time = (end_time - start_time) / iterations * 1000
        self.results['billing_adapter'] = billing_time

        print(f"认证适配器平均耗时: {auth_time:.3f}ms")
        print(f"计费适配器平均耗时: {billing_time:.3f}ms")

    def benchmark_concurrent_access(self, num_threads=10, tasks_per_thread=50):
        """基准测试并发访问"""
        from backend.core.managers.plugin_manager import PluginManager
        from plugins.image_processing_plugin import Plugin

        plugin_manager = PluginManager()
        plugin = Plugin()
        plugin_manager.register_plugin(plugin)

        def worker_task():
            times = []
            for _ in range(tasks_per_thread):
                start_time = time.time()
                plugin_manager.get_plugin_for_task('image_generation')
                end_time = time.time()
                times.append((end_time - start_time) * 1000)
            return times

        # 并发测试
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_task) for _ in range(num_threads)]
            all_times = []
            for future in as_completed(futures):
                all_times.extend(future.result())
        end_time = time.time()

        total_time = end_time - start_time
        avg_time = statistics.mean(all_times)
        p95_time = statistics.quantiles(all_times, n=20)[18]  # 95th percentile

        self.results['concurrent_avg'] = avg_time
        self.results['concurrent_p95'] = p95_time
        self.results['total_concurrent_time'] = total_time

        print(f"并发测试总耗时: {total_time:.3f}s")
        print(f"并发访问平均耗时: {avg_time:.3f}ms")
        print(f"并发访问P95耗时: {p95_time:.3f}ms")

    def run_all_benchmarks(self):
        """运行所有基准测试"""
        print("开始性能基准测试...")
        print("=" * 50)

        self.benchmark_config_manager()
        print("-" * 30)

        self.benchmark_plugin_manager()
        print("-" * 30)

        self.benchmark_adapter_performance()
        print("-" * 30)

        self.benchmark_concurrent_access()
        print("-" * 30)

        self.print_summary()

    def print_summary(self):
        """打印测试总结"""
        print("\n性能基准测试总结:")
        print("=" * 50)

        for test_name, result in self.results.items():
            if 'time' in test_name and 'concurrent' not in test_name:
                print(f"{test_name}: {result:.3f}s")
            else:
                print(f"{test_name}: {result:.3f}ms")

        # 性能评估
        print("\n性能评估:")
        if self.results.get('config_read', 0) < 1.0:
            print("✅ 配置读取性能: 优秀")
        else:
            print("⚠️ 配置读取性能: 需要优化")

        if self.results.get('plugin_lookup', 0) < 10.0:
            print("✅ 插件查找性能: 优秀")
        else:
            print("⚠️ 插件查找性能: 需要优化")

        if self.results.get('concurrent_p95', 0) < 50.0:
            print("✅ 并发性能: 优秀")
        else:
            print("⚠️ 并发性能: 需要优化")


if __name__ == '__main__':
    benchmark = PerformanceBenchmark()
    benchmark.run_all_benchmarks()
```

#### 1.6.4 运行性能测试
```bash
# 运行性能基准测试
python tests/benchmark_phase1.py
```

#### 1.6.5 功能验证脚本
创建文件 `scripts/validate_phase1.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段1功能验证脚本
"""
import sys
import os
import traceback

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_dir = os.path.dirname(current_dir)
sys.path.insert(0, project_dir)


class Phase1Validator:
    """阶段1功能验证器"""

    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []

    def run_test(self, test_name, test_func):
        """运行单个测试"""
        try:
            print(f"运行测试: {test_name}...")
            test_func()
            print(f"✅ {test_name} - 通过")
            self.passed_tests += 1
            self.test_results.append((test_name, True, None))
        except Exception as e:
            print(f"❌ {test_name} - 失败: {str(e)}")
            self.failed_tests += 1
            self.test_results.append((test_name, False, str(e)))
            if '--verbose' in sys.argv:
                traceback.print_exc()

    def validate_directory_structure(self):
        """验证目录结构"""
        required_dirs = [
            'backend/core',
            'backend/core/interfaces',
            'backend/core/adapters',
            'backend/core/managers',
            'plugins',
            'config'
        ]

        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                raise FileNotFoundError(f"必需目录不存在: {dir_path}")

    def validate_interface_files(self):
        """验证接口文件"""
        required_files = [
            'backend/core/interfaces/business_plugin.py',
            'backend/core/config_manager.py',
            'backend/core/managers/plugin_manager.py',
            'backend/core/adapters/auth_adapter.py',
            'backend/core/adapters/billing_adapter.py'
        ]

        for file_path in required_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"必需文件不存在: {file_path}")

    def validate_plugin_files(self):
        """验证插件文件"""
        plugin_files = [
            'plugins/image_processing_plugin.py',
            'plugins/image_processing_config.yaml'
        ]

        for file_path in plugin_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"插件文件不存在: {file_path}")

    def validate_config_manager(self):
        """验证配置管理器"""
        from backend.core.config_manager import ConfigManager

        config_manager = ConfigManager()

        # 测试基本配置获取
        mode = config_manager.get('mode')
        if not mode:
            raise ValueError("无法获取运行模式配置")

        # 测试嵌套配置获取
        auth_type = config_manager.get('auth.type')
        if not auth_type:
            raise ValueError("无法获取认证类型配置")

        # 测试模式判断方法
        if not hasattr(config_manager, 'is_saas_mode'):
            raise AttributeError("配置管理器缺少is_saas_mode方法")

    def validate_plugin_manager(self):
        """验证插件管理器"""
        from backend.core.managers.plugin_manager import PluginManager
        from plugins.image_processing_plugin import Plugin

        plugin_manager = PluginManager()
        plugin = Plugin()

        # 测试插件注册
        plugin_manager.register_plugin(plugin)

        # 测试插件获取
        registered_plugin = plugin_manager.get_plugin("图像处理插件")
        if not registered_plugin:
            raise ValueError("插件注册失败")

        # 测试任务类型映射
        task_plugin = plugin_manager.get_plugin_for_task("image_generation")
        if not task_plugin:
            raise ValueError("任务类型映射失败")

    def validate_auth_adapters(self):
        """验证认证适配器"""
        from backend.core.adapters.auth_adapter import (
            AuthAdapterFactory, AnonymousAuthAdapter, LocalAuthAdapter
        )

        # 测试工厂模式
        adapter = AuthAdapterFactory.create_adapter()
        if not adapter:
            raise ValueError("认证适配器工厂创建失败")

        # 测试匿名适配器
        anonymous_adapter = AnonymousAuthAdapter()
        user = anonymous_adapter.get_current_user()
        if not user or user['id'] != 'anonymous':
            raise ValueError("匿名认证适配器功能异常")

        # 测试本地适配器
        local_adapter = LocalAuthAdapter()
        credentials = {'username': 'admin', 'password': 'admin123'}
        auth_user = local_adapter.authenticate_user(credentials)
        if not auth_user:
            raise ValueError("本地认证适配器认证失败")

    def validate_billing_adapters(self):
        """验证计费适配器"""
        from backend.core.adapters.billing_adapter import (
            BillingAdapterFactory, FreeBillingAdapter
        )

        # 测试工厂模式
        adapter = BillingAdapterFactory.create_adapter()
        if not adapter:
            raise ValueError("计费适配器工厂创建失败")

        # 测试免费适配器
        free_adapter = FreeBillingAdapter()
        if not free_adapter.check_credits('test_user', 'image_generation'):
            raise ValueError("免费计费适配器功能异常")

        if free_adapter.get_task_cost('image_generation') != 0:
            raise ValueError("免费计费适配器计费逻辑错误")

    def validate_image_plugin(self):
        """验证图像处理插件"""
        from plugins.image_processing_plugin import Plugin

        plugin = Plugin()

        # 测试插件信息
        info = plugin.get_plugin_info()
        if not info or info['name'] != '图像处理插件':
            raise ValueError("插件信息获取失败")

        # 测试任务类型
        task_types = plugin.get_task_types()
        if 'image_generation' not in task_types:
            raise ValueError("插件任务类型配置错误")

        # 测试数据验证
        valid_task = {'type': 'image_generation', 'prompt': 'test'}
        if not plugin.validate_task_data(valid_task):
            raise ValueError("插件数据验证功能异常")

        # 测试健康检查
        health = plugin.health_check()
        if 'status' not in health:
            raise ValueError("插件健康检查功能异常")

    def validate_integration(self):
        """验证集成功能"""
        from backend.core.managers.plugin_manager import PluginManager
        from backend.core.adapters.auth_adapter import AnonymousAuthAdapter
        from backend.core.adapters.billing_adapter import FreeBillingAdapter
        from plugins.image_processing_plugin import Plugin

        # 创建组件
        plugin_manager = PluginManager()
        auth_adapter = AnonymousAuthAdapter()
        billing_adapter = FreeBillingAdapter()

        # 注册插件
        plugin = Plugin()
        plugin_manager.register_plugin(plugin)

        # 测试完整工作流
        user = auth_adapter.get_current_user()
        if not user:
            raise ValueError("用户认证失败")

        has_credits = billing_adapter.check_credits(user['id'], 'image_generation')
        if not has_credits:
            raise ValueError("积分检查失败")

        # 测试任务处理
        task_data = {'type': 'image_generation', 'prompt': 'test prompt'}
        result = plugin_manager.process_task(task_data)
        if not result.success:
            raise ValueError(f"任务处理失败: {result.error}")

    def run_all_validations(self):
        """运行所有验证"""
        print("开始阶段1功能验证...")
        print("=" * 60)

        validations = [
            ("目录结构验证", self.validate_directory_structure),
            ("接口文件验证", self.validate_interface_files),
            ("插件文件验证", self.validate_plugin_files),
            ("配置管理器验证", self.validate_config_manager),
            ("插件管理器验证", self.validate_plugin_manager),
            ("认证适配器验证", self.validate_auth_adapters),
            ("计费适配器验证", self.validate_billing_adapters),
            ("图像处理插件验证", self.validate_image_plugin),
            ("集成功能验证", self.validate_integration)
        ]

        for test_name, test_func in validations:
            self.run_test(test_name, test_func)

        self.print_summary()

    def print_summary(self):
        """打印验证总结"""
        print("\n" + "=" * 60)
        print("阶段1功能验证总结")
        print("=" * 60)

        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0

        print(f"总测试数: {total_tests}")
        print(f"通过测试: {self.passed_tests}")
        print(f"失败测试: {self.failed_tests}")
        print(f"成功率: {success_rate:.1f}%")

        if self.failed_tests > 0:
            print("\n失败的测试:")
            for test_name, passed, error in self.test_results:
                if not passed:
                    print(f"  ❌ {test_name}: {error}")

        print("\n" + "=" * 60)
        if self.failed_tests == 0:
            print("🎉 阶段1验证完全通过！可以进入阶段2。")
        else:
            print("⚠️ 存在失败的测试，请修复后重新验证。")


if __name__ == '__main__':
    validator = Phase1Validator()
    validator.run_all_validations()
```

#### 1.6.6 运行完整验证
```bash
# 运行阶段1完整验证
python scripts/validate_phase1.py

# 详细模式（显示错误堆栈）
python scripts/validate_phase1.py --verbose
```

**阶段1完成标准**:
- ✅ 所有集成测试通过
- ✅ 性能基准测试达标
- ✅ 功能验证脚本100%通过
- ✅ 现有功能保持正常
- ✅ 代码覆盖率 > 80%

---

## 🔄 阶段2: 模块分离和独立化 (3-4周)

### 步骤2.1: 创建SaaS核心平台 (第15-18天)

#### 2.1.1 创建SaaS核心应用结构
```bash
# 创建SaaS平台目录
mkdir -p saas_platform
mkdir -p saas_platform/core
mkdir -p saas_platform/api
mkdir -p saas_platform/config
mkdir -p saas_platform/templates
mkdir -p saas_platform/static

# 复制核心组件
cp -r backend/core/* saas_platform/core/
cp -r backend/models saas_platform/
cp -r backend/services/credit_service.py saas_platform/services/
cp -r backend/routes/user_routes.py saas_platform/api/
cp -r backend/routes/admin_routes.py saas_platform/api/
```

#### 2.1.2 创建SaaS平台主应用
创建文件 `saas_platform/app.py`:
