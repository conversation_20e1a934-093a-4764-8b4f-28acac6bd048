import FileUploader from '../modules/fileUploader.js';
import TaskProcessor from '../modules/taskProcessor.js';
import apiClient from '../modules/apiClient.js';
import uiManager from '../modules/uiManager.js';

// Global uploader variable for access in global functions
let globalUploader = null;

// 任务状态常量
const TASK_STATUS = {
    NOT_FOUND: 'Task not found',
    PENDING: 'Pending',
    REQUEST_MODERATED: 'Request Moderated',
    CONTENT_MODERATED: 'Content Moderated',
    READY: 'Ready',
    ERROR: 'Error'
};

// 轮询配置
const POLLING_CONFIG = {
    MAX_RETRIES: 60,    // 最大重试次数 (2分钟)
    INTERVAL: 2000,     // 轮询间隔（毫秒）
};

document.addEventListener('DOMContentLoaded', () => {
    // 1. 获取关键DOM元素
    const editForm = document.getElementById('editForm');
    const resetBtn = document.getElementById('resetBtn');
    const translateBtn = document.getElementById('translateBtn');
    const useTranslatedBtn = document.getElementById('useTranslatedBtn');
    const hideTranslatedBtn = document.getElementById('hideTranslatedBtn');
    const promptEl = document.getElementById('prompt');
    const translatedPromptEl = document.getElementById('translatedPrompt');
    const translatedPromptArea = document.getElementById('translatedPromptArea');
    const translateStatus = document.getElementById('translateStatus');

    // 2. 初始化文件上传器
    const uploader = new FileUploader('#uploadArea', 'image');
    globalUploader = uploader;

    // 3. 初始化任务处理器
    // 这是核心，它将表单、上传器和UI状态管理连接起来
    const taskProcessor = new TaskProcessor({
        form: editForm,
        uploader: uploader,
        submitBtnSelector: '#editBtn',
        statusSelector: '#statusArea',
        resultSelector: '#resultArea'
    });

    // 4. 绑定其他UI事件
    if (resetBtn) {
        resetBtn.addEventListener('click', () => {
            taskProcessor.reset();
            translatedPromptArea.style.display = 'none';
        });
    }

    // 翻译功能保持不变，因为它不属于主任务流程
    if (translateBtn) {
        translateBtn.addEventListener('click', translatePrompt);
    }

    if (useTranslatedBtn) {
        useTranslatedBtn.addEventListener('click', () => {
            if (translatedPromptEl.value.trim()) {
                promptEl.value = translatedPromptEl.value.trim();
                translatedPromptArea.style.display = 'none';
            }
        });
    }

    if (hideTranslatedBtn) {
        hideTranslatedBtn.addEventListener('click', () => {
            translatedPromptArea.style.display = 'none';
        });
    }

    async function translatePrompt() {
        const textToTranslate = promptEl.value.trim();
        if (!textToTranslate) {
            alert('请输入需要翻译的编辑指令。');
            return;
        }

        uiManager.showLoadingState(translateBtn, "翻译中...");
        translateStatus.style.display = 'inline';
        translateStatus.textContent = '正在翻译...';
        translateStatus.className = 'ms-2 text-muted';

        try {
            const translatedText = await apiClient.translateText(textToTranslate);
            translatedPromptEl.value = translatedText;
            translatedPromptArea.style.display = 'block';
            translateStatus.textContent = '翻译完成';
            translateStatus.className = 'ms-2 text-success';
            setTimeout(() => {
                translateStatus.style.display = 'none';
            }, 3000);
        } catch (error) {
            uiManager.showError(translateStatus, `翻译失败: ${error.message}`);
            translateStatus.className = 'ms-2 text-danger';
        } finally {
            uiManager.hideLoadingState(translateBtn);
        }
    }

    /**
     * 显示对比结果。
     * @param {object} statusData - The final status data object from polling.
     */
    function displayComparisonResult(statusData) {
        console.log('Displaying comparison result for:', statusData);
        
        // 从statusData中提取结果信息
        let processedImageUrl = null;
        
        // 根据实际API响应结构解析图片URL
        if (statusData.result) {
            if (statusData.result.sample) {
                // 如果result.sample是图片URL
                processedImageUrl = statusData.result.sample;
            } else if (typeof statusData.result === 'string') {
                // 如果result直接是图片URL字符串或文件路径
                processedImageUrl = statusData.result;
            }
        }
        
        if (!processedImageUrl) {
            console.error('No processed image URL found in result:', statusData.result);
            uiManager.updateStatus('#statusArea', 'error', '编辑完成但未找到结果图像');
             return;
        }
        
        // 构建图像URL - 处理不同的路径格式
        let finalProcessedUrl;
        if (/^https?:\/\//i.test(processedImageUrl)) {
            // 远程 URL (http/https)
            finalProcessedUrl = processedImageUrl;
        } else if (processedImageUrl.startsWith('/')) {
            // 已带有根路径的相对 URL
            finalProcessedUrl = processedImageUrl;
        } else {
            // 可能是本地磁盘路径 (含反斜杠) 或纯文件名
            const fileName = processedImageUrl.split(/[\\/]/).pop();
            finalProcessedUrl = `/outputs/${fileName}`;
        }
        
        // 显示结果
        resultArea.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-images"></i> 编辑结果
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <img src="${finalProcessedUrl}" class="img-fluid rounded shadow-lg" alt="编辑后的图像"
                             onerror="this.onerror=null; this.src='/static/img/error-placeholder.jpg';"
                             style="max-height: 400px;">
                        <div class="mt-3">
                            <a href="${finalProcessedUrl}" class="btn btn-success" download target="_blank">
                                <i class="fas fa-download me-2"></i>下载编辑后的图像
                            </a>
                        </div>
                    </div>
                    
                    <!-- 如果可以获取原图，显示对比 -->
                    <div class="mt-3" id="comparisonArea" style="display: none;">
                        <hr>
                        <h6 class="text-center mb-3">
                            <i class="fas fa-columns"></i> 前后对比
                        </h6>
                        <div class="row">
                            <div class="col-md-6 text-center">
                                <h6 class="text-muted">原图</h6>
                                <img id="originalImage" src="" class="img-fluid rounded" alt="原图" style="max-height: 300px;">
                        </div>
                        <div class="col-md-6 text-center">
                                <h6 class="text-muted">编辑后</h6>
                                <img src="${finalProcessedUrl}" class="img-fluid rounded" alt="编辑后" style="max-height: 300px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 尝试显示原图对比（如果上传的文件还在内存中）
        if (globalUploader && globalUploader.getFile()) {
            const originalFile = globalUploader.getFile();
            if (originalFile) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const originalImage = document.getElementById('originalImage');
                    const comparisonArea = document.getElementById('comparisonArea');
                    if (originalImage && comparisonArea) {
                        originalImage.src = e.target.result;
                        comparisonArea.style.display = 'block';
                    }
                };
                reader.readAsDataURL(originalFile);
            }
        }
    }
});

// Global functions for HTML onclick handlers
window.useExample = function(element) {
    const text = element.textContent.split(':')[1].trim();
    $('#prompt').val(text);
    $('#translatedPromptArea').hide();
};

window.resetForm = function() {
    $('#editForm')[0].reset();
    if (globalUploader) {
        globalUploader.reset();
    }
    $('#statusArea').html(`
        <div class="text-center text-muted">
            <i class="fas fa-edit fa-3x mb-3"></i>
            <p>上传图像并填写编辑指令来开始编辑</p>
        </div>
    `);
    $('#resultArea').html('');
    $('#translatedPromptArea').hide();
    
    // 取消任何正在进行的轮询
    apiClient.cancelPolling();
}; 