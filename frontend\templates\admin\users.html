{% extends "base.html" %}

{% block title %}用户管理 - 管理员面板{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-users"></i> 用户管理</h2>
                <div class="btn-group">
                    <a href="{{ url_for('pages.admin_dashboard_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回仪表板
                    </a>
                    <button class="btn btn-primary" onclick="showAddUserModal()">
                        <i class="fas fa-user-plus"></i> 添加用户
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索用户名或邮箱...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="userTypeFilter">
                                <option value="">所有用户类型</option>
                                <option value="free">免费用户</option>
                                <option value="premium">付费用户</option>
                                <option value="pro">Pro用户</option>
                                <option value="enterprise">企业用户</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">非活跃</option>
                                <option value="verified">已验证</option>
                                <option value="unverified">未验证</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary w-100" onclick="searchUsers()">
                                <i class="fas fa-filter"></i> 筛选
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> 用户列表</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-info" onclick="refreshUsers()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>用户类型</th>
                                    <th>积分</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="9" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页按钮将通过JavaScript生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户详情模态框 -->
<div class="modal fade" id="userDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <!-- 用户详情内容将通过JavaScript加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="showEditUserModal()">编辑用户</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="mb-3">
                        <label class="form-label">用户类型</label>
                        <select class="form-select" id="editUserType">
                            <option value="free">免费用户</option>
                            <option value="premium">付费用户</option>
                            <option value="pro">Pro用户</option>
                            <option value="enterprise">企业用户</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">可用积分</label>
                        <input type="number" class="form-control" id="editTotalCredits" min="0">
                        <div class="form-text">设置用户的最终可用积分数量（会重置积分使用记录）</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">每日限制</label>
                        <input type="number" class="form-control" id="editDailyLimit" min="-1" 
                               placeholder="-1表示无限制">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive">
                            <label class="form-check-label" for="editIsActive">
                                账户激活
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsVerified">
                            <label class="form-check-label" for="editIsVerified">
                                邮箱已验证
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">保存更改</button>
            </div>
        </div>
    </div>
</div>

<!-- 积分调整模态框 -->
<div class="modal fade" id="creditAdjustModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">调整用户积分</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">当前可用积分</label>
                    <input type="text" class="form-control" id="currentCredits" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">调整方式</label>
                    <div class="btn-group w-100" role="group">
                        <input type="radio" class="btn-check" name="adjustType" id="adjustTypeSet" value="set" checked>
                        <label class="btn btn-outline-primary" for="adjustTypeSet">设置为</label>

                        <input type="radio" class="btn-check" name="adjustType" id="adjustTypeAdd" value="add">
                        <label class="btn btn-outline-success" for="adjustTypeAdd">增加</label>

                        <input type="radio" class="btn-check" name="adjustType" id="adjustTypeSubtract" value="subtract">
                        <label class="btn btn-outline-danger" for="adjustTypeSubtract">减少</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">积分数量</label>
                    <input type="number" class="form-control" id="creditAmount" min="0" placeholder="请输入积分数量">
                </div>
                <div class="mb-3">
                    <label class="form-label">调整原因</label>
                    <textarea class="form-control" id="creditReason" rows="3" placeholder="请输入调整原因（可选）"></textarea>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>设置为：</strong>将用户积分设置为指定数量（会重置使用记录）</li>
                        <li><strong>增加：</strong>在当前积分基础上增加指定数量</li>
                        <li><strong>减少：</strong>在当前积分基础上减少指定数量</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="adjustUserCredits()">确认调整</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentUserId = null;

$(document).ready(function() {
    // 检查管理员权限
    checkAdminAuth();
    
    // 加载用户列表
    loadUsers();
    
    // 绑定搜索事件
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchUsers();
        }
    });
});

function checkAdminAuth() {
    const token = getToken();
    
    if (!token) {
        showError('请先登录');
        setTimeout(() => {
            window.location.href = '/login';
        }, 2000);
        return;
    }
    
    // 验证管理员权限
    $.ajax({
        url: '/api/user/profile',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success && response.user) {
                if (response.user.user_type !== 'enterprise') {
                    showError('需要管理员权限访问此页面');
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } else {
                showError('获取用户信息失败');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            }
        },
        error: function() {
            showError('认证失败，请重新登录');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }
    });
}

function getToken() {
    // 优先从localStorage获取
    let token = localStorage.getItem('access_token');
    if (token) return token;
    
    // 从Cookie获取
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'access_token') {
            return value;
        }
    }
    return null;
}

function loadUsers(page = 1) {
    const token = getToken();
    const search = $('#searchInput').val();
    const userType = $('#userTypeFilter').val();
    const status = $('#statusFilter').val();

    let url = `/api/admin/users?page=${page}&per_page=20`;
    if (search) url += `&search=${encodeURIComponent(search)}`;
    if (userType) url += `&user_type=${userType}`;
    if (status) url += `&status=${status}`;

    $.ajax({
        url: url,
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success) {
                displayUsers(response.users);
                displayPagination(response.pagination);
                currentPage = page;
            } else {
                console.error('API返回错误:', response.message);
                showError('加载用户列表失败: ' + (response.message || '未知错误'));
            }
        },
        error: function(xhr) {
            console.error('加载用户列表失败:', xhr.responseText);
            let errorMessage = '加载用户列表失败';
            try {
                const errorData = JSON.parse(xhr.responseText);
                if (errorData.message) {
                    errorMessage += ': ' + errorData.message;
                }
            } catch (e) {
                errorMessage += ': 网络连接错误';
            }
            showError(errorMessage);
        }
    });
}

function displayUsers(users) {
    const tbody = $('#usersTableBody');
    tbody.empty();
    
    if (users.length === 0) {
        tbody.append('<tr><td colspan="9" class="text-center text-muted">没有找到用户</td></tr>');
        return;
    }
    
    users.forEach(user => {
        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td><span class="badge bg-${getUserTypeBadgeClass(user.user_type)}">${getUserTypeText(user.user_type)}</span></td>
                <td>${user.available_credits}/${user.total_credits}</td>
                <td>
                    ${user.is_active ? '<span class="badge bg-success">活跃</span>' : '<span class="badge bg-secondary">非活跃</span>'}
                    ${user.is_verified ? '<span class="badge bg-info ms-1">已验证</span>' : '<span class="badge bg-warning ms-1">未验证</span>'}
                </td>
                <td>${formatDate(user.created_at)}</td>
                <td>${user.last_login ? formatDate(user.last_login) : '从未登录'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="showUserDetail('${user.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="editUser('${user.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser('${user.id}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function getUserTypeBadgeClass(userType) {
    switch(userType) {
        case 'free': return 'secondary';
        case 'premium': return 'primary';
        case 'pro': return 'success';
        case 'enterprise': return 'warning';
        default: return 'secondary';
    }
}

function getUserTypeText(userType) {
    switch(userType) {
        case 'free': return '免费';
        case 'premium': return '付费';
        case 'pro': return 'Pro';
        case 'enterprise': return '企业';
        default: return userType;
    }
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleString('zh-CN');
}

function displayPagination(pagination) {
    const paginationEl = $('#pagination');
    paginationEl.empty();
    
    // 上一页
    if (pagination.has_prev) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.page - 1})">上一页</a>
            </li>
        `);
    }
    
    // 页码
    for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
        const activeClass = i === pagination.page ? 'active' : '';
        paginationEl.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            </li>
        `);
    }
    
    // 下一页
    if (pagination.has_next) {
        paginationEl.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.page + 1})">下一页</a>
            </li>
        `);
    }
}

function searchUsers() {
    loadUsers(1);
}

function refreshUsers() {
    loadUsers(currentPage);
}

function showUserDetail(userId) {
    currentUserId = userId; // 设置当前用户ID，供编辑功能使用
    const token = getToken();

    $.ajax({
        url: `/api/admin/users/${userId}`,
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success) {
                displayUserDetail(response.user);
                $('#userDetailModal').modal('show');
            } else {
                showError('获取用户详情失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('获取用户详情失败:', xhr.responseText);
            showError('获取用户详情失败');
        }
    });
}

function editUser(userId) {
    currentUserId = userId;

    // 先获取用户详情
    const token = getToken();
    $.ajax({
        url: `/api/admin/users/${userId}`,
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + token
        },
        success: function(response) {
            if (response.success) {
                const user = response.user;

                // 填充编辑表单
                $('#editUserId').val(user.id);
                $('#editUserType').val(user.user_type);
                $('#editIsActive').prop('checked', user.is_active);
                $('#editIsVerified').prop('checked', user.is_verified);
                $('#editTotalCredits').val(user.available_credits); // 显示可用积分
                $('#editDailyLimit').val(user.daily_limit);

                $('#editUserModal').modal('show');
            } else {
                showError('获取用户信息失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('获取用户信息失败:', xhr.responseText);
            showError('获取用户信息失败');
        }
    });
}

function deleteUser(userId) {
    if (confirm('确定要注销这个用户吗？注销后用户将无法登录，但历史记录会保留。')) {
        const token = getToken();

        $.ajax({
            url: `/api/admin/users/${userId}`,
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer ' + token
            },
            success: function(response) {
                if (response.success) {
                    showSuccess('用户注销成功');
                    loadUsers(currentPage); // 重新加载用户列表
                } else {
                    showError('注销用户失败: ' + response.message);
                }
            },
            error: function(xhr) {
                console.error('注销用户失败:', xhr.responseText);
                let errorMessage = '注销用户失败';
                try {
                    const errorData = JSON.parse(xhr.responseText);
                    if (errorData.message) {
                        errorMessage += ': ' + errorData.message;
                    }
                } catch (e) {
                    errorMessage += ': 网络连接错误';
                }
                showError(errorMessage);
            }
        });
    }
}

function showAddUserModal() {
    // TODO: 实现添加用户功能
    alert('添加用户功能待实现');
}

function exportUsers() {
    // TODO: 实现用户数据导出功能
    alert('用户数据导出功能待实现');
}

function displayUserDetail(user) {
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>用户ID:</td><td>${user.id}</td></tr>
                    <tr><td>用户名:</td><td>${user.username || '未设置'}</td></tr>
                    <tr><td>邮箱:</td><td>${user.email || '未设置'}</td></tr>
                    <tr><td>手机号:</td><td>${user.phone}</td></tr>
                    <tr><td>用户类型:</td><td><span class="badge bg-${getUserTypeBadgeClass(user.user_type)}">${getUserTypeText(user.user_type)}</span></td></tr>
                    <tr><td>状态:</td><td>
                        ${user.is_active ? '<span class="badge bg-success">活跃</span>' : '<span class="badge bg-secondary">非活跃</span>'}
                        ${user.is_verified ? '<span class="badge bg-info ms-1">已验证</span>' : '<span class="badge bg-warning ms-1">未验证</span>'}
                    </td></tr>
                    <tr><td>注册时间:</td><td>${formatDate(user.created_at)}</td></tr>
                    <tr><td>最后登录:</td><td>${user.last_login ? formatDate(user.last_login) : '从未登录'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>积分信息</h6>
                <table class="table table-sm">
                    <tr><td>总积分:</td><td>${user.total_credits}</td></tr>
                    <tr><td>已使用:</td><td>${user.used_credits}</td></tr>
                    <tr><td>可用积分:</td><td><strong class="text-primary">${user.available_credits}</strong></td></tr>
                </table>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="showCreditAdjustModal('${user.id}', ${user.available_credits})">
                        <i class="fas fa-coins"></i> 调整积分
                    </button>
                </div>
                    <tr><td>每日限制:</td><td>${user.daily_limit === -1 ? '无限制' : user.daily_limit}</td></tr>
                    <tr><td>今日已用:</td><td>${user.daily_used}</td></tr>
                </table>

                <h6>任务统计</h6>
                <table class="table table-sm">
                    <tr><td>总任务数:</td><td>${user.task_stats ? user.task_stats.total_tasks : 0}</td></tr>
                    <tr><td>完成任务:</td><td>${user.task_stats ? user.task_stats.completed_tasks : 0}</td></tr>
                    <tr><td>成功率:</td><td>${user.task_stats ? user.task_stats.success_rate : 0}%</td></tr>
                </table>
            </div>
        </div>

        ${user.recent_transactions && user.recent_transactions.length > 0 ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>最近积分交易</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>类型</th>
                                <th>数量</th>
                                <th>描述</th>
                                <th>时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${user.recent_transactions.map(t => `
                                <tr>
                                    <td><span class="badge bg-${t.type === 'earn' ? 'success' : 'danger'}">${t.type === 'earn' ? '获得' : '消费'}</span></td>
                                    <td>${t.amount > 0 ? '+' : ''}${t.amount}</td>
                                    <td>${t.description}</td>
                                    <td>${formatDate(t.created_at)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        ` : ''}
    `;

    $('#userDetailContent').html(content);
}

function updateUser() {
    const token = getToken();
    const userId = $('#editUserId').val();

    const data = {
        user_type: $('#editUserType').val(),
        is_active: $('#editIsActive').is(':checked'),
        is_verified: $('#editIsVerified').is(':checked'),
        total_credits: parseInt($('#editTotalCredits').val()),
        daily_limit: parseInt($('#editDailyLimit').val())
    };

    $.ajax({
        url: `/api/admin/users/${userId}`,
        method: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showSuccess('用户信息更新成功');
                $('#editUserModal').modal('hide');
                loadUsers(currentPage); // 重新加载用户列表
            } else {
                showError('更新用户信息失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('更新用户信息失败:', xhr.responseText);
            let errorMessage = '更新用户信息失败';
            try {
                const errorData = JSON.parse(xhr.responseText);
                if (errorData.message) {
                    errorMessage += ': ' + errorData.message;
                }
            } catch (e) {
                errorMessage += ': 网络连接错误';
            }
            showError(errorMessage);
        }
    });
}

let currentCreditUserId = null;

function showCreditAdjustModal(userId, currentCredits) {
    currentCreditUserId = userId;
    $('#currentCredits').val(currentCredits);
    $('#creditAmount').val('');
    $('#creditReason').val('');
    $('input[name="adjustType"][value="set"]').prop('checked', true);
    $('#creditAdjustModal').modal('show');
}

function adjustUserCredits() {
    const token = getToken();
    const adjustType = $('input[name="adjustType"]:checked').val();
    const amount = parseInt($('#creditAmount').val());
    const reason = $('#creditReason').val();
    const currentCredits = parseInt($('#currentCredits').val());

    if (!amount || amount < 0) {
        showError('请输入有效的积分数量');
        return;
    }

    let finalAmount;
    let apiUrl;
    let requestData;

    if (adjustType === 'set') {
        // 设置积分：使用用户更新API
        apiUrl = `/api/admin/users/${currentCreditUserId}`;
        requestData = {
            total_credits: amount
        };
    } else {
        // 增加或减少积分：使用积分调整API
        finalAmount = adjustType === 'add' ? amount : -amount;
        apiUrl = `/api/admin/users/${currentCreditUserId}/credits`;
        requestData = {
            adjustment: finalAmount,
            reason: reason || `管理员${adjustType === 'add' ? '增加' : '减少'}积分`
        };
    }

    $.ajax({
        url: apiUrl,
        method: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(requestData),
        success: function(response) {
            if (response.success) {
                showSuccess('积分调整成功');
                $('#creditAdjustModal').modal('hide');
                loadUsers(currentPage); // 重新加载用户列表

                // 如果用户详情模态框是打开的，也刷新它
                if ($('#userDetailModal').hasClass('show')) {
                    viewUser(currentCreditUserId);
                }
            } else {
                showError('积分调整失败: ' + response.message);
            }
        },
        error: function(xhr) {
            console.error('积分调整失败:', xhr.responseText);
            let errorMessage = '积分调整失败';
            try {
                const errorData = JSON.parse(xhr.responseText);
                if (errorData.message) {
                    errorMessage += ': ' + errorData.message;
                }
            } catch (e) {
                errorMessage += ': 网络连接错误';
            }
            showError(errorMessage);
        }
    });
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert-success').fadeOut();
    }, 3000);
}

function showEditUserModal() {
    // 从用户详情模态框切换到编辑模态框
    if (currentUserId) {
        $('#userDetailModal').modal('hide');
        editUser(currentUserId);
    }
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.container-fluid').prepend(alertHtml);
}
</script>
{% endblock %}
