import apiClient from './apiClient.js';
import uiManager from './uiManager.js';

/**
 * A comprehensive, reusable module for processing background tasks.
 * It handles form validation, submission, polling, and UI updates.
 * This version is robust and handles forms with or without file uploads.
 */
class TaskProcessor {
    /**
     * @param {object} options - The configuration options.
     * @param {HTMLFormElement} options.form - The form element to process.
     * @param {string} options.submitBtnSelector - The CSS selector for the submit button.
     * @param {string} options.statusSelector - The CSS selector for the status display area.
     * @param {string} options.resultSelector - The CSS selector for the result display area.
     * @param {object} [options.uploader] - Optional. The FileUploader instance, if the form includes a file upload.
     */
    constructor({ form, submitBtnSelector, statusSelector, resultSelector, uploader = null }) {
        if (!form || !submitBtnSelector || !statusSelector || !resultSelector) {
            throw new Error('TaskProcessor requires a form and all UI selectors.');
        }
        this.form = form;
        this.uploader = uploader; // Can be null for forms without uploads
        this.submitBtn = form.querySelector(submitBtnSelector);

        this.statusHandler = uiManager.createTaskStatusHandler(statusSelector, resultSelector);
        this.statusHandler.statusSelector = statusSelector;
        this.statusHandler.resultSelector = resultSelector;
        this.initialStatusHTML = document.querySelector(statusSelector)?.innerHTML || '';

        if (!this.submitBtn) {
            throw new Error(`Submit button with selector "${submitBtnSelector}" not found.`);
        }

        // 设置全局实例以便测试模式按钮可以调用
        window.taskProcessorInstance = this;

        this._bind();
    }

    _bind() {
        this.form.addEventListener('submit', (e) => this.submit(e));
    }

    reset() {
        this.form.reset();
        if (this.uploader) {
            this.uploader.reset();
        }
        this.statusHandler.reset(this.initialStatusHTML);
        apiClient.cancelPolling();
    }

    async submit(e) {
        e.preventDefault();

        // --- 0. Check Credits First ---
        const creditsCheck = await this.checkUserCredits();
        if (!creditsCheck.success) {
            return; // 积分检查失败，已显示相应提示
        }

        // --- 1. Check Test Mode ---
        const testModeEl = this.form.querySelector('#testMode');
        if (testModeEl && testModeEl.checked) {
            this.handleTestMode();
            return;
        }

        // --- 1. Validation ---
        // Validate file uploader only if it's provided
        if (this.uploader) {
            const imageFile = this.uploader.getFile();
            if (!imageFile) {
                this.statusHandler.showError('请先上传一张有效的图片。');
                return;
            }
        }

        const promptEl = this.form.querySelector('textarea[name="prompt"]');
        let currentPrompt = promptEl ? promptEl.value.trim() : '';
        if (promptEl && !currentPrompt) {
            this.statusHandler.showError('请输入必要的指令或描述。');
            return;
        }

        const translatedPromptArea = this.form.querySelector('#translatedPromptArea');
        const translatedPromptEl = this.form.querySelector('#translatedPrompt');
        if (translatedPromptArea?.style.display !== 'none' && translatedPromptEl?.value.trim()) {
            currentPrompt = translatedPromptEl.value.trim();
        }

        uiManager.showLoadingState(this.submitBtn, "处理中...");

        try {
            // --- 2. Data Preparation ---
            const formData = new FormData(this.form);
            if (promptEl) {
                formData.set('prompt', currentPrompt); 
            }
            
            // CRITICAL FIX: Ensure clean file data.
            // First, delete any 'image' field that might have been auto-added by new FormData().
            // Then, append the one we trust from the uploader's state.
            if (this.uploader && this.uploader.getFile()) {
                formData.delete(this.uploader.inputName); 
                this.uploader.appendToFormData(formData);
            }

            this.statusHandler.showProcessing('正在提交任务...');

            // --- 3. API Interaction ---
            const endpoint = this.form.getAttribute('action');
            if (!endpoint) {
                throw new Error('Form is missing an "action" attribute specifying the API endpoint.');
            }
            const taskUrl = await apiClient.submitTask(endpoint, formData);

            const finalTask = await apiClient.pollTaskStatus(taskUrl, (progress) => {
                this.statusHandler.showProcessing(progress.message || '任务处理中...');
            });

            // --- 4. Display Result ---
            // 修复：优先使用后端提供的view_url，然后才是output_file
            console.log('[DEBUG] TaskProcessor 收到最终任务结果:', finalTask);
            
            let imageUrl = null;
            if (finalTask.view_url) {
                imageUrl = finalTask.view_url;
                console.log('[DEBUG] 使用 view_url:', imageUrl);
            } else if (finalTask.download_url) {
                imageUrl = finalTask.download_url;
                console.log('[DEBUG] 使用 download_url:', imageUrl);
            } else if (finalTask.output_file) {
                // Fallback: 转换文件路径为URL
                const filename = finalTask.output_file.split(/[\\/]/).pop();
                imageUrl = `/outputs/${filename}`;
                console.log('[DEBUG] 转换 output_file 为 URL:', finalTask.output_file, '->', imageUrl);
            }
            
            console.log('[DEBUG] 最终图像URL:', imageUrl);
            
            if (imageUrl) {
                // For edit/style pages, show a comparison. For generate, show a single image.
                const originalImageUrl = this.uploader ? uiManager.getPreviewImageSrc(`#${this.uploader.container.id}`) : null;
                console.log('[DEBUG] 原始图像URL:', originalImageUrl);
                this.statusHandler.showSuccess('任务成功！', imageUrl, originalImageUrl);
            } else {
                throw new Error('任务完成，但未返回有效的图像结果。');
            }
        } catch (error) {
            this.statusHandler.showError(error.message || '发生未知错误，请稍后重试。');
        } finally {
            uiManager.hideLoadingState(this.submitBtn);
        }
    }

    handleTestMode() {
        // 收集表单数据用于测试模式显示
        const formData = new FormData(this.form);
        const testData = {};

        // 收集所有表单字段
        for (let [key, value] of formData.entries()) {
            if (key === 'image' && this.uploader) {
                // 对于图像文件，显示文件信息而不是实际数据
                const file = this.uploader.getFile();
                testData[key] = file ? {
                    name: file.name,
                    size: this.formatFileSize(file.size),
                    type: file.type
                } : null;
            } else {
                testData[key] = value;
            }
        }

        // 处理提示词
        const promptEl = this.form.querySelector('textarea[name="prompt"]');
        const translatedPromptArea = this.form.querySelector('#translatedPromptArea');
        const translatedPromptEl = this.form.querySelector('#translatedPrompt');

        if (translatedPromptArea?.style.display !== 'none' && translatedPromptEl?.value.trim()) {
            testData.prompt = translatedPromptEl.value.trim();
            testData.original_prompt = promptEl?.value.trim();
        }

        this.showTestModeResult(testData);
    }

    showTestModeResult(testData) {
        const endpoint = this.form.getAttribute('action');
        const method = this.form.getAttribute('method') || 'POST';

        // 使用statusHandler的内部方法直接设置HTML
        const statusArea = document.querySelector(this.statusHandler.statusSelector || '#statusArea');
        const resultArea = document.querySelector(this.statusHandler.resultSelector || '#statusArea');

        if (statusArea) {
            statusArea.innerHTML = `
                <div class="alert alert-info">
                    <h5><i class="fas fa-bug"></i> 测试模式 - API请求预览</h5>
                    <p class="mb-0">以下是将要发送到服务器的数据，实际API调用已被拦截。</p>
                </div>
            `;
        }

        if (resultArea) {
            resultArea.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">请求信息</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>端点:</strong> <code>${this.escapeHtml(endpoint)}</code></p>
                        <p><strong>方法:</strong> <code>${method}</code></p>
                        <p><strong>内容类型:</strong> <code>multipart/form-data</code></p>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">请求参数</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>${this.escapeHtml(JSON.stringify(testData, null, 2))}</code></pre>
                    </div>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <button class="btn btn-primary" onclick="window.taskProcessorInstance.exitTestMode()">
                        <i class="fas fa-play"></i> 退出测试模式并实际提交
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.taskProcessorInstance.reset()">
                        <i class="fas fa-undo"></i> 重置表单
                    </button>
                </div>
            `;
        }
    }

    exitTestMode() {
        const testModeEl = this.form.querySelector('#testMode');
        if (testModeEl) {
            testModeEl.checked = false;
        }
        // 重新提交表单
        this.submit(new Event('submit'));
    }

    reset() {
        // 重置表单
        this.form.reset();

        // 重置状态显示
        this.statusHandler.reset(this.initialStatusHTML);

        // 如果有文件上传器，也重置它
        if (this.uploader && typeof this.uploader.reset === 'function') {
            this.uploader.reset();
        }

        // 重置测试模式
        const testModeEl = this.form.querySelector('#testMode');
        if (testModeEl) {
            testModeEl.checked = false;
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 检查用户积分是否足够
     */
    async checkUserCredits() {
        try {
            // 获取JWT token
            const token = localStorage.getItem('access_token') || sessionStorage.getItem('access_token');
            if (!token) {
                this.showCreditError('请先登录', '您需要登录后才能使用图像生成功能');
                return { success: false };
            }

            // 计算所需积分
            const formData = new FormData(this.form);
            const model = formData.get('model') || 'flux-pro';
            const creditsNeeded = this.calculateCreditsNeeded(model, formData);

            // 检查用户积分
            const response = await fetch('/api/user/credits/check-limits', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + token,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    credits_needed: creditsNeeded
                })
            });

            const result = await response.json();

            if (!response.ok) {
                this.showCreditError('检查积分失败', result.message || '网络错误');
                return { success: false };
            }

            if (!result.success) {
                if (result.limit_type === 'credits') {
                    this.showInsufficientCredits(result.available_credits, result.needed_credits);
                } else if (result.limit_type === 'daily') {
                    this.showDailyLimitReached(result.daily_limit, result.daily_used);
                } else {
                    this.showCreditError('积分检查失败', result.message);
                }
                return { success: false };
            }

            return { success: true };

        } catch (error) {
            console.error('积分检查失败:', error);
            this.showCreditError('积分检查失败', '网络连接错误，请稍后重试');
            return { success: false };
        }
    }

    /**
     * 计算所需积分
     */
    calculateCreditsNeeded(model, formData) {
        // 基础积分消费
        const baseCosts = {
            'generate': 1,
            'edit': 2,
            'style': 3
        };

        // 模型倍数
        const modelMultipliers = {
            'flux-pro': 1.0,
            'flux-kontext-pro': 1.5,
            'flux-kontext-max': 2.0
        };

        // 确定任务类型
        const endpoint = this.form.getAttribute('action');
        let taskType = 'generate';
        if (endpoint.includes('/edit')) taskType = 'edit';
        else if (endpoint.includes('/style')) taskType = 'style';

        const baseCost = baseCosts[taskType] || 1;
        const multiplier = modelMultipliers[model] || 1.0;
        let cost = Math.ceil(baseCost * multiplier);

        // 高分辨率额外消费
        if (formData.get('high_resolution') === 'true') {
            cost += 1;
        }

        // 商业授权倍数
        if (formData.get('commercial_license') === 'true') {
            cost *= 3;
        }

        return Math.max(cost, 1);
    }

    /**
     * 显示积分不足提示
     */
    showInsufficientCredits(availableCredits, neededCredits) {
        const shortfall = neededCredits - availableCredits;

        if (availableCredits <= 0) {
            // 积分为0或负数，跳转到积分管理页面
            this.showCreditDialog(
                '积分不足',
                `您当前的积分为 ${availableCredits}，无法执行此操作。请前往积分管理页面充值。`,
                '前往充值',
                '/user/credits'
            );
        } else {
            // 积分不足但不为0
            this.showCreditDialog(
                '积分不足',
                `此操作需要 ${neededCredits} 积分，您当前有 ${availableCredits} 积分，还需要 ${shortfall} 积分。`,
                '前往充值',
                '/user/credits'
            );
        }
    }

    /**
     * 显示每日限制达到提示
     */
    showDailyLimitReached(dailyLimit, dailyUsed) {
        this.showCreditDialog(
            '每日使用次数已达上限',
            `您今日已使用 ${dailyUsed}/${dailyLimit} 次，已达到每日限制。请明天再试或升级账户。`,
            '了解升级',
            '/user/profile'
        );
    }

    /**
     * 显示积分错误提示
     */
    showCreditError(title, message) {
        this.statusHandler.showError(`${title}: ${message}`);
    }

    /**
     * 显示积分对话框
     */
    showCreditDialog(title, message, buttonText, redirectUrl) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="creditModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="window.location.href='${redirectUrl}'">${buttonText}</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('creditModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('creditModal'));
        modal.show();

        // 模态框关闭后移除DOM
        document.getElementById('creditModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }
}

export default TaskProcessor;