<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{% block title %}BFL AI 图像生成器{% endblock %}</title>
        <!-- 缓存清除标识 -->
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
        <meta http-equiv="Pragma" content="no-cache">
        <meta http-equiv="Expires" content="0">
        <!-- 内容安全策略 - 允许data URI、blob URI和必要的资源 -->
        <meta http-equiv="Content-Security-Policy" content="default-src 'self' https: data: blob:; script-src 'self' 'unsafe-inline' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' https: data: blob:;">
        <meta name="referrer" content="no-referrer">
        
        <!-- Bootstrap CSS - 本地文件 -->
        <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet" data-no-maps>
        <!-- Font Awesome - 本地文件 -->
        <link href="{{ url_for('static', filename='css/fontawesome.min.css') }}" rel="stylesheet">
        <!-- 主样式表 -->
        <link href="{{ url_for('static', filename='css/app.css') }}" rel="stylesheet">
        <!-- 基础样式 -->
        <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
        <link rel="stylesheet" href="{{ url_for('static', filename='css/fontawesome.min.css') }}">
        <!-- 主题和组件样式 -->
        <link rel="stylesheet" href="{{ url_for('static', filename='css/themes/variables.css') }}">
        <link rel="stylesheet" href="{{ url_for('static', filename='css/components/unified-components.css') }}">
    
    {% block extra_css %}{% endblock %}
    {% block extra_head %}{% endblock %}
    </head>
    <body class="{% block body_class %}{% endblock %}">
    <!-- 开发者工具检测和性能优化 -->
    <script>
        // 检测开发者工具并优化性能
        (function() {
            let devtools = {
                open: false,
                orientation: null
            };
            
            const threshold = 160;
            
            // 检测开发者工具是否打开
            function detectDevTools() {
                if (window.outerHeight - window.innerHeight > threshold || 
                    window.outerWidth - window.innerWidth > threshold) {
                    if (!devtools.open) {
                        devtools.open = true;
                        // 开发者工具打开时，减少动画和效果以提高性能
                        document.documentElement.style.setProperty('--animation-duration', '0s');
                        console.warn('检测到开发者工具，已优化性能设置');
                    }
                } else {
                    if (devtools.open) {
                        devtools.open = false;
                        // 恢复正常效果
                        document.documentElement.style.removeProperty('--animation-duration');
                    }
                }
            }
            
            // 定期检测（降低频率以减少性能影响）
            setInterval(detectDevTools, 2000);
            
            // 防止某些调试器断点
            if (typeof console !== 'undefined') {
                ['log', 'warn', 'error'].forEach(method => {
                    const original = console[method];
                    console[method] = function(...args) {
                        // 避免在某些特定条件下触发调试器
                        if (args.length > 0 && typeof args[0] === 'string' && 
                            args[0].includes('querySelector')) {
                            return;
                        }
                        return original.apply(console, args);
                    };
                });
            }
        })();
    </script>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('pages.index') }}">
                <i class="fas fa-magic"></i> BFL AI 图像生成器
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.generate_page') }}">
                            <i class="fas fa-paint-brush"></i> 图像生成
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.edit_page') }}">
                            <i class="fas fa-edit"></i> 图像编辑
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.style_page') }}">
                            <i class="fas fa-palette"></i> 风格迁移
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.translate_page') }}">
                            <i class="fas fa-language"></i> 提示词翻译
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.restore_page') }}">
                            <i class="fas fa-history"></i> 旧照片修复
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.compare_page') }}">
                            <i class="fas fa-exchange-alt"></i> 对比示例
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pages.gallery') }}">
                            <i class="fas fa-images"></i> 图像画廊
                        </a>
                    </li>
                </ul>

                <!-- 用户菜单 -->
                <ul class="navbar-nav">
                    <li class="nav-item" id="guestMenu">
                        <div class="d-flex">
                            <a class="nav-link" href="{{ url_for('pages.login_page') }}">
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </a>
                            <a class="nav-link" href="{{ url_for('pages.register_page') }}">
                                <i class="fas fa-user-plus"></i> 注册
                            </a>
                        </div>
                    </li>
                    <li class="nav-item dropdown" id="userMenu" style="display: none;">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle"></i> <span id="usernameDisplay">用户</span>
                            <span class="badge bg-warning ms-1" id="creditsDisplay">0</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('pages.dashboard_page') }}">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('pages.profile_page') }}">
                                <i class="fas fa-user-edit"></i> 个人中心
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('pages.credits_store_page') }}">
                                <i class="fas fa-coins"></i> 积分管理
                            </a></li>
                            <li id="adminMenuItem" style="display: none;"><a class="dropdown-item" href="{{ url_for('pages.admin_dashboard_page') }}">
                                <i class="fas fa-cog"></i> 管理面板
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <div class="container main-content" style="margin-top: 100px;">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </div>
    
    <!-- 页脚 -->
    <div class="footer">
        <div class="container">
            <p>&copy; 2024 BFL AI 图像生成器 | 基于 Black Forest Labs API</p>
        </div>
    </div>
    
    <!-- jQuery - 本地文件 -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <!-- Bootstrap JS - 本地文件 -->
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}" data-no-maps></script>

    <!-- 简化认证管理器 -->
    <script src="{{ url_for('static', filename='js/simple-auth-manager.js') }}"></script>

    <!-- 用户认证JavaScript -->
    <script>
    $(document).ready(function() {
        // 监听认证状态变更
        window.simpleAuth.addListener(function(eventData) {
            if (eventData.newState === window.simpleAuth.AuthState.LOGGED_IN && eventData.user) {
                showUserMenu(eventData.user);
            } else if (eventData.newState === window.simpleAuth.AuthState.LOGGED_OUT) {
                showGuestMenu();
            } else if (eventData.newState === window.simpleAuth.AuthState.ERROR) {
                console.warn('认证错误:', eventData.errorMessage);
                showGuestMenu();
            }
        });
        
        // 页面加载时检查认证状态
        window.simpleAuth.checkInitialAuthState();
    });

    // 显示认证相关消息
    function showAuthMessage(message, type = 'info') {
        const alertClass = type === 'warning' ? 'alert-warning' : 'alert-info';
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;" role="alert">
                <i class="fas fa-info-circle"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('body').append(alertHtml);

        // 5秒后自动消失
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    function showUserMenu(user) {
        $('#guestMenu').hide();
        $('#userMenu').show();
        $('#usernameDisplay').text(user.username || user.phone);
        $('#creditsDisplay').text(user.available_credits || (user.total_credits - user.used_credits));

        // 如果是管理员，显示管理员菜单项
        if (user.user_type === 'enterprise' || user.user_type === 'admin') {
            $('#adminMenuItem').show();
        } else {
            $('#adminMenuItem').hide();
        }
    }

    function showGuestMenu() {
        $('#userMenu').hide();
        $('#guestMenu').show();
    }

    function logout() {
        if (confirm('确定要退出登录吗？')) {
            window.simpleAuth.logout().then((result) => {
                if (result.success) {
                    // 跳转到首页
                    window.location.href = '/';
                } else {
                    console.error('登出失败:', result.message);
                    // 即使登出失败也清除本地状态
                    showGuestMenu();
                    window.location.href = '/';
                }
            }).catch((error) => {
                console.error('登出异常:', error);
                // 即使出现异常也清除本地状态
                showGuestMenu();
                window.location.href = '/';
            });
        }
    }

    // 全局AJAX错误处理
    $(document).ajaxError(function(event, xhr, settings) {
        if (xhr.status === 401) {
            // 认证失败，清除状态并跳转到登录页
            window.simpleAuth.setState(window.simpleAuth.AuthState.LOGGED_OUT);
            showGuestMenu();
            if (!window.location.pathname.includes('login')) {
                window.location.href = '/simple-login';
            }
        }
    });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>