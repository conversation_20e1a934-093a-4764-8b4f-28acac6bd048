#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译功能测试模块 - 简化版
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import requests
import time
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

try:
    from Translation.ollama_service import OllamaService
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)

class TestTranslation(unittest.TestCase):
    """测试翻译功能"""
    
    def setUp(self):
        """测试前准备"""
        from backend.routes.translation_routes import translation_bp
        self.translation_bp = translation_bp
        self.test_text = "一只可爱的小猫咪坐在窗台上"
    
    def test_ollama_translation(self):
        """测试Ollama翻译"""
        with patch('requests.post') as mock_post:
            # 模拟成功响应
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                'response': 'A cute kitten sits on the windowsill'
            }
            
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': 'qwen3:4b',
                    'prompt': f"将下面的文本翻译成中文：\n{self.test_text}"
                }
            )
            
            self.assertEqual(response.status_code, 200)
            result = response.json()
            self.assertIn('response', result)
    
    def test_deepseek_translation(self):
        """测试DeepSeek翻译"""
        with patch('requests.post') as mock_post:
            # 模拟成功响应
            mock_post.return_value.status_code = 200
            mock_post.return_value.json.return_value = {
                'choices': [{
                    'message': {
                        'content': 'A cute kitten sits on the windowsill'
                    }
                }]
            }
            
            response = requests.post(
                'https://api.deepseek.com/chat/completions',
                json={
                    'model': 'deepseek-chat',
                    'messages': [{
                        'role': 'user',
                        'content': f"请将以下文本翻译成中文，保持原文的语气和风格：\n{self.test_text}"
                    }]
                },
                headers={'Authorization': f'Bearer {os.getenv("DEEPSEEK_API_KEY")}'}
            )
            
            self.assertEqual(response.status_code, 200)
            result = response.json()
            self.assertIn('choices', result)
            self.assertIn('message', result['choices'][0])
            self.assertIn('content', result['choices'][0]['message'])

def format_time_diff(start_time, end_time):
    """格式化时间差"""
    diff = end_time - start_time
    return f"{diff:.3f}秒"

def test_ollama_performance():
    """测试Ollama服务的性能"""
    print("\n=== Ollama服务性能测试 ===")
    
    # 初始化服务
    print("\n1. 初始化Ollama服务...")
    start_time = time.time()
    ollama = OllamaService()
    init_end_time = time.time()
    print(f"初始化耗时: {format_time_diff(start_time, init_end_time)}")
    
    # 测试文本润色
    print("\n2. 测试文本润色...")
    polish_text = "古道西风瘦马"
    polish_start = time.time()
    polish_result = ollama.polish(polish_text)
    polish_end = time.time()
    print(f"润色结果: {polish_result.get('text', '润色失败')}")
    print(f"润色耗时: {format_time_diff(polish_start, polish_end)}")
    
    # 测试文本翻译
    print("\n3. 测试文本翻译...")
    translate_start = time.time()
    translate_result = ollama.translate(polish_result.get('text', polish_text))
    translate_end = time.time()
    print(f"翻译结果: {translate_result.get('translated', '翻译失败')}")
    print(f"翻译耗时: {format_time_diff(translate_start, translate_end)}")
    
    # 总结
    print("\n=== 性能测试总结 ===")
    print(f"总耗时: {format_time_diff(start_time, translate_end)}")
    print(f"初始化: {format_time_diff(start_time, init_end_time)}")
    print(f"润色阶段: {format_time_diff(polish_start, polish_end)}")
    print(f"翻译阶段: {format_time_diff(translate_start, translate_end)}")

if __name__ == "__main__":
    test_ollama_performance() 