{% extends "base.html" %}

{% block title %}图像对比工具 - BFL AI 图像生成器{% endblock %}

{% block body_class %}theme-restore{% endblock %}

{% block extra_head %}
<link href="{{ url_for('static', filename='css/pages/compare.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container">
    <div class="card">
        <div class="card-header">
            <h2><i class="fas fa-exchange-alt me-2"></i>图像对比工具</h2>
            <p class="mb-0">智能预处理对比或手动上传两张图片进行左右对比</p>
        </div>
        <div class="card-body">
        <!-- 功能选择标签 -->
        <div class="function-tabs">
            <button class="tab-btn active" data-tab="preprocess">
                <i class="fas fa-magic"></i> 智能预处理对比
            </button>
            <button class="tab-btn" data-tab="manual">
                <i class="fas fa-images"></i> 手动对比两图
            </button>
        </div>

        <!-- 智能预处理对比 -->
            <div class="tab-content active" id="preprocess">
                <div class="upload-area">
                    <input type="file" id="preprocess-file" accept="image/*" class="d-none">
                    <button class="upload-btn" onclick="document.getElementById('preprocess-file').click()">
                        <i class="fas fa-upload"></i> 上传图片
                        </button>
                    </div>
                <div id="preprocess-compare" style="display: none;">
                    {% with 
                        before_image='',
                        after_image='',
                        before_label='原图',
                        after_label='处理后'
                    %}
                    {% include 'components/compare_widget.html' %}
                    {% endwith %}
            </div>
        </div>

        <!-- 手动对比两图 -->
            <div class="tab-content" id="manual">
                <div class="row">
                    <div class="col-md-6">
                        <div class="upload-area">
                            <input type="file" id="manual-file-1" accept="image/*" class="d-none">
                            <button class="upload-btn" onclick="document.getElementById('manual-file-1').click()">
                                <i class="fas fa-upload"></i> 上传第一张图片
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="upload-area">
                            <input type="file" id="manual-file-2" accept="image/*" class="d-none">
                            <button class="upload-btn" onclick="document.getElementById('manual-file-2').click()">
                                <i class="fas fa-upload"></i> 上传第二张图片
                            </button>
                        </div>
                    </div>
                </div>
                <div id="manual-compare" style="display: none;">
                    {% with 
                        before_image='',
                        after_image='',
                        before_label='图片1',
                        after_label='图片2'
                    %}
                    {% include 'components/compare_widget.html' %}
                    {% endwith %}
            </div>
        </div>

        <!-- 状态显示 -->
        <div id="statusArea" class="status-area" style="display: none;">
            <div class="status-message">
                <i class="fas fa-spinner fa-spin"></i>
                <span id="statusText">正在处理中...</span>
            </div>
        </div>

        <!-- 处理信息 -->
        <div id="infoArea" class="info-area" style="display: none;">
            <div class="info-content">
                <div class="info-item">
                    <strong>左侧图像:</strong> <span id="leftImageInfo">-</span>
                    <strong>右侧图像:</strong> <span id="rightImageInfo">-</span>
                </div>
                <div class="info-item" id="preprocessInfo" style="display: none;">
                    <strong>目标宽高比:</strong> <span id="aspectRatio">-</span>
                    <strong>处理操作:</strong> <span id="cropInfo">-</span>
                </div>
                </div>
        </div>

        <!-- 操作按钮 -->
        <div class="compare-actions" id="actionArea" style="display: none;">
            <button class="action-btn secondary" id="newCompare">
                <i class="fas fa-plus"></i> 新对比
            </button>
                <a href="{{ url_for('pages.restore_page') }}" class="action-btn secondary">
                <i class="fas fa-undo"></i> 旧照片修复
            </a>
                <a href="{{ url_for('pages.gallery') }}" class="action-btn secondary">
                <i class="fas fa-images"></i> 图像画廊
            </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/pages/compare.js') }}"></script>
{% endblock %} 