# 翻译服务配置实现

## 核心配置

1. **DeepSeek配置**
   - API密钥: `DEEPSEEK_API_KEY`
   - API地址: `DEEPSEEK_API_URL`
   - 模型名称: `DEEPSEEK_MODEL_NAME`
   - 超时设置: `DEEPSEEK_TIMEOUT`

2. **Ollama配置**
   - 服务地址: `OLLAMA_URL` (默认: http://localhost:11434)
   - 模型名称: `OLLAMA_MODEL` (默认: qwen3:4b)
   - 超时设置: `OLLAMA_TIMEOUT`

3. **服务选择**
   - 默认服务: `DEFAULT_SERVICE` (ollama或deepseek)

## 配置管理

1. **配置优先级**
   - 环境变量优先
   - 默认配置备选
   - 运行时可更新

2. **配置方法**
   - `get_deepseek_config()` - 获取DeepSeek配置
   - `get_ollama_config()` - 获取Ollama配置
   - `get_service_config()` - 获取完整配置
   - `print_config()` - 打印配置信息

## 特殊功能

1. **照片修复提示词**
   - deep_restore: 深度修复
   - colorize_restore: 彩色化修复
   - default: 基础修复

2. **配置验证**
   - API密钥检查
   - 服务可用性验证
   - 配置完整性检查

3. **安全特性**
   - 敏感信息保护
   - 配置打印脱敏
   - 默认值安全性