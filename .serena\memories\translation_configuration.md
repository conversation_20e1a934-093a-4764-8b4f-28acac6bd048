# 翻译服务配置说明

## 环境变量配置
1. DeepSeek配置
   - `DEEPSEEK_API_KEY` - API密钥
   - `DEEPSEEK_API_URL` - API地址
   - `DEEPSEEK_MODEL_NAME` - 模型名称
   - `DEEPSEEK_TIMEOUT` - 超时时间(秒)

2. Ollama配置
   - `OLLAMA_URL` - 服务地址(默认: http://localhost:11434)
   - `OLLAMA_MODEL` - 模型名称(默认: qwen3:4b)
   - `OLLAMA_TIMEOUT` - 超时时间(秒)

3. 服务选择
   - `DEFAULT_SERVICE` - 默认服务('ollama'或'deepseek')

## 配置优先级
1. 环境变量
2. 配置文件
3. 默认值

## 重要说明
- Ollama服务需要本地安装并运行Ollama服务器
- DeepSeek服务需要有效的API密钥
- 建议同时配置两种服务以实现故障转移