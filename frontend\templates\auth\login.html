{% extends "base.html" %}

{% block title %}用户登录 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header text-center bg-primary text-white">
                    <h4><i class="fas fa-sign-in-alt"></i> 用户登录</h4>
                </div>
                <div class="card-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="identifier" class="form-label">登录账号</label>
                            <input type="text" class="form-control" id="identifier" name="identifier" required
                                   placeholder="手机号/邮箱/用户名">
                            <div class="form-text">支持手机号、邮箱或用户名登录</div>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember">
                            <label class="form-check-label" for="remember">记住我</label>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p class="mb-0">还没有账户？ <a href="{{ url_for('pages.register_page') }}">立即注册</a></p>
                        <p class="mb-0"><a href="#" id="forgotPassword">忘记密码？</a></p>
                    </div>
                    
                    <!-- 游客模式提示 -->
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle"></i> 
                        您也可以<a href="{{ url_for('pages.index') }}" class="alert-link">以游客身份</a>使用基础功能
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 登录成功模态框 -->
<div class="modal fade" id="loginSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> 登录成功
                </h5>
            </div>
            <div class="modal-body">
                <p>欢迎回来！正在跳转到您的仪表板...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#loginForm').on('submit', function(e) {
        e.preventDefault();
        
        const identifier = $('#identifier').val();
        const password = $('#password').val();
        const remember = $('#remember').is(':checked');

        // 基本验证
        if (!identifier || !password) {
            showAlert('danger', '请填写完整的登录信息');
            return;
        }

        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 登录中...').prop('disabled', true);

        // 使用新的简化认证系统
        window.simpleAuth.login(identifier, password, remember)
            .then(function(result) {
                if (result.success) {
                    // 显示成功模态框
                    $('#loginSuccessModal').modal('show');

                    // 获取用户信息并决定跳转页面
                    const authState = window.simpleAuth.getState();
                    const user = authState.user;
                    let redirectUrl = '/?login_success=1';

                    if (user && user.user_type === 'enterprise') {
                        // 管理员用户跳转到管理员仪表板
                        redirectUrl = '/admin?login_success=1';
                    } else if (user && (user.user_type === 'pro' || user.user_type === 'premium')) {
                        // 高级用户跳转到用户仪表板
                        redirectUrl = '/dashboard?login_success=1';
                    }

                    // 2秒后跳转
                    setTimeout(function() {
                        window.location.href = redirectUrl;
                    }, 2000);
                } else {
                    showAlert('danger', result.message || '登录失败');
                }
            })
            .catch(function(error) {
                showAlert('danger', '网络错误，请稍后重试');
                console.error('登录错误:', error);
            })
            .finally(function() {
                // 恢复按钮状态
                submitBtn.html(originalText).prop('disabled', false);
            });
    });
    
    $('#forgotPassword').on('click', function(e) {
        e.preventDefault();
        showAlert('info', '密码重置功能即将推出，请联系管理员');
    });
});

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.card-body').prepend(alertHtml);
    
    // 5秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
