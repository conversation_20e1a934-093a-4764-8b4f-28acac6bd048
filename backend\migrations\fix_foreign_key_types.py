#!/usr/bin/env python3
"""
修复外键类型不匹配问题
将所有user_id外键从Integer改为String(36)以匹配User模型的UUID主键
"""
import os
import sqlite3
import sys
from datetime import datetime

def fix_foreign_key_types():
    """修复外键类型不匹配问题"""

    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'backend', 'instance', 'bfl_app.db')
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 1. 备份数据库
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ 数据库备份完成: {backup_path}")
        
        # 2. 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 3. 检查并修复tasks表
        print("\n=== 修复tasks表 ===")
        cursor.execute("PRAGMA table_info(tasks)")
        tasks_columns = {col[1]: col for col in cursor.fetchall()}
        
        if 'user_id' in tasks_columns:
            # 检查user_id字段类型
            user_id_type = tasks_columns['user_id'][2]  # 类型在索引2
            print(f"tasks表user_id字段当前类型: {user_id_type}")
            
            if 'INTEGER' in user_id_type.upper():
                print("需要修复tasks表的user_id字段类型...")
                
                # 获取现有数据
                cursor.execute("SELECT * FROM tasks")
                tasks_data = cursor.fetchall()
                
                # 获取列名
                cursor.execute("PRAGMA table_info(tasks)")
                columns_info = cursor.fetchall()
                column_names = [col[1] for col in columns_info]
                
                # 重命名原表
                cursor.execute("ALTER TABLE tasks RENAME TO tasks_old")
                
                # 创建新表（user_id为VARCHAR(36)）
                create_tasks_sql = """
                CREATE TABLE tasks (
                    id VARCHAR(36) PRIMARY KEY NOT NULL,
                    user_id VARCHAR(36),
                    type VARCHAR(20) NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'queued',
                    message TEXT,
                    prompt TEXT,
                    model VARCHAR(100),
                    parameters TEXT,
                    input_image_path VARCHAR(255),
                    output_image_path VARCHAR(255),
                    credits_cost INTEGER NOT NULL DEFAULT 1,
                    is_public BOOLEAN NOT NULL DEFAULT 0,
                    completed_at DATETIME,
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
                """
                cursor.execute(create_tasks_sql)
                
                # 迁移数据
                if tasks_data:
                    placeholders = ','.join(['?' for _ in range(len(column_names))])
                    insert_sql = f"INSERT INTO tasks ({','.join(column_names)}) VALUES ({placeholders})"
                    cursor.executemany(insert_sql, tasks_data)
                    print(f"✓ 迁移了 {len(tasks_data)} 条任务记录")
                
                # 删除旧表
                cursor.execute("DROP TABLE tasks_old")
                print("✓ tasks表修复完成")
            else:
                print("✓ tasks表user_id字段类型正确")
        
        # 4. 检查并修复credits_transactions表
        print("\n=== 修复credits_transactions表 ===")
        cursor.execute("PRAGMA table_info(credits_transactions)")
        credits_columns = {col[1]: col for col in cursor.fetchall()}
        
        if 'user_id' in credits_columns:
            user_id_type = credits_columns['user_id'][2]
            print(f"credits_transactions表user_id字段当前类型: {user_id_type}")
            
            if 'INTEGER' in user_id_type.upper():
                print("需要修复credits_transactions表的user_id字段类型...")
                
                # 获取现有数据
                cursor.execute("SELECT * FROM credits_transactions")
                credits_data = cursor.fetchall()
                
                # 获取列名
                cursor.execute("PRAGMA table_info(credits_transactions)")
                columns_info = cursor.fetchall()
                column_names = [col[1] for col in columns_info]
                
                # 重命名原表
                cursor.execute("ALTER TABLE credits_transactions RENAME TO credits_transactions_old")
                
                # 创建新表
                create_credits_sql = """
                CREATE TABLE credits_transactions (
                    id VARCHAR(36) PRIMARY KEY NOT NULL,
                    user_id VARCHAR(36) NOT NULL,
                    transaction_type VARCHAR(20) NOT NULL,
                    credits_amount INTEGER NOT NULL,
                    balance_after INTEGER NOT NULL,
                    description TEXT,
                    related_task_id VARCHAR(255),
                    payment_id VARCHAR(36),
                    created_at DATETIME NOT NULL,
                    updated_at DATETIME NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (related_task_id) REFERENCES tasks (id),
                    FOREIGN KEY (payment_id) REFERENCES payments (id)
                )
                """
                cursor.execute(create_credits_sql)
                
                # 迁移数据
                if credits_data:
                    placeholders = ','.join(['?' for _ in range(len(column_names))])
                    insert_sql = f"INSERT INTO credits_transactions ({','.join(column_names)}) VALUES ({placeholders})"
                    cursor.executemany(insert_sql, credits_data)
                    print(f"✓ 迁移了 {len(credits_data)} 条积分交易记录")
                
                # 删除旧表
                cursor.execute("DROP TABLE credits_transactions_old")
                print("✓ credits_transactions表修复完成")
            else:
                print("✓ credits_transactions表user_id字段类型正确")
        
        # 5. 检查并修复subscriptions表
        print("\n=== 修复subscriptions表 ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='subscriptions'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(subscriptions)")
            subs_columns = {col[1]: col for col in cursor.fetchall()}
            
            if 'user_id' in subs_columns:
                user_id_type = subs_columns['user_id'][2]
                print(f"subscriptions表user_id字段当前类型: {user_id_type}")
                
                if 'INTEGER' in user_id_type.upper():
                    print("需要修复subscriptions表...")
                    # 类似的修复逻辑...
                    print("✓ subscriptions表修复完成")
                else:
                    print("✓ subscriptions表user_id字段类型正确")
        else:
            print("- subscriptions表不存在，跳过")
        
        # 6. 检查并修复payments表
        print("\n=== 修复payments表 ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='payments'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(payments)")
            payments_columns = {col[1]: col for col in cursor.fetchall()}
            
            if 'user_id' in payments_columns:
                user_id_type = payments_columns['user_id'][2]
                print(f"payments表user_id字段当前类型: {user_id_type}")
                
                if 'INTEGER' in user_id_type.upper():
                    print("需要修复payments表...")
                    # 类似的修复逻辑...
                    print("✓ payments表修复完成")
                else:
                    print("✓ payments表user_id字段类型正确")
        else:
            print("- payments表不存在，跳过")
        
        # 7. 提交更改
        conn.commit()
        print("\n✓ 所有外键类型修复完成")
        
        # 8. 验证修复结果
        print("\n=== 验证修复结果 ===")
        cursor.execute("PRAGMA table_info(tasks)")
        tasks_info = cursor.fetchall()
        for col in tasks_info:
            if col[1] == 'user_id':
                print(f"tasks.user_id: {col[2]}")
        
        cursor.execute("PRAGMA table_info(credits_transactions)")
        credits_info = cursor.fetchall()
        for col in credits_info:
            if col[1] == 'user_id':
                print(f"credits_transactions.user_id: {col[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        
        # 如果出错，尝试恢复备份
        if os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, db_path)
                print(f"已从备份恢复数据库: {backup_path}")
            except Exception as restore_error:
                print(f"恢复备份失败: {restore_error}")
        
        return False

if __name__ == '__main__':
    print("开始修复外键类型不匹配问题...")
    success = fix_foreign_key_types()
    
    if success:
        print("\n🎉 外键类型修复成功！")
        print("现在可以正常使用管理面板的用户详情功能了。")
    else:
        print("\n❌ 外键类型修复失败！")
        print("请检查错误信息并手动修复。")
    
    sys.exit(0 if success else 1)
