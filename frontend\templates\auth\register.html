{% extends "base.html" %}

{% block title %}用户注册 - BFL AI 图像生成器{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-header text-center bg-success text-white">
                    <h4><i class="fas fa-user-plus"></i> 用户注册</h4>
                </div>
                <div class="card-body">
                    <form id="registerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">手机号 <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="phone" name="phone" required placeholder="请输入手机号">
                                    <div class="form-text">主要身份标识，用于登录和验证</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="username" name="username" placeholder="可选，用于显示">
                                    <div class="form-text">3-20个字符，只能包含字母、数字和下划线</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="email" name="email" placeholder="可选，用于接收通知">
                            <div class="form-text">辅助身份标识，用于找回密码和接收通知</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">至少8个字符</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">确认密码</label>
                                    <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="userType" class="form-label">账户类型</label>
                            <select class="form-select" id="userType" name="userType">
                                <option value="free">免费用户 (10积分，每日5次)</option>
                            </select>
                            <div class="form-text">注册后可升级到付费账户获得更多积分和权限</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                我同意 <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">服务条款</a> 和 <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">隐私政策</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> 注册账户
                            </button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <p class="mb-0">已有账户？ <a href="{{ url_for('pages.login_page') }}">立即登录</a></p>
                    </div>
                    
                    <!-- 注册优势 -->
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-gift"></i> 注册优势</h6>
                        <ul class="mb-0">
                            <li>免费获得10积分</li>
                            <li>保存生成历史</li>
                            <li>高级参数设置</li>
                            <li>优先处理队列</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 注册成功模态框 -->
<div class="modal fade" id="registerSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle"></i> 注册成功
                </h5>
            </div>
            <div class="modal-body">
                <p>恭喜您成功注册！您已获得10个免费积分。</p>
                <p>正在跳转到登录页面...</p>
            </div>
        </div>
    </div>
</div>

<!-- 服务条款模态框 -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">服务条款</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. 服务说明</h6>
                <p>BFL AI图像生成器提供基于人工智能的图像生成、编辑和风格迁移服务。</p>
                
                <h6>2. 使用规则</h6>
                <ul>
                    <li>禁止生成违法、有害或不当内容</li>
                    <li>尊重他人版权和肖像权</li>
                    <li>不得滥用服务或进行恶意攻击</li>
                </ul>
                
                <h6>3. 积分系统</h6>
                <p>积分用于支付图像生成费用，不同操作消耗不同积分。</p>
                
                <h6>4. 免责声明</h6>
                <p>用户对生成内容负责，服务提供方不承担相关法律责任。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 隐私政策模态框 -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">隐私政策</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. 信息收集</h6>
                <p>我们收集您提供的注册信息和使用数据，用于提供更好的服务。</p>
                
                <h6>2. 信息使用</h6>
                <ul>
                    <li>提供和改进服务</li>
                    <li>用户身份验证</li>
                    <li>发送重要通知</li>
                </ul>
                
                <h6>3. 信息保护</h6>
                <p>我们采用行业标准的安全措施保护您的个人信息。</p>
                
                <h6>4. 信息共享</h6>
                <p>除法律要求外，我们不会与第三方共享您的个人信息。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 实时验证
    $('#phone').on('input', function() {
        const phone = $(this).val();
        if (phone.length >= 11) {
            checkPhoneAvailability(phone);
        }
    });

    $('#username').on('input', function() {
        const username = $(this).val();
        if (username.length >= 3) {
            checkUsernameAvailability(username);
        }
    });

    $('#email').on('input', function() {
        const email = $(this).val();
        if (email.includes('@')) {
            checkEmailAvailability(email);
        }
    });
    
    $('#confirmPassword').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
            $(this).after('<div class="invalid-feedback">密码不匹配</div>');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    $('#registerForm').on('submit', function(e) {
        e.preventDefault();

        // 调试：打印表单元素和值
        console.log('=== 注册表单调试信息 ===');
        console.log('用户名元素:', $('#username'));
        console.log('用户名元素长度:', $('#username').length);
        console.log('用户名元素HTML:', $('#username')[0]);
        console.log('用户名值:', $('#username').val());
        console.log('用户名值类型:', typeof $('#username').val());
        console.log('用户名值长度:', $('#username').val().length);

        console.log('邮箱元素:', $('#email'));
        console.log('邮箱元素长度:', $('#email').length);
        console.log('邮箱值:', $('#email').val());

        console.log('密码元素:', $('#password'));
        console.log('密码元素长度:', $('#password').length);
        console.log('密码值:', $('#password').val());

        console.log('用户类型元素:', $('#userType'));
        console.log('用户类型元素长度:', $('#userType').length);
        console.log('用户类型值:', $('#userType').val());

        // 尝试用原生JavaScript获取
        console.log('=== 原生JavaScript调试 ===');
        const usernameEl = document.getElementById('username');
        console.log('原生用户名元素:', usernameEl);
        console.log('原生用户名值:', usernameEl ? usernameEl.value : 'null');

        // 检查是否有多个相同ID的元素
        const allUsernameEls = document.querySelectorAll('#username');
        console.log('所有用户名元素:', allUsernameEls);
        console.log('用户名元素数量:', allUsernameEls.length);

        const formData = {
            phone: $('#phone').val(),
            username: $('#username').val(),
            email: $('#email').val(),
            password: $('#password').val(),
            user_type: $('#userType').val()
        };

        console.log('最终表单数据:', formData);

        // 前端验证
        if (!formData.phone || formData.phone.trim() === '') {
            showAlert('danger', '请输入手机号');
            console.error('手机号为空！');
            return;
        }

        if (!formData.password || formData.password.trim() === '') {
            showAlert('danger', '请输入密码');
            console.error('密码为空！');
            return;
        }

        // 验证密码匹配
        if (formData.password !== $('#confirmPassword').val()) {
            showAlert('danger', '密码不匹配');
            return;
        }
        
        // 显示加载状态
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> 注册中...').prop('disabled', true);
        
        $.ajax({
            url: '/api/user/register',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                if (response.success) {
                    $('#registerSuccessModal').modal('show');
                    setTimeout(function() {
                        window.location.href = '/login';
                    }, 3000);
                } else {
                    showAlert('danger', response.message || '注册失败');
                }
            },
            error: function(xhr) {
                let message = '注册失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                showAlert('danger', message);
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});

function checkUsernameAvailability(username) {
    $.ajax({
        url: '/api/user/check-username',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({username: username}),
        success: function(response) {
            const input = $('#username');
            if (response.available) {
                input.removeClass('is-invalid').addClass('is-valid');
            } else {
                input.removeClass('is-valid').addClass('is-invalid');
                input.next('.invalid-feedback').remove();
                input.after('<div class="invalid-feedback">用户名已被使用</div>');
            }
        }
    });
}

function checkPhoneAvailability(phone) {
    $.ajax({
        url: '/api/user/check-phone',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({phone: phone}),
        success: function(response) {
            const input = $('#phone');
            if (response.available) {
                input.removeClass('is-invalid').addClass('is-valid');
            } else {
                input.removeClass('is-valid').addClass('is-invalid');
                input.next('.invalid-feedback').remove();
                input.after('<div class="invalid-feedback">手机号已被注册</div>');
            }
        }
    });
}

function checkEmailAvailability(email) {
    $.ajax({
        url: '/api/user/check-email',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({email: email}),
        success: function(response) {
            const input = $('#email');
            if (response.available) {
                input.removeClass('is-invalid').addClass('is-valid');
            } else {
                input.removeClass('is-valid').addClass('is-invalid');
                input.next('.invalid-feedback').remove();
                input.after('<div class="invalid-feedback">邮箱已被注册</div>');
            }
        }
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show mt-3" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    $('.card-body').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
