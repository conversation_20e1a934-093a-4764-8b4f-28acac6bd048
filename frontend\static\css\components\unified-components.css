/* 统一组件样式 */

/* 卡片基础样式 */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(var(--primary-color), 0.1);
    box-shadow: 0 8px 32px var(--shadow-primary);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px var(--shadow-light), 0 4px 6px var(--shadow-medium);
}

/* 卡片头部 */
.card .card-header {
    background: var(--primary-gradient) !important;
    color: white !important;
    border: none !important;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

.card .card-header i {
    color: rgba(255, 255, 255, 0.9) !important;
    margin-right: 0.5rem;
}

/* 示例提示词样式 */
.example-prompt {
    padding: 14px 18px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(var(--primary-color), 0.05) 0%, rgba(var(--secondary-color), 0.03) 100%);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid var(--primary-color);
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.example-prompt:hover {
    background: linear-gradient(135deg, rgba(var(--primary-color), 0.1) 0%, rgba(var(--secondary-color), 0.05) 100%);
    transform: translateX(8px) translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-primary);
}

/* 现代化上传组件样式 */
.modern-upload-area {
    position: relative;
    border: 2px dashed #e2e8f0;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    overflow: hidden;
    min-height: 200px;
}

.modern-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-color), 0.02);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-primary);
}

.modern-upload-area.drag-over {
    border-color: var(--primary-color) !important;
    background: rgba(var(--primary-color), 0.05) !important;
    transform: scale(1.02);
}

/* 上传内容区域 */
.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    height: 100%;
    min-height: 200px;
    cursor: pointer;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.modern-upload-area:hover .upload-icon {
    transform: scale(1.1);
    color: var(--primary-dark);
}

/* 按钮样式 */
.btn-primary {
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 12px 28px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px var(--shadow-primary);
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
}

/* 表单控件样式 */
.form-control, .form-select {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    background: white;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--shadow-primary);
}

/* 状态徽章样式 */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-queued {
    background-color: var(--warning-color);
    color: white;
}

.status-processing {
    background: var(--primary-gradient);
    color: white;
}

.status-completed {
    background-color: var(--success-color);
    color: white;
}

.status-failed {
    background-color: var(--danger-color);
    color: white;
}

/* 警告样式 */
.alert-warning {
    background: linear-gradient(135deg, rgba(var(--warning-color), 0.1), rgba(var(--warning-color), 0.05)) !important;
    border-color: rgba(var(--warning-color), 0.2) !important;
    color: var(--dark-color) !important;
}

/* 高级参数区域 */
.advanced-params .card-body {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.6) 100%);
    backdrop-filter: blur(10px);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn-primary {
        padding: 10px 20px;
        font-size: 0.95rem;
    }
    
    .modern-upload-area {
        min-height: 150px;
    }
    
    .upload-content {
        padding: 1.5rem;
    }
} 