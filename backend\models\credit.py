"""
积分系统相关数据模型
"""
from .database import db, BaseModel
import enum
import uuid


class TransactionType(enum.Enum):
    """积分交易类型枚举"""
    purchase = "purchase"    # 购买积分
    usage = "usage"         # 使用积分
    refund = "refund"       # 退款
    bonus = "bonus"         # 奖励积分
    admin = "admin"         # 管理员调整


class CreditsTransaction(BaseModel):
    """积分交易记录模型"""
    __tablename__ = 'credits_transactions'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=False, index=True)
    transaction_type = db.Column(db.Enum(TransactionType), nullable=False, index=True)
    credits_amount = db.Column(db.Integer, nullable=False)  # 正数为增加，负数为消费
    balance_after = db.Column(db.Integer, nullable=False)   # 交易后余额
    description = db.Column(db.Text)
    related_task_id = db.Column(db.String(255), db.ForeignKey('tasks.id'), nullable=True)  # 关联任务
    
    # 支付相关（如果是购买积分）
    payment_id = db.Column(db.String(36), db.ForeignKey('payments.id'), nullable=True)
    
    # 关系
    user = db.relationship("User", back_populates="credit_transactions")
    task = db.relationship("Task", foreign_keys=[related_task_id])
    payment = db.relationship("Payment", back_populates="credit_transaction")
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        data['transaction_type'] = self.transaction_type.value if self.transaction_type else None
        return data
    
    @classmethod
    def get_user_transactions(cls, user_id, limit=50, transaction_type=None):
        """获取用户的积分交易记录"""
        query = cls.query.filter_by(user_id=user_id)
        
        if transaction_type:
            if isinstance(transaction_type, str):
                transaction_type = TransactionType(transaction_type)
            query = query.filter_by(transaction_type=transaction_type)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_user_balance(cls, user_id):
        """获取用户当前积分余额（从交易记录计算）"""
        latest_transaction = cls.query.filter_by(user_id=user_id)\
                                     .order_by(cls.created_at.desc())\
                                     .first()
        
        return latest_transaction.balance_after if latest_transaction else 0
    
    @classmethod
    def get_usage_stats(cls, user_id, days=30):
        """获取用户积分使用统计"""
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # 按交易类型统计
        stats = db.session.query(
            cls.transaction_type,
            func.sum(cls.credits_amount).label('total_amount'),
            func.count(cls.id).label('transaction_count')
        ).filter(
            cls.user_id == user_id,
            cls.created_at >= start_date
        ).group_by(cls.transaction_type).all()
        
        result = {}
        for stat in stats:
            result[stat.transaction_type.value] = {
                'total_amount': stat.total_amount,
                'transaction_count': stat.transaction_count
            }
        
        return result
    
    def __repr__(self):
        return f'<CreditsTransaction {self.transaction_type.value if self.transaction_type else "unknown"} {self.credits_amount}>'


class CreditPackage(BaseModel):
    """积分套餐模型"""
    __tablename__ = 'credit_packages'
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    name = db.Column(db.String(100), nullable=False)
    credits_amount = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Numeric(10, 2), nullable=False)  # 价格（美元）
    currency = db.Column(db.String(3), default='USD', nullable=False)
    discount_percentage = db.Column(db.Integer, default=0)  # 折扣百分比
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    sort_order = db.Column(db.Integer, default=0)  # 排序
    
    # 套餐描述
    description = db.Column(db.Text)
    features = db.Column(db.JSON)  # 套餐特性列表
    
    @property
    def price_per_credit(self):
        """每积分价格"""
        return float(self.price) / self.credits_amount if self.credits_amount > 0 else 0
    
    @property
    def original_price(self):
        """原价（未打折前）"""
        if self.discount_percentage > 0:
            return float(self.price) / (1 - self.discount_percentage / 100)
        return float(self.price)
    
    @property
    def savings(self):
        """节省金额"""
        return self.original_price - float(self.price)
    
    @classmethod
    def get_active_packages(cls):
        """获取活跃的积分套餐"""
        return cls.query.filter_by(is_active=True)\
                       .order_by(cls.sort_order, cls.credits_amount)\
                       .all()
    
    def to_dict(self):
        """转换为字典"""
        data = super().to_dict()
        data['price'] = float(self.price)
        data['price_per_credit'] = self.price_per_credit
        data['original_price'] = self.original_price
        data['savings'] = self.savings
        return data
    
    def __repr__(self):
        return f'<CreditPackage {self.name} {self.credits_amount} credits ${self.price}>'
