# 图像编辑和风格迁移自动翻译功能

## 功能概述

为图像编辑和风格迁移功能添加了智能语言检测和自动翻译机制，确保中文提示词能够自动翻译为英文后再提交给BFL API，提高生成质量。

## 功能特性

### 🔍 智能语言检测
- 自动检测提示词语言类型（中文/英文）
- 支持中英混合文本的准确识别
- 包含中文标点符号的完整检测

### 🌐 自动翻译机制
- **英文提示词**：直接使用，无需翻译
- **中文提示词**：自动调用翻译服务转为英文
- **翻译失败**：降级使用原始提示词，不影响主功能

### 📊 详细状态反馈
- 任务状态中显示语言检测结果
- 显示翻译处理过程和使用的服务
- 记录原始提示词和最终提示词

## 技术实现

### 函数分解设计

#### 1. 语言检测函数 (`utils/common_utils.py`)

```python
def detect_prompt_language(text: str) -> str:
    """检测提示词语言类型"""
    # 检测中文字符（包括标点符号）
    # 返回 'chinese' 或 'english'

def is_chinese_text(text: str) -> bool:
    """判断文本是否包含中文字符"""
    # 简化的布尔检测函数

def extract_english_from_mixed_text(text: str) -> str:
    """从混合文本中提取英文部分"""
    # 处理翻译结果中的中英混合情况
```

#### 2. 自动翻译处理函数 (`backend/app.py`)

```python
def auto_translate_if_needed(prompt: str) -> Dict[str, Any]:
    """自动检测语言并翻译中文提示词"""
    # 核心翻译逻辑函数

def process_prompt_for_api(prompt: str) -> Tuple[str, Dict[str, Any]]:
    """处理提示词以供BFL API使用"""
    # 主控函数，组合语言检测和翻译功能

def _generate_processing_summary(translation_info: Dict[str, Any]) -> str:
    """生成提示词处理摘要信息"""
    # 生成用户友好的处理状态描述
```

### 集成点

#### 图像编辑API (`/api/edit`)
- 接收用户原始提示词
- 自动检测语言并翻译
- 使用翻译后的提示词调用BFL API
- 在任务状态中记录处理信息

#### 风格迁移API (`/api/style`)
- 相同的自动翻译流程
- 适用于风格迁移的内容描述
- 完整的状态跟踪和反馈

## 使用示例

### 中文输入自动翻译
```
用户输入: "将猫的颜色改为橙色"
系统检测: 中文提示词
自动翻译: "Change the cat's color to orange"
API调用: 使用英文提示词
状态显示: "正在编辑图像...(检测到中文提示词，已使用ollama服务自动翻译为英文)"
```

### 英文输入直接使用
```
用户输入: "Change the cat's color to orange"
系统检测: 英文提示词
处理结果: 直接使用原提示词
API调用: 使用原始提示词
状态显示: "正在编辑图像...(检测到英文提示词，直接使用)"
```

### 翻译失败降级处理
```
用户输入: "将猫的颜色改为橙色"
系统检测: 中文提示词
翻译尝试: 失败（服务不可用）
降级处理: 使用原始中文提示词
状态显示: "正在编辑图像...(检测到中文提示词，翻译失败(服务不可用)，使用原始提示词)"
```

## 错误处理策略

### 1. 翻译服务不可用
- 记录警告日志
- 使用原始提示词继续执行
- 不中断主要功能流程

### 2. 翻译结果异常
- 检测翻译结果质量
- 提取纯英文部分
- fallback到原始结果

### 3. 空提示词处理
- 空文本默认识别为英文
- 保持原有验证逻辑

## 配置要求

### 翻译服务依赖
- **Ollama服务**：本地翻译服务（优先）
- **DeepSeek API**：云端翻译服务（备选）
- 自动选择可用服务，无需手动配置

### 支持的翻译模型
- `qwen3:4b`（默认Ollama模型）
- `deepseek-chat`（DeepSeek API模型）

## 测试验证

### 语言检测测试
```bash
python tests/test_language_detection.py
```

### 完整功能测试
1. 启动Web应用
2. 访问图像编辑页面 (`/edit`)
3. 上传图像并输入中文编辑指令
4. 观察任务状态中的翻译信息
5. 验证最终生成效果

## API响应格式

### 任务信息增强
```json
{
  "task_id": "uuid",
  "type": "edit",
  "status": "processing",
  "message": "正在编辑图像...(检测到中文提示词，已使用ollama服务自动翻译为英文)",
  "original_prompt": "将猫的颜色改为橙色",
  "final_prompt": "Change the cat's color to orange",
  "prompt_processing": {
    "original_language": "chinese",
    "translation_used": true,
    "translation_service": "ollama",
    "processing_summary": "检测到中文提示词，已使用ollama服务自动翻译为英文"
  }
}
```

## 性能影响

### 延迟分析
- **英文提示词**：无额外延迟（直接使用）
- **中文提示词**：增加翻译时间（通常1-3秒）
- **翻译失败**：快速fallback，延迟最小

### 资源使用
- 本地Ollama服务：使用本地GPU/CPU资源
- DeepSeek API：消耗API配额
- 内存影响：微小（仅增加状态信息存储）

## 后续扩展

### 支持更多语言
- 扩展语言检测算法
- 支持日语、韩语等其他语言
- 多语言到英文的翻译支持

### 翻译质量优化
- 专业术语词典
- 艺术风格描述优化
- 上下文感知翻译

### 用户控制选项
- 手动禁用自动翻译
- 翻译服务优先级设置
- 翻译结果预览和确认

## 故障排除

### 常见问题

1. **翻译服务连接失败**
   - 检查Ollama服务是否运行
   - 验证DeepSeek API密钥
   - 查看控制台日志

2. **翻译质量不佳**
   - 尝试更换翻译服务
   - 检查提示词长度和复杂度
   - 考虑手动翻译复杂描述

3. **语言检测错误**
   - 检查输入文本格式
   - 避免特殊字符干扰
   - 报告边界案例以改进算法

### 日志查看
```bash
# 查看翻译处理日志
grep "语言检测\|翻译" backend.log

# 查看任务状态
curl http://localhost:5000/api/status/{task_id}
```

---

## 总结

自动翻译功能通过函数式编程的设计理念，将复杂的语言处理逻辑分解为独立、可测试的函数单元，并通过组合实现完整的自动化流程。这确保了代码的可维护性、可扩展性，同时提供了优秀的用户体验。

# 自动翻译服务

本项目提供中英文自动翻译服务，支持DeepSeek和Ollama两种翻译引擎。

## 配置说明

配置文件位于`backend/config/translation_config.json`，支持以下配置项：

```json
{
    "deepseek": {
        "api_key": "",
        "api_url": "https://api.deepseek.com/v1",
        "model": "deepseek-chat",
        "timeout": 30
    },
    "ollama": {
        "url": "http://localhost:11434",
        "model": "qwen3:4b",
        "timeout": 30
    },
    "default_service": "ollama"
}
```

也可以通过环境变量配置：

- `DEEPSEEK_API_KEY`: DeepSeek API密钥
- `DEEPSEEK_API_URL`: DeepSeek API地址
- `DEEPSEEK_MODEL`: DeepSeek模型名称
- `DEEPSEEK_TIMEOUT`: DeepSeek请求超时时间
- `OLLAMA_URL`: Ollama服务地址
- `OLLAMA_MODEL`: Ollama模型名称
- `OLLAMA_TIMEOUT`: Ollama请求超时时间
- `DEFAULT_SERVICE`: 默认服务类型（deepseek/ollama）

## API接口

### 翻译文本

```http
POST /api/translate/text
Content-Type: application/json

{
    "text": "要翻译的中文文本"
}
```

响应格式：

```json
{
    "success": true,
    "message": "翻译成功",
    "data": {
        "original": "要翻译的中文文本",
        "translated": "Text to be translated",
        "service": "ollama",
        "model": "qwen3:4b",
        "processing_time": 1.234
    }
}
```

错误响应：

```json
{
    "success": false,
    "message": "翻译失败: 服务不可用",
    "data": {
        "error_type": "SERVICE_ERROR",
        "details": "具体错误信息"
    }
}
```

## 错误类型

- `INVALID_REQUEST`: 无效的请求
- `MISSING_DATA`: 缺少请求数据
- `INVALID_TEXT`: 无效的文本
- `TEXT_TOO_LONG`: 文本过长
- `SERVICE_ERROR`: 服务错误
- `UNKNOWN_ERROR`: 未知错误

## 服务状态

服务管理器会自动监控各个翻译服务的状态，并根据以下指标计算服务健康度：

- 初始化成功（10分）
- 状态检查成功（20分）
- API调用成功（30分）
- 响应时间达标（20分）
- 错误率达标（20分）

当服务健康度低于60分时，服务管理器会自动切换到备用服务。

## 开发说明

### 添加新的翻译服务

1. 在`Translation`目录下创建新的服务类，继承`BaseTranslationService`
2. 实现必要的方法：
   - `translate(text: str) -> Dict[str, Any]`
   - `check_status() -> bool`
   - `get_health_score() -> int`

### 运行测试

```bash
python -m pytest tests/test_services.py -v
```

### 调试日志

所有翻译操作都会记录详细的日志，包括：

- 请求ID
- 文本长度
- 使用的服务和模型
- 处理时间
- 错误信息（如果有）

## 常见问题

1. DeepSeek服务返回401错误
   - 检查API密钥是否正确
   - 检查API密钥是否过期

2. Ollama服务无法连接
   - 检查Ollama服务是否运行
   - 检查服务地址和端口是否正确
   - 检查防火墙设置

3. 翻译结果不准确
   - 尝试切换到其他服务
   - 检查文本是否符合要求
   - 尝试使用其他模型 