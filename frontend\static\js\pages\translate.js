import apiClient from '../modules/apiClient.js';
import uiManager from '../modules/uiManager.js';

document.addEventListener('DOMContentLoaded', () => {
    // #region Element Selectors
    const rawPromptTextarea = document.getElementById('rawPrompt');
    const polishBtn = document.getElementById('polishBtn');
    
    const polishedPromptTextarea = document.getElementById('polishedPrompt');
    const translateBtn = document.getElementById('translateBtn');

    const translatedPromptTextarea = document.getElementById('translatedPrompt');
    const generateBtn = document.getElementById('generateBtn');

    const step1 = document.getElementById('step1');
    const step2 = document.getElementById('step2');
    const step3 = document.getElementById('step3');
    // #endregion

    if (!polishBtn) return; // Exit if not on the translate page

    // #region Event Listeners
    polishBtn.addEventListener('click', handlePolish);
    translateBtn.addEventListener('click', handleTranslate);
    generateBtn.addEventListener('click', redirectToGenerate);

    rawPromptTextarea.addEventListener('input', () => {
        polishBtn.disabled = rawPromptTextarea.value.trim().length === 0;
    });
    // #endregion

    // #region Core Functions
    async function handlePolish() {
        const text = rawPromptTextarea.value.trim();
        if (!text) return;

        uiManager.showLoadingState(polishBtn, null, '润色中...');
        const contentArea = step1.querySelector('.step-content');
        uiManager.clearStatus(contentArea);

        try {
            const polishedText = await apiClient.polishPrompt(text);
            polishedPromptTextarea.value = polishedText;
            
            uiManager.showSuccess(contentArea, '提示词已润色！');
            step2.style.display = 'block';
            translateBtn.disabled = false;
            polishedPromptTextarea.focus();

        } catch (error) {
            uiManager.showError(contentArea, error.message);
        } finally {
            uiManager.hideLoadingState(polishBtn);
        }
    }

    async function handleTranslate() {
        const text = polishedPromptTextarea.value.trim();
        if (!text) return;

        uiManager.showLoadingState(translateBtn, null, '翻译中...');
        const contentArea = step2.querySelector('.step-content');
        uiManager.clearStatus(contentArea);

        try {
            const translatedText = await apiClient.translateText(text);
            translatedPromptTextarea.value = translatedText;
            
            uiManager.showSuccess(contentArea, '提示词已翻译！');
            step3.style.display = 'block';
            generateBtn.disabled = false;
            translatedPromptTextarea.focus();

        } catch (error) {
            uiManager.showError(contentArea, error.message);
        } finally {
            uiManager.hideLoadingState(translateBtn);
        }
    }

    function redirectToGenerate() {
        const prompt = translatedPromptTextarea.value.trim();
        if (!prompt) {
            alert('没有可用的提示词。');
            return;
        }
        const url = `/generate?prompt=${encodeURIComponent(prompt)}`;
        window.location.href = url;
    }
    // #endregion
}); 