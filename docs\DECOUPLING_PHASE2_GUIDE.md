# BFL AI 项目解耦实施指南 - 阶段2

> **文档版本**: v1.0  
> **阶段**: 模块分离和独立化  
> **预计工期**: 3-4周  
> **前置条件**: 阶段1完成并通过验证

## 🎯 阶段2目标

将项目物理分离为两个独立的应用：
1. **SaaS核心平台** - 用户、积分、支付管理系统
2. **图像处理应用** - 独立的图像生成和编辑服务

## 📋 实施步骤

### 步骤2.1: 创建SaaS核心平台 (第15-18天)

#### 2.1.1 创建SaaS平台目录结构
```bash
# 创建SaaS平台根目录
mkdir -p saas_platform
cd saas_platform

# 创建核心目录结构
mkdir -p {core,api,services,models,config,templates,static,migrations}
mkdir -p core/{interfaces,adapters,managers}
mkdir -p api/{v1,auth,admin}
mkdir -p static/{css,js,images}
mkdir -p templates/{auth,admin,user}

# 创建初始化文件
touch {__init__.py,core/__init__.py,api/__init__.py,services/__init__.py,models/__init__.py}
```

#### 2.1.2 复制和重构核心组件
```bash
# 复制核心接口和管理器
cp -r ../backend/core/* ./core/

# 复制数据模型
cp -r ../backend/models/* ./models/

# 复制用户相关服务
cp ../backend/services/credit_service.py ./services/
cp ../backend/services/task_service.py ./services/

# 复制API路由
cp ../backend/routes/user_routes.py ./api/v1/
cp ../backend/routes/admin_routes.py ./api/v1/

# 复制认证相关
cp -r ../backend/auth ./api/
```

#### 2.1.3 创建SaaS平台主应用
创建文件 `saas_platform/app.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SaaS核心平台主应用
"""
import os
import sys
from flask import Flask, jsonify, request
from flask_cors import CORS

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

from core.config_manager import ConfigManager
from core.managers.plugin_manager import PluginManager
from core.adapters.auth_adapter import AuthAdapterFactory
from core.adapters.billing_adapter import BillingAdapterFactory
from models.database import db
from api.v1.user_routes import user_bp
from api.v1.admin_routes import admin_bp
from api.gateway import api_gateway


class SaaSPlatform:
    """SaaS核心平台"""
    
    def __init__(self):
        self.app = None
        self.config_manager = ConfigManager()
        self.plugin_manager = PluginManager()
        self.auth_adapter = AuthAdapterFactory.create_adapter()
        self.billing_adapter = BillingAdapterFactory.create_adapter()
    
    def create_app(self):
        """创建Flask应用"""
        self.app = Flask(__name__)
        
        # 配置应用
        self._configure_app()
        
        # 初始化数据库
        self._initialize_database()
        
        # 注册蓝图
        self._register_blueprints()
        
        # 启动插件管理器
        self._initialize_plugins()
        
        # 注册错误处理器
        self._register_error_handlers()
        
        return self.app
    
    def _configure_app(self):
        """配置应用"""
        # 基本配置
        self.app.config['SECRET_KEY'] = self.config_manager.get('auth.jwt_secret')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = self.config_manager.get('database.core_db')
        self.app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        
        # CORS配置
        CORS(self.app, origins=['http://localhost:3000', 'http://localhost:5001'])
        
        # 会话配置
        self.app.config['PERMANENT_SESSION_LIFETIME'] = self.config_manager.get('auth.session_timeout', 3600)
    
    def _initialize_database(self):
        """初始化数据库"""
        db.init_app(self.app)
        
        with self.app.app_context():
            # 创建所有表
            db.create_all()
            
            # 创建默认管理员用户
            self._create_default_admin()
    
    def _create_default_admin(self):
        """创建默认管理员用户"""
        from models.user import User, UserType
        
        admin_phone = self.config_manager.get('admin.default_phone', '13800000000')
        admin_password = self.config_manager.get('admin.default_password', 'admin123')
        
        # 检查是否已存在管理员
        existing_admin = User.query.filter_by(phone=admin_phone).first()
        if existing_admin:
            return
        
        # 创建管理员用户
        admin_user = User(
            phone=admin_phone,
            username='admin',
            email='<EMAIL>',
            user_type=UserType.enterprise,
            is_active=True,
            is_verified=True,
            total_credits=10000
        )
        admin_user.set_password(admin_password)
        admin_user.save()
        
        print(f"默认管理员用户已创建: {admin_phone}")
    
    def _register_blueprints(self):
        """注册蓝图"""
        # 注册API蓝图
        self.app.register_blueprint(user_bp, url_prefix='/api/v1/user')
        self.app.register_blueprint(admin_bp, url_prefix='/api/v1/admin')
        self.app.register_blueprint(api_gateway, url_prefix='/api/v1/gateway')
        
        # 注册健康检查端点
        @self.app.route('/health')
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'saas-platform',
                'version': '2.0.0',
                'plugins': len(self.plugin_manager.plugins)
            })
        
        # 注册插件信息端点
        @self.app.route('/api/v1/plugins')
        def list_plugins():
            return jsonify({
                'plugins': self.plugin_manager.get_all_plugins(),
                'supported_tasks': self.plugin_manager.get_supported_task_types()
            })
    
    def _initialize_plugins(self):
        """初始化插件"""
        # 启动插件管理器
        self.plugin_manager.start()
        
        # 将插件管理器添加到应用上下文
        self.app.plugin_manager = self.plugin_manager
        self.app.auth_adapter = self.auth_adapter
        self.app.billing_adapter = self.billing_adapter
    
    def _register_error_handlers(self):
        """注册错误处理器"""
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': '资源不存在'}), 404
        
        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({'error': '服务器内部错误'}), 500
        
        @self.app.errorhandler(403)
        def forbidden(error):
            return jsonify({'error': '权限不足'}), 403
    
    def run(self, host=None, port=None, debug=None):
        """运行应用"""
        host = host or self.config_manager.get('api.host', '0.0.0.0')
        port = port or self.config_manager.get('api.port', 5000)
        debug = debug or self.config_manager.get('api.debug', False)
        
        print(f"SaaS平台启动: http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


def create_app():
    """应用工厂函数"""
    platform = SaaSPlatform()
    return platform.create_app()


if __name__ == '__main__':
    platform = SaaSPlatform()
    app = platform.create_app()
    platform.run()
```

#### 2.1.4 创建API网关
创建文件 `saas_platform/api/gateway.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API网关 - 统一处理插件任务请求
"""
from flask import Blueprint, request, jsonify, current_app, session
from functools import wraps
import uuid
from datetime import datetime

api_gateway = Blueprint('api_gateway', __name__)


def require_auth(f):
    """认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_adapter = current_app.auth_adapter
        
        if not auth_adapter.is_authenticated():
            return jsonify({'error': '需要认证'}), 401
        
        return f(*args, **kwargs)
    return decorated_function


def require_credits(task_type):
    """积分检查装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            auth_adapter = current_app.auth_adapter
            billing_adapter = current_app.billing_adapter
            
            user = auth_adapter.get_current_user()
            if not user:
                return jsonify({'error': '用户信息获取失败'}), 401
            
            # 检查积分
            if not billing_adapter.check_credits(user['id'], task_type):
                return jsonify({
                    'error': '积分不足',
                    'required_credits': billing_adapter.get_task_cost(task_type),
                    'available_credits': billing_adapter.get_user_credits(user['id'])
                }), 402
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


@api_gateway.route('/tasks', methods=['POST'])
@require_auth
def create_task():
    """创建任务"""
    try:
        task_data = request.get_json()
        if not task_data:
            return jsonify({'error': '请求数据为空'}), 400
        
        task_type = task_data.get('type')
        if not task_type:
            return jsonify({'error': '任务类型不能为空'}), 400
        
        # 获取当前用户
        auth_adapter = current_app.auth_adapter
        user = auth_adapter.get_current_user()
        
        # 检查积分
        billing_adapter = current_app.billing_adapter
        required_credits = billing_adapter.get_task_cost(task_type)
        
        if not billing_adapter.check_credits(user['id'], task_type):
            return jsonify({
                'error': '积分不足',
                'required_credits': required_credits,
                'available_credits': billing_adapter.get_user_credits(user['id'])
            }), 402
        
        # 添加用户信息到任务数据
        task_data['user_id'] = user['id']
        task_data['task_id'] = str(uuid.uuid4())
        
        # 处理任务
        plugin_manager = current_app.plugin_manager
        result = plugin_manager.process_task(task_data)
        
        if result.success:
            # 扣除积分
            billing_adapter.deduct_credits(user['id'], required_credits, result.task_id)
            
            # 记录任务到数据库
            _save_task_record(task_data, result)
            
            return jsonify({
                'success': True,
                'task_id': result.task_id,
                'data': result.data,
                'credits_deducted': required_credits,
                'remaining_credits': billing_adapter.get_user_credits(user['id'])
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error,
                'task_id': result.task_id
            }), 500
    
    except Exception as e:
        return jsonify({'error': f'任务创建失败: {str(e)}'}), 500


@api_gateway.route('/tasks/<task_id>', methods=['GET'])
@require_auth
def get_task_status(task_id):
    """获取任务状态"""
    try:
        plugin_manager = current_app.plugin_manager
        
        # 从插件获取任务状态
        for plugin_name, plugin in plugin_manager.plugins.items():
            try:
                status = plugin.get_task_status(task_id)
                if 'error' not in status:
                    return jsonify(status)
            except:
                continue
        
        # 从数据库获取任务记录
        from models.task import Task
        task = Task.query.filter_by(id=task_id).first()
        if task:
            return jsonify(task.to_dict())
        
        return jsonify({'error': '任务不存在'}), 404
    
    except Exception as e:
        return jsonify({'error': f'获取任务状态失败: {str(e)}'}), 500


@api_gateway.route('/tasks/<task_id>', methods=['DELETE'])
@require_auth
def cancel_task(task_id):
    """取消任务"""
    try:
        plugin_manager = current_app.plugin_manager
        
        # 尝试取消插件中的任务
        cancelled = False
        for plugin_name, plugin in plugin_manager.plugins.items():
            try:
                if plugin.cancel_task(task_id):
                    cancelled = True
                    break
            except:
                continue
        
        if cancelled:
            return jsonify({'success': True, 'message': '任务已取消'})
        else:
            return jsonify({'error': '任务取消失败或任务不存在'}), 404
    
    except Exception as e:
        return jsonify({'error': f'取消任务失败: {str(e)}'}), 500


@api_gateway.route('/user/credits', methods=['GET'])
@require_auth
def get_user_credits():
    """获取用户积分信息"""
    try:
        auth_adapter = current_app.auth_adapter
        billing_adapter = current_app.billing_adapter
        
        user = auth_adapter.get_current_user()
        credits = billing_adapter.get_user_credits(user['id'])
        
        return jsonify({
            'user_id': user['id'],
            'available_credits': credits,
            'user_type': user.get('user_type', 'free')
        })
    
    except Exception as e:
        return jsonify({'error': f'获取积分信息失败: {str(e)}'}), 500


@api_gateway.route('/pricing', methods=['GET'])
def get_pricing_info():
    """获取计费信息"""
    try:
        plugin_manager = current_app.plugin_manager
        pricing_info = {}
        
        for plugin_name, plugin in plugin_manager.plugins.items():
            plugin_pricing = plugin.get_pricing_info()
            pricing_info.update(plugin_pricing)
        
        return jsonify({
            'pricing': pricing_info,
            'supported_tasks': plugin_manager.get_supported_task_types()
        })
    
    except Exception as e:
        return jsonify({'error': f'获取计费信息失败: {str(e)}'}), 500


def _save_task_record(task_data, result):
    """保存任务记录到数据库"""
    try:
        from models.task import Task, TaskType, TaskStatus
        from models.database import db
        
        # 创建任务记录
        task = Task(
            id=result.task_id,
            user_id=task_data.get('user_id'),
            type=TaskType(task_data['type']),
            status=TaskStatus.completed if result.success else TaskStatus.failed,
            prompt=task_data.get('prompt'),
            model=task_data.get('model'),
            parameters=task_data,
            credits_cost=current_app.billing_adapter.get_task_cost(task_data['type'])
        )
        
        if result.success and 'image_path' in result.data:
            task.output_image_path = result.data['image_path']
        
        task.save()
        
    except Exception as e:
        print(f"保存任务记录失败: {e}")
```

#### 2.1.5 创建SaaS平台配置
创建文件 `saas_platform/config/saas_config.yaml`:

```yaml
# SaaS平台配置
mode: saas_only

# 认证配置
auth:
  type: saas
  session_timeout: 3600
  jwt_secret: ${JWT_SECRET:-saas-secret-key}
  password_salt: ${PASSWORD_SALT:-saas-salt}

# 计费配置
billing:
  type: saas
  default_credits: 10
  free_tier_limit: 5

# 插件配置
plugins:
  enabled:
    - image_processing
  auto_load: true
  plugin_dir: ../plugins
  max_concurrent_tasks: 10

# 数据库配置
database:
  core_db: ${DATABASE_URL:-sqlite:///saas_core.db}

# API配置
api:
  host: 0.0.0.0
  port: 5000
  debug: ${FLASK_DEBUG:-false}

# 管理员配置
admin:
  default_phone: "13800000000"
  default_password: "admin123"

# 文件存储配置
file_storage:
  upload_dir: uploads
  output_dir: outputs
  max_file_size: 10485760  # 10MB
  allowed_extensions:
    - jpg
    - jpeg
    - png
    - webp

# CORS配置
cors:
  origins:
    - http://localhost:3000
    - http://localhost:5001
    - http://127.0.0.1:3000
    - http://127.0.0.1:5001
```

#### 2.1.6 测试SaaS平台
```bash
# 进入SaaS平台目录
cd saas_platform

# 安装依赖
pip install flask flask-cors flask-sqlalchemy pyyaml

# 运行SaaS平台
python app.py
```

**验证SaaS平台功能**:
```bash
# 测试健康检查
curl http://localhost:5000/health

# 测试插件列表
curl http://localhost:5000/api/v1/plugins

# 测试计费信息
curl http://localhost:5000/api/v1/gateway/pricing
```

**完成标准**:
- ✅ SaaS平台可独立启动
- ✅ API网关正常工作
- ✅ 数据库初始化成功
- ✅ 插件管理器正常运行
- ✅ 健康检查端点响应正常

### 步骤2.2: 创建独立图像处理应用 (第19-22天)

#### 2.2.1 创建图像处理应用目录结构
```bash
# 创建图像处理应用根目录
mkdir -p image_processor
cd image_processor

# 创建目录结构
mkdir -p {core,api,services,config,static,uploads,outputs}
mkdir -p core/{interfaces,adapters,managers}
mkdir -p api/v1
mkdir -p static/{css,js}

# 创建初始化文件
touch {__init__.py,core/__init__.py,api/__init__.py,services/__init__.py}
```

#### 2.2.2 复制核心组件
```bash
# 复制核心接口和管理器
cp -r ../backend/core/* ./core/

# 复制图像处理服务
cp ../backend/services/image_service.py ./services/
cp -r ../Translation ./

# 复制插件
cp -r ../plugins ./
```

#### 2.2.3 创建独立图像处理应用
创建文件 `image_processor/app.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立图像处理应用
"""
import os
import sys
from flask import Flask, jsonify, request, render_template, send_from_directory
from flask_cors import CORS

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)
sys.path.insert(0, parent_dir)

from core.config_manager import ConfigManager
from core.managers.plugin_manager import PluginManager
from core.adapters.auth_adapter import AuthAdapterFactory
from core.adapters.billing_adapter import BillingAdapterFactory
from api.v1.image_routes import image_bp


class ImageProcessorApp:
    """独立图像处理应用"""

    def __init__(self):
        self.app = None
        self.config_manager = ConfigManager()
        self.plugin_manager = PluginManager()
        self.auth_adapter = AuthAdapterFactory.create_adapter()
        self.billing_adapter = BillingAdapterFactory.create_adapter()

    def create_app(self):
        """创建Flask应用"""
        self.app = Flask(__name__)

        # 配置应用
        self._configure_app()

        # 注册蓝图
        self._register_blueprints()

        # 启动插件管理器
        self._initialize_plugins()

        # 注册错误处理器
        self._register_error_handlers()

        return self.app

    def _configure_app(self):
        """配置应用"""
        # 基本配置
        self.app.config['SECRET_KEY'] = self.config_manager.get('auth.secret_key', 'image-processor-secret')
        self.app.config['UPLOAD_FOLDER'] = self.config_manager.get('file_storage.upload_dir', 'uploads')
        self.app.config['OUTPUT_FOLDER'] = self.config_manager.get('file_storage.output_dir', 'outputs')
        self.app.config['MAX_CONTENT_LENGTH'] = self.config_manager.get('file_storage.max_file_size', 16 * 1024 * 1024)

        # CORS配置
        CORS(self.app, origins=['*'])

        # 创建必要目录
        os.makedirs(self.app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(self.app.config['OUTPUT_FOLDER'], exist_ok=True)

    def _register_blueprints(self):
        """注册蓝图"""
        # 注册图像处理API
        self.app.register_blueprint(image_bp, url_prefix='/api/v1')

        # 注册主页路由
        @self.app.route('/')
        def index():
            return render_template('index.html')

        # 注册健康检查端点
        @self.app.route('/health')
        def health_check():
            return jsonify({
                'status': 'healthy',
                'service': 'image-processor',
                'version': '2.0.0',
                'plugins': len(self.plugin_manager.plugins)
            })

        # 注册文件服务端点
        @self.app.route('/outputs/<filename>')
        def serve_output(filename):
            return send_from_directory(self.app.config['OUTPUT_FOLDER'], filename)

        @self.app.route('/uploads/<filename>')
        def serve_upload(filename):
            return send_from_directory(self.app.config['UPLOAD_FOLDER'], filename)

        # 注册插件信息端点
        @self.app.route('/api/v1/plugins')
        def list_plugins():
            return jsonify({
                'plugins': self.plugin_manager.get_all_plugins(),
                'supported_tasks': self.plugin_manager.get_supported_task_types()
            })

    def _initialize_plugins(self):
        """初始化插件"""
        # 启动插件管理器
        self.plugin_manager.start()

        # 将组件添加到应用上下文
        self.app.plugin_manager = self.plugin_manager
        self.app.auth_adapter = self.auth_adapter
        self.app.billing_adapter = self.billing_adapter

    def _register_error_handlers(self):
        """注册错误处理器"""
        @self.app.errorhandler(404)
        def not_found(error):
            return jsonify({'error': '资源不存在'}), 404

        @self.app.errorhandler(500)
        def internal_error(error):
            return jsonify({'error': '服务器内部错误'}), 500

        @self.app.errorhandler(413)
        def file_too_large(error):
            return jsonify({'error': '文件过大'}), 413

    def run(self, host=None, port=None, debug=None):
        """运行应用"""
        host = host or self.config_manager.get('api.host', '0.0.0.0')
        port = port or self.config_manager.get('api.port', 5001)
        debug = debug or self.config_manager.get('api.debug', False)

        print(f"图像处理应用启动: http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug)


def create_app():
    """应用工厂函数"""
    processor = ImageProcessorApp()
    return processor.create_app()


if __name__ == '__main__':
    processor = ImageProcessorApp()
    app = processor.create_app()
    processor.run()
```

#### 2.2.4 创建图像处理API路由
创建文件 `image_processor/api/v1/image_routes.py`:

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理API路由
"""
import os
import uuid
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from datetime import datetime

image_bp = Blueprint('image_api', __name__)


def allowed_file(filename):
    """检查文件类型是否允许"""
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions


@image_bp.route('/generate', methods=['POST'])
def generate_image():
    """图像生成API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        prompt = data.get('prompt')
        if not prompt:
            return jsonify({'error': '提示词不能为空'}), 400

        # 构建任务数据
        task_data = {
            'type': 'image_generation',
            'prompt': prompt,
            'model': data.get('model', 'flux-pro'),
            'aspect_ratio': data.get('aspect_ratio', '1:1'),
            'safety_tolerance': data.get('safety_tolerance', 2),
            'output_format': data.get('output_format', 'png'),
            'task_id': str(uuid.uuid4())
        }

        # 处理任务
        plugin_manager = current_app.plugin_manager
        result = plugin_manager.process_task(task_data)

        if result.success:
            return jsonify({
                'success': True,
                'task_id': result.task_id,
                'data': result.data
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error,
                'task_id': result.task_id
            }), 500

    except Exception as e:
        return jsonify({'error': f'图像生成失败: {str(e)}'}), 500


@image_bp.route('/edit', methods=['POST'])
def edit_image():
    """图像编辑API"""
    try:
        # 检查是否有文件上传
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图像文件'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型'}), 400

        # 保存上传的文件
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        # 获取编辑参数
        prompt = request.form.get('prompt', '')
        model = request.form.get('model', 'flux-pro')
        strength = float(request.form.get('strength', 0.8))

        # 构建任务数据
        task_data = {
            'type': 'image_editing',
            'input_image': file_path,
            'prompt': prompt,
            'model': model,
            'strength': strength,
            'task_id': str(uuid.uuid4())
        }

        # 处理任务
        plugin_manager = current_app.plugin_manager
        result = plugin_manager.process_task(task_data)

        if result.success:
            return jsonify({
                'success': True,
                'task_id': result.task_id,
                'data': result.data
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error,
                'task_id': result.task_id
            }), 500

    except Exception as e:
        return jsonify({'error': f'图像编辑失败: {str(e)}'}), 500


@image_bp.route('/style-transfer', methods=['POST'])
def style_transfer():
    """风格迁移API"""
    try:
        # 检查是否有文件上传
        if 'content_image' not in request.files:
            return jsonify({'error': '没有上传内容图像'}), 400

        content_file = request.files['content_image']
        if content_file.filename == '':
            return jsonify({'error': '没有选择内容图像'}), 400

        if not allowed_file(content_file.filename):
            return jsonify({'error': '不支持的文件类型'}), 400

        # 保存内容图像
        content_filename = secure_filename(content_file.filename)
        unique_content_filename = f"content_{uuid.uuid4()}_{content_filename}"
        content_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_content_filename)
        content_file.save(content_path)

        # 处理风格图像（可选）
        style_path = None
        if 'style_image' in request.files:
            style_file = request.files['style_image']
            if style_file.filename != '' and allowed_file(style_file.filename):
                style_filename = secure_filename(style_file.filename)
                unique_style_filename = f"style_{uuid.uuid4()}_{style_filename}"
                style_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_style_filename)
                style_file.save(style_path)

        # 获取风格描述
        style_prompt = request.form.get('style_prompt', '')

        # 构建任务数据
        task_data = {
            'type': 'style_transfer',
            'content_image': content_path,
            'style_image': style_path,
            'style_prompt': style_prompt,
            'task_id': str(uuid.uuid4())
        }

        # 处理任务
        plugin_manager = current_app.plugin_manager
        result = plugin_manager.process_task(task_data)

        if result.success:
            return jsonify({
                'success': True,
                'task_id': result.task_id,
                'data': result.data
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error,
                'task_id': result.task_id
            }), 500

    except Exception as e:
        return jsonify({'error': f'风格迁移失败: {str(e)}'}), 500


@image_bp.route('/restore', methods=['POST'])
def restore_image():
    """图像修复API"""
    try:
        # 检查是否有文件上传
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图像文件'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件类型'}), 400

        # 保存上传的文件
        filename = secure_filename(file.filename)
        unique_filename = f"restore_{uuid.uuid4()}_{filename}"
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        # 获取修复参数
        restore_type = request.form.get('restore_type', 'general')

        # 构建任务数据
        task_data = {
            'type': 'image_restore',
            'input_image': file_path,
            'restore_type': restore_type,
            'task_id': str(uuid.uuid4())
        }

        # 处理任务
        plugin_manager = current_app.plugin_manager
        result = plugin_manager.process_task(task_data)

        if result.success:
            return jsonify({
                'success': True,
                'task_id': result.task_id,
                'data': result.data
            })
        else:
            return jsonify({
                'success': False,
                'error': result.error,
                'task_id': result.task_id
            }), 500

    except Exception as e:
        return jsonify({'error': f'图像修复失败: {str(e)}'}), 500


@image_bp.route('/tasks/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """获取任务状态"""
    try:
        plugin_manager = current_app.plugin_manager

        # 从插件获取任务状态
        for plugin_name, plugin in plugin_manager.plugins.items():
            try:
                status = plugin.get_task_status(task_id)
                if 'error' not in status:
                    return jsonify(status)
            except:
                continue

        return jsonify({'error': '任务不存在'}), 404

    except Exception as e:
        return jsonify({'error': f'获取任务状态失败: {str(e)}'}), 500


@image_bp.route('/tasks/<task_id>', methods=['DELETE'])
def cancel_task(task_id):
    """取消任务"""
    try:
        plugin_manager = current_app.plugin_manager

        # 尝试取消插件中的任务
        cancelled = False
        for plugin_name, plugin in plugin_manager.plugins.items():
            try:
                if plugin.cancel_task(task_id):
                    cancelled = True
                    break
            except:
                continue

        if cancelled:
            return jsonify({'success': True, 'message': '任务已取消'})
        else:
            return jsonify({'error': '任务取消失败或任务不存在'}), 404

    except Exception as e:
        return jsonify({'error': f'取消任务失败: {str(e)}'}), 500
```

#### 2.2.5 创建独立应用配置
创建文件 `image_processor/config/processor_config.yaml`:

```yaml
# 独立图像处理应用配置
mode: plugin_only

# 认证配置
auth:
  type: anonymous
  secret_key: ${SECRET_KEY:-image-processor-secret}

# 计费配置
billing:
  type: free

# 插件配置
plugins:
  enabled:
    - image_processing
  auto_load: true
  plugin_dir: ./plugins
  max_concurrent_tasks: 5

# API配置
api:
  host: 0.0.0.0
  port: 5001
  debug: ${FLASK_DEBUG:-false}

# 文件存储配置
file_storage:
  upload_dir: uploads
  output_dir: outputs
  max_file_size: 16777216  # 16MB
  allowed_extensions:
    - jpg
    - jpeg
    - png
    - gif
    - webp

# BFL API配置
bfl:
  api_key: ${BFL_API_KEY}
  base_url: "https://api.bfl.ml"
  timeout: 60
  retry_count: 3

# 翻译服务配置
translation:
  primary_service: "deepseek"
  backup_service: "ollama"
  auto_translate: true
  cache_translations: true
```

#### 2.2.6 创建简单的Web界面
创建文件 `image_processor/templates/index.html`:

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI图像处理器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result img {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
            border-radius: 5px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 AI图像处理器</h1>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('generate')">图像生成</div>
            <div class="tab" onclick="switchTab('edit')">图像编辑</div>
            <div class="tab" onclick="switchTab('style')">风格迁移</div>
            <div class="tab" onclick="switchTab('restore')">图像修复</div>
        </div>

        <!-- 图像生成 -->
        <div id="generate" class="tab-content active">
            <form id="generateForm">
                <div class="form-group">
                    <label for="prompt">提示词:</label>
                    <textarea id="prompt" name="prompt" placeholder="描述你想要生成的图像..." required></textarea>
                </div>
                <div class="form-group">
                    <label for="model">模型:</label>
                    <select id="model" name="model">
                        <option value="flux-pro">Flux Pro</option>
                        <option value="flux-dev">Flux Dev</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="aspect_ratio">宽高比:</label>
                    <select id="aspect_ratio" name="aspect_ratio">
                        <option value="1:1">1:1 (正方形)</option>
                        <option value="16:9">16:9 (宽屏)</option>
                        <option value="9:16">9:16 (竖屏)</option>
                        <option value="4:3">4:3 (标准)</option>
                    </select>
                </div>
                <button type="submit">生成图像</button>
            </form>
        </div>

        <!-- 图像编辑 -->
        <div id="edit" class="tab-content">
            <form id="editForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="editImage">选择图像:</label>
                    <input type="file" id="editImage" name="image" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="editPrompt">编辑提示词:</label>
                    <textarea id="editPrompt" name="prompt" placeholder="描述你想要的编辑效果..."></textarea>
                </div>
                <div class="form-group">
                    <label for="strength">编辑强度:</label>
                    <input type="range" id="strength" name="strength" min="0.1" max="1.0" step="0.1" value="0.8">
                    <span id="strengthValue">0.8</span>
                </div>
                <button type="submit">编辑图像</button>
            </form>
        </div>

        <!-- 风格迁移 -->
        <div id="style" class="tab-content">
            <form id="styleForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="contentImage">内容图像:</label>
                    <input type="file" id="contentImage" name="content_image" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="styleImage">风格图像 (可选):</label>
                    <input type="file" id="styleImage" name="style_image" accept="image/*">
                </div>
                <div class="form-group">
                    <label for="stylePrompt">风格描述:</label>
                    <textarea id="stylePrompt" name="style_prompt" placeholder="描述想要的艺术风格..."></textarea>
                </div>
                <button type="submit">应用风格</button>
            </form>
        </div>

        <!-- 图像修复 -->
        <div id="restore" class="tab-content">
            <form id="restoreForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="restoreImage">选择图像:</label>
                    <input type="file" id="restoreImage" name="image" accept="image/*" required>
                </div>
                <div class="form-group">
                    <label for="restoreType">修复类型:</label>
                    <select id="restoreType" name="restore_type">
                        <option value="general">通用修复</option>
                        <option value="denoise">降噪</option>
                        <option value="enhance">增强</option>
                        <option value="colorize">上色</option>
                    </select>
                </div>
                <button type="submit">修复图像</button>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>处理中，请稍候...</p>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 更新强度值显示
        document.getElementById('strength').addEventListener('input', function() {
            document.getElementById('strengthValue').textContent = this.value;
        });

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('result').style.display = 'none';
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        // 显示结果
        function showResult(success, message, imageUrl = null) {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${success ? 'success' : 'error'}`;

            let html = `<p>${message}</p>`;
            if (success && imageUrl) {
                html += `<img src="${imageUrl}" alt="生成的图像">`;
            }

            resultDiv.innerHTML = html;
            resultDiv.style.display = 'block';
        }

        // 图像生成表单提交
        document.getElementById('generateForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            showLoading();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            try {
                const response = await fetch('/api/v1/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    const imageUrl = result.data.image_url || `/outputs/${result.data.image_path.split('/').pop()}`;
                    showResult(true, '图像生成成功！', imageUrl);
                } else {
                    showResult(false, `生成失败: ${result.error}`);
                }
            } catch (error) {
                showResult(false, `请求失败: ${error.message}`);
            }
        });

        // 图像编辑表单提交
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            showLoading();

            const formData = new FormData(this);

            try {
                const response = await fetch('/api/v1/edit', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    const imageUrl = result.data.edited_image_url || `/outputs/${result.data.edited_image_path.split('/').pop()}`;
                    showResult(true, '图像编辑成功！', imageUrl);
                } else {
                    showResult(false, `编辑失败: ${result.error}`);
                }
            } catch (error) {
                showResult(false, `请求失败: ${error.message}`);
            }
        });

        // 风格迁移表单提交
        document.getElementById('styleForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            showLoading();

            const formData = new FormData(this);

            try {
                const response = await fetch('/api/v1/style-transfer', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showResult(true, '风格迁移成功！');
                } else {
                    showResult(false, `风格迁移失败: ${result.error}`);
                }
            } catch (error) {
                showResult(false, `请求失败: ${error.message}`);
            }
        });

        // 图像修复表单提交
        document.getElementById('restoreForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            showLoading();

            const formData = new FormData(this);

            try {
                const response = await fetch('/api/v1/restore', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showResult(true, '图像修复成功！');
                } else {
                    showResult(false, `修复失败: ${result.error}`);
                }
            } catch (error) {
                showResult(false, `请求失败: ${error.message}`);
            }
        });
    </script>
</body>
</html>
```

#### 2.2.7 测试独立图像处理应用
```bash
# 进入图像处理应用目录
cd image_processor

# 运行应用
python app.py
```

**验证独立应用功能**:
```bash
# 测试健康检查
curl http://localhost:5001/health

# 测试插件列表
curl http://localhost:5001/api/v1/plugins

# 测试图像生成API
curl -X POST http://localhost:5001/api/v1/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "一只可爱的猫", "model": "flux-pro"}'
```

**完成标准**:
- ✅ 独立应用可正常启动
- ✅ Web界面可正常访问
- ✅ 图像生成API正常工作
- ✅ 文件上传和处理正常
- ✅ 插件系统正常运行
