"""
任务相关数据模型
"""
from .database import db, BaseModel
import enum
import uuid


class TaskType(enum.Enum):
    """任务类型枚举"""
    generate = "generate"
    edit = "edit"
    style = "style"


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    queued = "queued"
    processing = "processing"
    completed = "completed"
    failed = "failed"


class Task(BaseModel):
    """任务模型 - 扩展现有任务系统以支持数据库持久化"""
    __tablename__ = 'tasks'
    
    # 使用UUID作为主键，与现有系统兼容
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))  # 覆盖BaseModel的id
    
    # 用户关联（可选，支持匿名用户）
    user_id = db.Column(db.String(36), db.ForeignKey('users.id'), nullable=True, index=True)
    
    # 任务基本信息
    type = db.Column(db.Enum(TaskType), nullable=False, index=True)
    status = db.Column(db.Enum(TaskStatus), default=TaskStatus.queued, nullable=False, index=True)
    message = db.Column(db.Text)
    
    # 任务参数
    prompt = db.Column(db.Text)
    model = db.Column(db.String(100))
    parameters = db.Column(db.JSON)  # 存储所有生成参数
    
    # 文件路径
    input_image_path = db.Column(db.String(255))
    output_image_path = db.Column(db.String(255))
    
    # 积分和权限
    credits_cost = db.Column(db.Integer, default=1, nullable=False)
    is_public = db.Column(db.Boolean, default=False, nullable=False)  # 是否公开分享
    
    # 时间戳
    completed_at = db.Column(db.DateTime)
    
    # 关系
    user = db.relationship("User", back_populates="tasks")
    
    def __init__(self, **kwargs):
        # 如果没有提供created_at，使用BaseModel的默认值
        super().__init__(**kwargs)
    
    @property
    def is_completed(self):
        """任务是否已完成"""
        return self.status in [TaskStatus.completed, TaskStatus.failed]
    
    @property
    def is_successful(self):
        """任务是否成功完成"""
        return self.status == TaskStatus.completed
    
    @property
    def duration(self):
        """任务执行时长（秒）"""
        if self.completed_at and self.created_at:
            return (self.completed_at - self.created_at).total_seconds()
        return None
    
    def update_status(self, status, message="", **kwargs):
        """更新任务状态"""
        if isinstance(status, str):
            status = TaskStatus(status)
        
        self.status = status
        if message:
            self.message = message
        
        # 如果任务完成，记录完成时间
        if status in [TaskStatus.completed, TaskStatus.failed]:
            from datetime import datetime
            self.completed_at = datetime.utcnow()
        
        # 更新其他字段
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        db.session.commit()
        return self
    
    def to_dict(self, include_user=False):
        """转换为字典"""
        data = super().to_dict()
        
        # 转换枚举值
        data['type'] = self.type.value if self.type else None
        data['status'] = self.status.value if self.status else None
        
        # 添加计算字段
        data['is_completed'] = self.is_completed
        data['is_successful'] = self.is_successful
        data['duration'] = self.duration
        
        # 添加文件URL（如果存在）
        if self.output_image_path:
            data['output_url'] = f'/outputs/{self.output_image_path.split("/")[-1]}'
            data['download_url'] = f'/download/{self.output_image_path.split("/")[-1]}'
        
        # 可选包含用户信息
        if include_user and self.user:
            data['user'] = {
                'id': self.user.id,
                'username': self.user.username,
                'user_type': self.user.user_type.value
            }
        
        return data
    
    @classmethod
    def get_user_tasks(cls, user_id, limit=20, status=None):
        """获取用户的任务列表"""
        query = cls.query.filter_by(user_id=user_id)
        
        if status:
            if isinstance(status, str):
                status = TaskStatus(status)
            query = query.filter_by(status=status)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_public_tasks(cls, limit=50):
        """获取公开的任务（用于画廊展示）"""
        return cls.query.filter_by(
            is_public=True,
            status=TaskStatus.completed
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_stats_by_user(cls, user_id):
        """获取用户的任务统计"""
        from sqlalchemy import func
        
        stats = db.session.query(
            cls.type,
            cls.status,
            func.count(cls.id).label('count'),
            func.sum(cls.credits_cost).label('total_credits')
        ).filter_by(user_id=user_id).group_by(cls.type, cls.status).all()
        
        result = {}
        for stat in stats:
            task_type = stat.type.value
            task_status = stat.status.value
            
            if task_type not in result:
                result[task_type] = {}
            
            result[task_type][task_status] = {
                'count': stat.count,
                'credits': stat.total_credits or 0
            }
        
        return result
    
    def __repr__(self):
        return f'<Task {self.id} {self.type.value if self.type else "unknown"} {self.status.value if self.status else "unknown"}>'
